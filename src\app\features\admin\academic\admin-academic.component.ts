import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';

@Component({
  selector: 'app-admin-academic',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <div class="admin-academic">
      <!-- Header -->
      <header class="page-header">
        <div class="header-content">
          <div class="header-left">
            <button class="back-btn" (click)="goBack()">
              <span class="icon">←</span>
              Back to Dashboard
            </button>
            <h1>📚 Academic Management</h1>
            <p>Manage classes, subjects, and academic schedules</p>
          </div>
          <div class="header-actions">
            <button class="add-btn" (click)="showAddModal = true">
              <span class="icon">➕</span>
              Add New {{getAddButtonText()}}
            </button>
          </div>
        </div>
      </header>

      <!-- Academic Tabs -->
      <section class="tabs-section">
        <div class="tabs-content">
          <div class="tab-buttons">
            <button 
              class="tab-btn" 
              [class.active]="activeTab === 'classes'"
              (click)="setActiveTab('classes')">
              🏫 Classes ({{classes.length}})
            </button>
            <button 
              class="tab-btn" 
              [class.active]="activeTab === 'subjects'"
              (click)="setActiveTab('subjects')">
              📖 Subjects ({{subjects.length}})
            </button>
            <button 
              class="tab-btn" 
              [class.active]="activeTab === 'schedules'"
              (click)="setActiveTab('schedules')">
              📅 Schedules ({{schedules.length}})
            </button>
          </div>
        </div>
      </section>

      <!-- Classes Tab -->
      <section class="content-section" *ngIf="activeTab === 'classes'">
        <div class="classes-grid">
          <div class="class-card" *ngFor="let class of classes">
            <div class="class-header">
              <div class="class-info">
                <h3>{{class.name}}</h3>
                <p>{{class.level}} • {{class.students}} students</p>
              </div>
              <div class="class-status" [ngClass]="class.status">
                {{class.status | titlecase}}
              </div>
            </div>
            <div class="class-details">
              <div class="detail-item">
                <span class="label">Teacher:</span>
                <span class="value">{{class.teacher}}</span>
              </div>
              <div class="detail-item">
                <span class="label">Room:</span>
                <span class="value">{{class.room}}</span>
              </div>
              <div class="detail-item">
                <span class="label">Schedule:</span>
                <span class="value">{{class.schedule}}</span>
              </div>
            </div>
            <div class="class-actions">
              <button class="action-btn edit" (click)="editClass(class)">
                <span class="icon">✏️</span>
                Edit
              </button>
              <button class="action-btn view" (click)="viewClass(class)">
                <span class="icon">👁️</span>
                View
              </button>
            </div>
          </div>
        </div>
      </section>

      <!-- Subjects Tab -->
      <section class="content-section" *ngIf="activeTab === 'subjects'">
        <div class="subjects-table">
          <table class="data-table">
            <thead>
              <tr>
                <th>Subject</th>
                <th>Code</th>
                <th>Department</th>
                <th>Credits</th>
                <th>Teachers</th>
                <th>Status</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let subject of subjects">
                <td class="subject-info">
                  <div class="subject-icon">{{subject.icon}}</div>
                  <div class="subject-details">
                    <div class="subject-name">{{subject.name}}</div>
                    <div class="subject-description">{{subject.description}}</div>
                  </div>
                </td>
                <td class="subject-code">{{subject.code}}</td>
                <td>{{subject.department}}</td>
                <td class="credits">{{subject.credits}}</td>
                <td class="teachers-count">{{subject.teachers}} teachers</td>
                <td>
                  <span class="status-badge" [ngClass]="subject.status">
                    {{subject.status | titlecase}}
                  </span>
                </td>
                <td class="actions">
                  <button class="action-btn edit" (click)="editSubject(subject)">
                    <span class="icon">✏️</span>
                  </button>
                  <button class="action-btn delete" (click)="deleteSubject(subject)">
                    <span class="icon">🗑️</span>
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </section>

      <!-- Schedules Tab -->
      <section class="content-section" *ngIf="activeTab === 'schedules'">
        <div class="schedule-view">
          <div class="schedule-header">
            <h3>📅 Weekly Schedule Overview</h3>
            <div class="schedule-controls">
              <select [(ngModel)]="selectedWeek" name="selectedWeek">
                <option value="current">Current Week</option>
                <option value="next">Next Week</option>
                <option value="previous">Previous Week</option>
              </select>
            </div>
          </div>
          
          <div class="schedule-grid">
            <div class="time-column">
              <div class="time-header">Time</div>
              <div class="time-slot" *ngFor="let time of timeSlots">{{time}}</div>
            </div>
            
            <div class="day-column" *ngFor="let day of weekDays">
              <div class="day-header">{{day}}</div>
              <div class="schedule-slot" *ngFor="let time of timeSlots">
                <div class="schedule-item" *ngFor="let item of getScheduleForDayTime(day, time)">
                  <div class="schedule-subject">{{item.subject}}</div>
                  <div class="schedule-class">{{item.class}}</div>
                  <div class="schedule-teacher">{{item.teacher}}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Add Modal -->
      <div class="modal-overlay" *ngIf="showAddModal" (click)="showAddModal = false">
        <div class="modal-content" (click)="$event.stopPropagation()">
          <div class="modal-header">
            <h2>➕ Add New {{getModalTitle()}}</h2>
            <button class="close-btn" (click)="showAddModal = false">✕</button>
          </div>
          
          <!-- Add Class Form -->
          <form class="add-form" *ngIf="activeTab === 'classes'">
            <div class="form-row">
              <div class="form-group">
                <label>Class Name</label>
                <input type="text" [(ngModel)]="newClass.name" name="className">
              </div>
              <div class="form-group">
                <label>Level</label>
                <select [(ngModel)]="newClass.level" name="classLevel">
                  <option value="">Select Level</option>
                  <option value="Grade 1">Grade 1</option>
                  <option value="Grade 2">Grade 2</option>
                  <option value="Grade 3">Grade 3</option>
                  <option value="Grade 4">Grade 4</option>
                  <option value="Grade 5">Grade 5</option>
                </select>
              </div>
            </div>
            <div class="form-row">
              <div class="form-group">
                <label>Teacher</label>
                <select [(ngModel)]="newClass.teacher" name="classTeacher">
                  <option value="">Select Teacher</option>
                  <option value="Sarah Johnson">Sarah Johnson</option>
                  <option value="Michael Chen">Michael Chen</option>
                  <option value="Emily Davis">Emily Davis</option>
                </select>
              </div>
              <div class="form-group">
                <label>Room</label>
                <input type="text" [(ngModel)]="newClass.room" name="classRoom">
              </div>
            </div>
            <div class="modal-actions">
              <button type="button" class="cancel-btn" (click)="showAddModal = false">Cancel</button>
              <button type="button" class="save-btn" (click)="addClass()">
                <span class="icon">💾</span>
                Save Class
              </button>
            </div>
          </form>

          <!-- Add Subject Form -->
          <form class="add-form" *ngIf="activeTab === 'subjects'">
            <div class="form-row">
              <div class="form-group">
                <label>Subject Name</label>
                <input type="text" [(ngModel)]="newSubject.name" name="subjectName">
              </div>
              <div class="form-group">
                <label>Subject Code</label>
                <input type="text" [(ngModel)]="newSubject.code" name="subjectCode">
              </div>
            </div>
            <div class="form-row">
              <div class="form-group">
                <label>Department</label>
                <select [(ngModel)]="newSubject.department" name="subjectDepartment">
                  <option value="">Select Department</option>
                  <option value="Mathematics">Mathematics</option>
                  <option value="Science">Science</option>
                  <option value="Languages">Languages</option>
                  <option value="Arts">Arts</option>
                </select>
              </div>
              <div class="form-group">
                <label>Credits</label>
                <input type="number" [(ngModel)]="newSubject.credits" name="subjectCredits">
              </div>
            </div>
            <div class="modal-actions">
              <button type="button" class="cancel-btn" (click)="showAddModal = false">Cancel</button>
              <button type="button" class="save-btn" (click)="addSubject()">
                <span class="icon">💾</span>
                Save Subject
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .admin-academic {
      min-height: 100vh;
      background: #f5f7fa;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .page-header {
      background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
      color: white;
      padding: 30px;
      margin-bottom: 30px;
    }

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      max-width: 1400px;
      margin: 0 auto;
    }

    .back-btn {
      background: rgba(255, 255, 255, 0.2);
      border: none;
      color: white;
      padding: 8px 16px;
      border-radius: 6px;
      cursor: pointer;
      margin-bottom: 16px;
      transition: all 0.3s ease;
    }

    .back-btn:hover {
      background: rgba(255, 255, 255, 0.3);
    }

    .header-left h1 {
      margin: 0 0 8px 0;
      font-size: 28px;
      font-weight: 600;
    }

    .header-left p {
      margin: 0;
      opacity: 0.9;
      font-size: 16px;
    }

    .add-btn {
      background: #27ae60;
      border: none;
      color: white;
      padding: 12px 24px;
      border-radius: 8px;
      cursor: pointer;
      font-weight: 600;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .add-btn:hover {
      background: #229954;
      transform: translateY(-2px);
    }

    .tabs-section {
      max-width: 1400px;
      margin: 0 auto 30px auto;
      padding: 0 30px;
    }

    .tabs-content {
      background: white;
      border-radius: 12px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
      overflow: hidden;
    }

    .tab-buttons {
      display: flex;
      border-bottom: 1px solid #e1e5e9;
    }

    .tab-btn {
      background: none;
      border: none;
      padding: 16px 24px;
      cursor: pointer;
      font-size: 16px;
      font-weight: 500;
      color: #7f8c8d;
      transition: all 0.3s ease;
      border-bottom: 3px solid transparent;
    }

    .tab-btn.active {
      color: #2c3e50;
      border-bottom-color: #3498db;
      background: #f8f9fa;
    }

    .tab-btn:hover:not(.active) {
      background: #f8f9fa;
    }

    .content-section {
      max-width: 1400px;
      margin: 0 auto;
      padding: 0 30px;
    }

    .classes-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
      gap: 24px;
    }

    .class-card {
      background: white;
      border-radius: 12px;
      padding: 24px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
      transition: transform 0.3s ease;
    }

    .class-card:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .class-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 16px;
    }

    .class-info h3 {
      margin: 0 0 4px 0;
      color: #2c3e50;
      font-size: 18px;
    }

    .class-info p {
      margin: 0;
      color: #7f8c8d;
      font-size: 14px;
    }

    .class-status {
      padding: 4px 12px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 600;
    }

    .class-status.active {
      background: #d4edda;
      color: #155724;
    }

    .class-status.inactive {
      background: #f8d7da;
      color: #721c24;
    }

    .class-details {
      margin-bottom: 20px;
    }

    .detail-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;
    }

    .detail-item .label {
      color: #7f8c8d;
      font-size: 14px;
    }

    .detail-item .value {
      color: #2c3e50;
      font-weight: 500;
      font-size: 14px;
    }

    .class-actions {
      display: flex;
      gap: 12px;
    }

    .action-btn {
      background: #f8f9fa;
      border: 1px solid #e1e5e9;
      padding: 8px 16px;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      gap: 6px;
    }

    .action-btn.edit:hover {
      background: #e3f2fd;
      border-color: #2196f3;
    }

    .action-btn.view:hover {
      background: #f3e5f5;
      border-color: #9c27b0;
    }

    .subjects-table {
      background: white;
      border-radius: 12px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
      overflow: hidden;
    }

    .data-table {
      width: 100%;
      border-collapse: collapse;
    }

    .data-table th {
      background: #f8f9fa;
      padding: 16px;
      text-align: left;
      font-weight: 600;
      color: #2c3e50;
      border-bottom: 1px solid #e1e5e9;
    }

    .data-table td {
      padding: 16px;
      border-bottom: 1px solid #f1f3f4;
    }

    .data-table tr:hover {
      background: #f8f9fa;
    }

    .subject-info {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .subject-icon {
      width: 40px;
      height: 40px;
      border-radius: 8px;
      background: #e9ecef;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 18px;
    }

    .subject-name {
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 2px;
    }

    .subject-description {
      font-size: 12px;
      color: #7f8c8d;
    }

    .status-badge {
      padding: 4px 12px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 600;
    }

    .status-badge.active {
      background: #d4edda;
      color: #155724;
    }

    .status-badge.inactive {
      background: #f8d7da;
      color: #721c24;
    }

    .schedule-view {
      background: white;
      border-radius: 12px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
      padding: 24px;
    }

    .schedule-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;
    }

    .schedule-header h3 {
      margin: 0;
      color: #2c3e50;
    }

    .schedule-controls select {
      padding: 8px 12px;
      border: 2px solid #e1e5e9;
      border-radius: 6px;
      font-size: 14px;
    }

    .schedule-grid {
      display: grid;
      grid-template-columns: 100px repeat(5, 1fr);
      gap: 1px;
      background: #e1e5e9;
      border-radius: 8px;
      overflow: hidden;
    }

    .time-column,
    .day-column {
      background: white;
    }

    .time-header,
    .day-header {
      background: #f8f9fa;
      padding: 12px;
      font-weight: 600;
      text-align: center;
      color: #2c3e50;
    }

    .time-slot,
    .schedule-slot {
      padding: 8px;
      min-height: 60px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .schedule-item {
      background: #e3f2fd;
      border-radius: 4px;
      padding: 4px 8px;
      width: 100%;
      text-align: center;
    }

    .schedule-subject {
      font-weight: 600;
      font-size: 12px;
      color: #1976d2;
    }

    .schedule-class,
    .schedule-teacher {
      font-size: 10px;
      color: #666;
    }

    .modal-overlay {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1000;
    }

    .modal-content {
      background: white;
      border-radius: 12px;
      width: 90%;
      max-width: 600px;
      max-height: 90vh;
      overflow-y: auto;
    }

    .modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 24px;
      border-bottom: 1px solid #e1e5e9;
    }

    .modal-header h2 {
      margin: 0;
      color: #2c3e50;
    }

    .close-btn {
      background: none;
      border: none;
      font-size: 20px;
      cursor: pointer;
      color: #7f8c8d;
    }

    .add-form {
      padding: 24px;
    }

    .form-row {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 16px;
      margin-bottom: 16px;
    }

    .form-group {
      display: flex;
      flex-direction: column;
    }

    .form-group label {
      margin-bottom: 8px;
      font-weight: 500;
      color: #2c3e50;
    }

    .form-group input,
    .form-group select {
      padding: 12px;
      border: 2px solid #e1e5e9;
      border-radius: 6px;
      font-size: 14px;
    }

    .form-group input:focus,
    .form-group select:focus {
      outline: none;
      border-color: #3498db;
    }

    .modal-actions {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
      margin-top: 24px;
      padding-top: 24px;
      border-top: 1px solid #e1e5e9;
    }

    .cancel-btn {
      background: #6c757d;
      border: none;
      color: white;
      padding: 12px 24px;
      border-radius: 6px;
      cursor: pointer;
    }

    .save-btn {
      background: #27ae60;
      border: none;
      color: white;
      padding: 12px 24px;
      border-radius: 6px;
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    @media (max-width: 768px) {
      .header-content {
        flex-direction: column;
        gap: 20px;
        text-align: center;
      }

      .tab-buttons {
        flex-direction: column;
      }

      .classes-grid {
        grid-template-columns: 1fr;
      }

      .form-row {
        grid-template-columns: 1fr;
      }

      .schedule-grid {
        grid-template-columns: 80px repeat(5, 1fr);
        font-size: 12px;
      }
    }
  `]
})
export class AdminAcademicComponent {
  activeTab = 'classes';
  showAddModal = false;
  selectedWeek = 'current';

  newClass = {
    name: '',
    level: '',
    teacher: '',
    room: ''
  };

  newSubject = {
    name: '',
    code: '',
    department: '',
    credits: 0
  };

  classes = [
    {
      name: 'Mathematics 5A',
      level: 'Grade 5',
      students: 28,
      teacher: 'Sarah Johnson',
      room: 'Room 101',
      schedule: 'Mon, Wed, Fri 9:00 AM',
      status: 'active'
    },
    {
      name: 'Science 4B',
      level: 'Grade 4',
      students: 25,
      teacher: 'Michael Chen',
      room: 'Lab 201',
      schedule: 'Tue, Thu 10:30 AM',
      status: 'active'
    },
    {
      name: 'English 3A',
      level: 'Grade 3',
      students: 22,
      teacher: 'Emily Davis',
      room: 'Room 105',
      schedule: 'Daily 8:00 AM',
      status: 'inactive'
    }
  ];

  subjects = [
    {
      name: 'Mathematics',
      code: 'MATH101',
      department: 'Mathematics',
      credits: 4,
      teachers: 3,
      status: 'active',
      icon: '🔢',
      description: 'Basic arithmetic and algebra'
    },
    {
      name: 'Science',
      code: 'SCI101',
      department: 'Science',
      credits: 3,
      teachers: 2,
      status: 'active',
      icon: '🔬',
      description: 'General science concepts'
    },
    {
      name: 'English',
      code: 'ENG101',
      department: 'Languages',
      credits: 3,
      teachers: 4,
      status: 'active',
      icon: '📚',
      description: 'Language arts and literature'
    }
  ];

  schedules = [
    { day: 'Monday', time: '9:00 AM', subject: 'Math', class: '5A', teacher: 'Sarah J.' },
    { day: 'Monday', time: '10:30 AM', subject: 'Science', class: '4B', teacher: 'Michael C.' },
    { day: 'Tuesday', time: '8:00 AM', subject: 'English', class: '3A', teacher: 'Emily D.' }
  ];

  weekDays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];
  timeSlots = ['8:00 AM', '9:00 AM', '10:30 AM', '12:00 PM', '1:30 PM', '3:00 PM'];

  constructor(private router: Router) {}

  goBack(): void {
    this.router.navigate(['/admin']);
  }

  setActiveTab(tab: string): void {
    this.activeTab = tab;
  }

  getAddButtonText(): string {
    const texts = {
      classes: 'Class',
      subjects: 'Subject',
      schedules: 'Schedule'
    };
    return texts[this.activeTab as keyof typeof texts] || 'Item';
  }

  getModalTitle(): string {
    const titles = {
      classes: 'Class',
      subjects: 'Subject',
      schedules: 'Schedule'
    };
    return titles[this.activeTab as keyof typeof titles] || 'Item';
  }

  editClass(classItem: any): void {
    console.log('Edit class:', classItem);
  }

  viewClass(classItem: any): void {
    console.log('View class:', classItem);
  }

  editSubject(subject: any): void {
    console.log('Edit subject:', subject);
  }

  deleteSubject(subject: any): void {
    if (confirm(`Are you sure you want to delete ${subject.name}?`)) {
      this.subjects = this.subjects.filter(s => s.code !== subject.code);
    }
  }

  addClass(): void {
    if (this.newClass.name && this.newClass.level && this.newClass.teacher && this.newClass.room) {
      const newClass = {
        name: this.newClass.name,
        level: this.newClass.level,
        students: 0,
        teacher: this.newClass.teacher,
        room: this.newClass.room,
        schedule: 'TBD',
        status: 'active'
      };

      this.classes.push(newClass);
      this.showAddModal = false;
      this.resetNewClass();
    }
  }

  addSubject(): void {
    if (this.newSubject.name && this.newSubject.code && this.newSubject.department) {
      const newSubject = {
        name: this.newSubject.name,
        code: this.newSubject.code,
        department: this.newSubject.department,
        credits: this.newSubject.credits,
        teachers: 0,
        status: 'active',
        icon: '📖',
        description: 'New subject'
      };

      this.subjects.push(newSubject);
      this.showAddModal = false;
      this.resetNewSubject();
    }
  }

  getScheduleForDayTime(day: string, time: string): any[] {
    return this.schedules.filter(s => s.day === day && s.time === time);
  }

  private resetNewClass(): void {
    this.newClass = {
      name: '',
      level: '',
      teacher: '',
      room: ''
    };
  }

  private resetNewSubject(): void {
    this.newSubject = {
      name: '',
      code: '',
      department: '',
      credits: 0
    };
  }
}
