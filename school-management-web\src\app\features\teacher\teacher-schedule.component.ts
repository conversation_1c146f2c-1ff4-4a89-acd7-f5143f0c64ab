import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'app-teacher-schedule',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <div class="teacher-schedule">
      <!-- Header -->
      <header class="page-header">
        <div class="header-content">
          <div class="header-left">
            <h1>📅 My Schedule</h1>
            <p>View and manage your teaching schedule and appointments</p>
          </div>
          <div class="header-actions">
            <button class="action-btn" (click)="showAddModal = true">
              <span class="icon">➕</span>
              Add Event
            </button>
            <button class="action-btn secondary" (click)="exportSchedule()">
              <span class="icon">📊</span>
              Export
            </button>
          </div>
        </div>
      </header>

      <!-- Week Navigation -->
      <section class="week-navigation">
        <div class="nav-controls">
          <button class="nav-btn" (click)="previousWeek()">
            <span class="icon">←</span>
            Previous
          </button>
          <div class="week-info">
            <h2>{{getCurrentWeekRange()}}</h2>
            <p>Week {{getWeekNumber()}}</p>
          </div>
          <button class="nav-btn" (click)="nextWeek()">
            Next
            <span class="icon">→</span>
          </button>
        </div>
      </section>

      <!-- Schedule Grid -->
      <section class="schedule-section">
        <div class="schedule-container">
          <div class="schedule-grid">
            <!-- Time Column -->
            <div class="time-column">
              <div class="time-header">Time</div>
              <div class="time-slot" *ngFor="let time of timeSlots">{{time}}</div>
            </div>

            <!-- Day Columns -->
            <div class="day-column" *ngFor="let day of weekDays">
              <div class="day-header">
                <div class="day-name">{{day.name}}</div>
                <div class="day-date">{{day.date | date:'MMM d'}}</div>
              </div>
              <div class="schedule-slot" *ngFor="let time of timeSlots">
                <div class="schedule-event" 
                     *ngFor="let event of getEventsForDayTime(day.date, time)"
                     [ngClass]="event.type"
                     (click)="viewEvent(event)">
                  <div class="event-title">{{event.title}}</div>
                  <div class="event-details">{{event.details}}</div>
                  <div class="event-room" *ngIf="event.room">{{event.room}}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Today's Events -->
      <section class="today-events">
        <h2>📋 Today's Events</h2>
        <div class="events-list">
          <div class="event-card" *ngFor="let event of getTodayEvents()">
            <div class="event-time">{{event.startTime}} - {{event.endTime}}</div>
            <div class="event-content">
              <h4>{{event.title}}</h4>
              <p>{{event.details}}</p>
              <div class="event-meta">
                <span *ngIf="event.room">📍 {{event.room}}</span>
                <span *ngIf="event.students">👥 {{event.students}} students</span>
              </div>
            </div>
            <div class="event-status" [ngClass]="event.status">{{event.status}}</div>
          </div>
        </div>
      </section>

      <!-- Add Event Modal -->
      <div class="modal-overlay" *ngIf="showAddModal" (click)="showAddModal = false">
        <div class="modal-content" (click)="$event.stopPropagation()">
          <div class="modal-header">
            <h2>➕ Add New Event</h2>
            <button class="close-btn" (click)="showAddModal = false">✕</button>
          </div>
          <form class="event-form">
            <div class="form-group">
              <label>Event Title</label>
              <input type="text" [(ngModel)]="newEvent.title" name="title" placeholder="Enter event title">
            </div>
            <div class="form-row">
              <div class="form-group">
                <label>Type</label>
                <select [(ngModel)]="newEvent.type" name="type">
                  <option value="">Select Type</option>
                  <option value="class">Class</option>
                  <option value="meeting">Meeting</option>
                  <option value="appointment">Appointment</option>
                  <option value="break">Break</option>
                </select>
              </div>
              <div class="form-group">
                <label>Room</label>
                <input type="text" [(ngModel)]="newEvent.room" name="room" placeholder="Room number">
              </div>
            </div>
            <div class="form-row">
              <div class="form-group">
                <label>Date</label>
                <input type="date" [(ngModel)]="newEvent.date" name="date">
              </div>
              <div class="form-group">
                <label>Start Time</label>
                <input type="time" [(ngModel)]="newEvent.startTime" name="startTime">
              </div>
            </div>
            <div class="form-group">
              <label>End Time</label>
              <input type="time" [(ngModel)]="newEvent.endTime" name="endTime">
            </div>
            <div class="form-group">
              <label>Details</label>
              <textarea [(ngModel)]="newEvent.details" name="details" rows="3" placeholder="Event details..."></textarea>
            </div>
            <div class="modal-actions">
              <button type="button" class="cancel-btn" (click)="showAddModal = false">Cancel</button>
              <button type="button" class="save-btn" (click)="addEvent()">
                <span class="icon">💾</span>
                Save Event
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .teacher-schedule {
      padding: 0;
      background: #f5f7fa;
      min-height: 100vh;
    }

    .page-header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 30px;
      margin: -30px -30px 30px -30px;
    }

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      max-width: 1400px;
      margin: 0 auto;
    }

    .header-left h1 {
      margin: 0 0 8px 0;
      font-size: 28px;
      font-weight: 600;
    }

    .header-left p {
      margin: 0;
      opacity: 0.9;
      font-size: 16px;
    }

    .header-actions {
      display: flex;
      gap: 12px;
    }

    .action-btn {
      background: rgba(255, 255, 255, 0.2);
      border: none;
      color: white;
      padding: 12px 20px;
      border-radius: 8px;
      cursor: pointer;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 8px;
      transition: all 0.3s ease;
    }

    .action-btn:hover {
      background: rgba(255, 255, 255, 0.3);
    }

    .action-btn.secondary {
      background: transparent;
      border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .week-navigation {
      margin-bottom: 30px;
    }

    .nav-controls {
      display: flex;
      justify-content: space-between;
      align-items: center;
      background: white;
      padding: 20px 24px;
      border-radius: 12px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .nav-btn {
      background: #667eea;
      border: none;
      color: white;
      padding: 10px 16px;
      border-radius: 8px;
      cursor: pointer;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .week-info {
      text-align: center;
    }

    .week-info h2 {
      margin: 0 0 4px 0;
      color: #2d3748;
      font-size: 20px;
    }

    .week-info p {
      margin: 0;
      color: #718096;
      font-size: 14px;
    }

    .schedule-section {
      margin-bottom: 40px;
    }

    .schedule-container {
      background: white;
      border-radius: 16px;
      padding: 24px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
      overflow-x: auto;
    }

    .schedule-grid {
      display: grid;
      grid-template-columns: 100px repeat(5, 1fr);
      gap: 1px;
      background: #e2e8f0;
      border-radius: 8px;
      overflow: hidden;
      min-width: 800px;
    }

    .time-column,
    .day-column {
      background: white;
    }

    .time-header,
    .day-header {
      background: #f8fafc;
      padding: 16px 12px;
      font-weight: 600;
      text-align: center;
      color: #2d3748;
    }

    .day-name {
      font-size: 14px;
      margin-bottom: 2px;
    }

    .day-date {
      font-size: 12px;
      color: #718096;
    }

    .time-slot,
    .schedule-slot {
      padding: 8px;
      min-height: 80px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-bottom: 1px solid #f1f3f4;
    }

    .time-slot {
      font-size: 12px;
      color: #718096;
      font-weight: 500;
    }

    .schedule-event {
      background: #e3f2fd;
      border-radius: 6px;
      padding: 8px;
      width: 100%;
      cursor: pointer;
      transition: all 0.3s ease;
      border-left: 4px solid #2196f3;
    }

    .schedule-event:hover {
      transform: scale(1.02);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .schedule-event.class {
      background: #e3f2fd;
      border-left-color: #2196f3;
    }

    .schedule-event.meeting {
      background: #f3e5f5;
      border-left-color: #9c27b0;
    }

    .schedule-event.appointment {
      background: #e8f5e8;
      border-left-color: #4caf50;
    }

    .schedule-event.break {
      background: #fff3e0;
      border-left-color: #ff9800;
    }

    .event-title {
      font-weight: 600;
      font-size: 12px;
      color: #2d3748;
      margin-bottom: 2px;
    }

    .event-details {
      font-size: 10px;
      color: #718096;
      margin-bottom: 2px;
    }

    .event-room {
      font-size: 10px;
      color: #667eea;
      font-weight: 500;
    }

    .today-events {
      background: white;
      border-radius: 16px;
      padding: 24px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
      margin-bottom: 40px;
    }

    .today-events h2 {
      margin: 0 0 20px 0;
      color: #2d3748;
      font-size: 20px;
    }

    .events-list {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    .event-card {
      display: flex;
      align-items: center;
      gap: 16px;
      padding: 16px;
      background: #f8fafc;
      border-radius: 12px;
      border-left: 4px solid #667eea;
    }

    .event-time {
      font-weight: 600;
      color: #667eea;
      font-size: 14px;
      min-width: 120px;
    }

    .event-content {
      flex: 1;
    }

    .event-content h4 {
      margin: 0 0 4px 0;
      color: #2d3748;
      font-size: 16px;
    }

    .event-content p {
      margin: 0 0 8px 0;
      color: #718096;
      font-size: 14px;
    }

    .event-meta {
      display: flex;
      gap: 16px;
      font-size: 12px;
      color: #718096;
    }

    .event-status {
      padding: 4px 12px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 600;
    }

    .event-status.upcoming {
      background: #e3f2fd;
      color: #1976d2;
    }

    .event-status.current {
      background: #e8f5e8;
      color: #388e3c;
    }

    .event-status.completed {
      background: #f5f5f5;
      color: #757575;
    }

    /* Modal Styles */
    .modal-overlay {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1000;
    }

    .modal-content {
      background: white;
      border-radius: 12px;
      width: 90%;
      max-width: 600px;
      max-height: 90vh;
      overflow-y: auto;
    }

    .modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 24px;
      border-bottom: 1px solid #e2e8f0;
    }

    .modal-header h2 {
      margin: 0;
      color: #2d3748;
    }

    .close-btn {
      background: none;
      border: none;
      font-size: 20px;
      cursor: pointer;
      color: #718096;
    }

    .event-form {
      padding: 24px;
    }

    .form-row {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 16px;
      margin-bottom: 16px;
    }

    .form-group {
      display: flex;
      flex-direction: column;
      margin-bottom: 16px;
    }

    .form-group label {
      margin-bottom: 8px;
      font-weight: 500;
      color: #2d3748;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
      padding: 12px;
      border: 2px solid #e2e8f0;
      border-radius: 6px;
      font-size: 14px;
    }

    .modal-actions {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
      margin-top: 24px;
      padding-top: 24px;
      border-top: 1px solid #e2e8f0;
    }

    .cancel-btn {
      background: #6c757d;
      border: none;
      color: white;
      padding: 12px 24px;
      border-radius: 6px;
      cursor: pointer;
    }

    .save-btn {
      background: #667eea;
      border: none;
      color: white;
      padding: 12px 24px;
      border-radius: 6px;
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    @media (max-width: 768px) {
      .header-content {
        flex-direction: column;
        gap: 20px;
        text-align: center;
      }

      .nav-controls {
        flex-direction: column;
        gap: 16px;
      }

      .schedule-grid {
        grid-template-columns: 80px repeat(5, 1fr);
        font-size: 12px;
      }

      .form-row {
        grid-template-columns: 1fr;
      }

      .event-card {
        flex-direction: column;
        align-items: flex-start;
      }
    }
  `]
})
export class TeacherScheduleComponent {
  showAddModal = false;
  currentWeekStart = new Date();

  newEvent = {
    title: '',
    type: '',
    room: '',
    date: '',
    startTime: '',
    endTime: '',
    details: ''
  };

  timeSlots = [
    '08:00', '09:00', '10:00', '11:00', '12:00', 
    '13:00', '14:00', '15:00', '16:00', '17:00'
  ];

  weekDays = this.getWeekDays();

  events = [
    {
      id: 1,
      title: 'Mathematics 5A',
      details: 'Algebra lesson',
      type: 'class',
      room: 'Room 101',
      date: new Date(),
      startTime: '09:00',
      endTime: '09:50',
      students: 28,
      status: 'upcoming'
    },
    {
      id: 2,
      title: 'Parent Meeting',
      details: 'John Smith\'s parents',
      type: 'meeting',
      room: 'Office',
      date: new Date(),
      startTime: '15:00',
      endTime: '15:30',
      status: 'upcoming'
    }
  ];

  constructor() {
    this.setCurrentWeek();
  }

  setCurrentWeek(): void {
    const today = new Date();
    const dayOfWeek = today.getDay();
    const mondayOffset = dayOfWeek === 0 ? -6 : 1 - dayOfWeek;
    this.currentWeekStart = new Date(today);
    this.currentWeekStart.setDate(today.getDate() + mondayOffset);
    this.weekDays = this.getWeekDays();
  }

  getWeekDays() {
    const days = [];
    const dayNames = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];
    
    for (let i = 0; i < 5; i++) {
      const date = new Date(this.currentWeekStart);
      date.setDate(this.currentWeekStart.getDate() + i);
      days.push({
        name: dayNames[i],
        date: date
      });
    }
    return days;
  }

  getCurrentWeekRange(): string {
    const endDate = new Date(this.currentWeekStart);
    endDate.setDate(this.currentWeekStart.getDate() + 4);
    
    const startStr = this.currentWeekStart.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
    const endStr = endDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
    
    return `${startStr} - ${endStr}`;
  }

  getWeekNumber(): number {
    const onejan = new Date(this.currentWeekStart.getFullYear(), 0, 1);
    const millisecsInDay = 86400000;
    return Math.ceil((((this.currentWeekStart.getTime() - onejan.getTime()) / millisecsInDay) + onejan.getDay() + 1) / 7);
  }

  previousWeek(): void {
    this.currentWeekStart.setDate(this.currentWeekStart.getDate() - 7);
    this.weekDays = this.getWeekDays();
  }

  nextWeek(): void {
    this.currentWeekStart.setDate(this.currentWeekStart.getDate() + 7);
    this.weekDays = this.getWeekDays();
  }

  getEventsForDayTime(date: Date, time: string) {
    return this.events.filter(event => {
      const eventDate = new Date(event.date);
      return eventDate.toDateString() === date.toDateString() && 
             event.startTime === time;
    });
  }

  getTodayEvents() {
    const today = new Date();
    return this.events.filter(event => {
      const eventDate = new Date(event.date);
      return eventDate.toDateString() === today.toDateString();
    });
  }

  viewEvent(event: any): void {
    console.log('View event:', event);
  }

  addEvent(): void {
    if (this.newEvent.title && this.newEvent.date && this.newEvent.startTime) {
      const newEvent = {
        id: this.events.length + 1,
        title: this.newEvent.title,
        details: this.newEvent.details,
        type: this.newEvent.type,
        room: this.newEvent.room,
        date: new Date(this.newEvent.date),
        startTime: this.newEvent.startTime,
        endTime: this.newEvent.endTime,
        status: 'upcoming'
      };
      this.events.push(newEvent);
      this.showAddModal = false;
      this.resetNewEvent();
    }
  }

  resetNewEvent(): void {
    this.newEvent = {
      title: '',
      type: '',
      room: '',
      date: '',
      startTime: '',
      endTime: '',
      details: ''
    };
  }

  exportSchedule(): void {
    console.log('Export schedule');
  }
}
