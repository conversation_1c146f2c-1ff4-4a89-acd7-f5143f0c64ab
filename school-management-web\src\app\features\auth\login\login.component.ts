import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <div class="login-container">
      <div class="login-card">
        <div class="login-header">
          <div class="school-logo">
            <div class="logo-icon">🎓</div>
            <h1>School Management System</h1>
            <p>Welcome back! Please sign in to your account</p>
          </div>
        </div>
        
        <form class="login-form" (ngSubmit)="onLogin()">
          <div class="form-group">
            <label for="username">Username or Email</label>
            <input 
              type="text" 
              id="username" 
              [(ngModel)]="username" 
              name="username"
              placeholder="Enter your username or email"
              required>
          </div>
          
          <div class="form-group">
            <label for="password">Password</label>
            <input 
              type="password" 
              id="password" 
              [(ngModel)]="password" 
              name="password"
              placeholder="Enter your password"
              required>
          </div>
          
          <div class="form-group">
            <label for="role">Login as</label>
            <select id="role" [(ngModel)]="selectedRole" name="role" required>
              <option value="">Select your role</option>
              <option value="admin">Administrator</option>
              <option value="teacher">Teacher</option>
              <option value="parent">Parent</option>
            </select>
          </div>
          
          <div class="form-options">
            <label class="checkbox-container">
              <input type="checkbox" [(ngModel)]="rememberMe" name="rememberMe">
              <span class="checkmark"></span>
              Remember me
            </label>
            <a href="#" class="forgot-password">Forgot password?</a>
          </div>
          
          <button type="submit" class="login-btn" [disabled]="!isFormValid()">
            <span class="btn-icon">🔐</span>
            Sign In
          </button>
        </form>
        
        <div class="login-footer">
          <p>Don't have an account? <a href="#">Contact administrator</a></p>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .login-container {
      min-height: 100vh;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20px;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }
    
    .login-card {
      background: white;
      border-radius: 16px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      overflow: hidden;
      width: 100%;
      max-width: 400px;
      animation: slideUp 0.6s ease-out;
    }
    
    @keyframes slideUp {
      from {
        opacity: 0;
        transform: translateY(30px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }
    
    .login-header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 40px 30px;
      text-align: center;
    }
    
    .logo-icon {
      font-size: 48px;
      margin-bottom: 16px;
    }
    
    .login-header h1 {
      margin: 0 0 8px 0;
      font-size: 24px;
      font-weight: 600;
    }
    
    .login-header p {
      margin: 0;
      opacity: 0.9;
      font-size: 14px;
    }
    
    .login-form {
      padding: 40px 30px;
    }
    
    .form-group {
      margin-bottom: 24px;
    }
    
    .form-group label {
      display: block;
      margin-bottom: 8px;
      font-weight: 500;
      color: #333;
      font-size: 14px;
    }
    
    .form-group input,
    .form-group select {
      width: 100%;
      padding: 12px 16px;
      border: 2px solid #e1e5e9;
      border-radius: 8px;
      font-size: 14px;
      transition: all 0.3s ease;
      box-sizing: border-box;
    }
    
    .form-group input:focus,
    .form-group select:focus {
      outline: none;
      border-color: #667eea;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }
    
    .form-options {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 32px;
    }
    
    .checkbox-container {
      display: flex;
      align-items: center;
      font-size: 14px;
      color: #666;
      cursor: pointer;
    }
    
    .checkbox-container input {
      margin-right: 8px;
      width: auto;
    }
    
    .forgot-password {
      color: #667eea;
      text-decoration: none;
      font-size: 14px;
      font-weight: 500;
    }
    
    .forgot-password:hover {
      text-decoration: underline;
    }
    
    .login-btn {
      width: 100%;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border: none;
      padding: 14px 24px;
      border-radius: 8px;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
    }
    
    .login-btn:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    }
    
    .login-btn:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none;
    }
    
    .btn-icon {
      font-size: 18px;
    }
    
    .login-footer {
      padding: 20px 30px;
      text-align: center;
      background: #f8f9fa;
      border-top: 1px solid #e1e5e9;
    }
    
    .login-footer p {
      margin: 0;
      font-size: 14px;
      color: #666;
    }
    
    .login-footer a {
      color: #667eea;
      text-decoration: none;
      font-weight: 500;
    }
    
    .login-footer a:hover {
      text-decoration: underline;
    }
  `]
})
export class LoginComponent {
  username = '';
  password = '';
  selectedRole = '';
  rememberMe = false;

  constructor(private router: Router) {}

  isFormValid(): boolean {
    return this.username.length > 0 && 
           this.password.length > 0 && 
           this.selectedRole.length > 0;
  }

  onLogin(): void {
    if (this.isFormValid()) {
      // Simulate login success and navigate based on role
      console.log('Login attempt:', {
        username: this.username,
        role: this.selectedRole,
        rememberMe: this.rememberMe
      });

      // Navigate to appropriate dashboard based on role
      switch (this.selectedRole) {
        case 'admin':
          this.router.navigate(['/admin']);
          break;
        case 'teacher':
          this.router.navigate(['/teacher']);
          break;
        case 'parent':
          this.router.navigate(['/parent']);
          break;
        default:
          this.router.navigate(['/parent']); // Default to parent
      }
    }
  }
}
