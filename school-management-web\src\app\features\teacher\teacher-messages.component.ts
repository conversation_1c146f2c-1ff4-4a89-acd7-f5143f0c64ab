import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'app-teacher-messages',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <div class="teacher-messages">
      <!-- Header -->
      <header class="page-header">
        <div class="header-content">
          <div class="header-left">
            <h1>💬 Messages</h1>
            <p>Communicate with parents, students, and colleagues</p>
          </div>
          <div class="header-actions">
            <button class="action-btn" (click)="showComposeModal = true">
              <span class="icon">✏️</span>
              Compose
            </button>
          </div>
        </div>
      </header>

      <!-- Messages Layout -->
      <div class="messages-layout">
        <!-- Conversations List -->
        <div class="conversations-panel">
          <div class="conversations-header">
            <h3>Conversations</h3>
            <div class="search-box">
              <input type="text" [(ngModel)]="searchTerm" placeholder="Search conversations..." class="search-input">
            </div>
          </div>
          <div class="conversations-list">
            <div class="conversation-item" 
                 *ngFor="let conversation of getFilteredConversations()"
                 [class.active]="selectedConversation?.id === conversation.id"
                 (click)="selectConversation(conversation)">
              <div class="conversation-avatar">{{conversation.name.charAt(0)}}</div>
              <div class="conversation-content">
                <div class="conversation-header">
                  <span class="conversation-name">{{conversation.name}}</span>
                  <span class="conversation-time">{{conversation.lastMessage.time | date:'shortTime'}}</span>
                </div>
                <div class="conversation-preview">{{conversation.lastMessage.text}}</div>
                <div class="conversation-meta">
                  <span class="conversation-type">{{conversation.type}}</span>
                  <span class="unread-count" *ngIf="conversation.unreadCount > 0">{{conversation.unreadCount}}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Chat Panel -->
        <div class="chat-panel" *ngIf="selectedConversation">
          <div class="chat-header">
            <div class="chat-contact">
              <div class="contact-avatar">{{selectedConversation.name.charAt(0)}}</div>
              <div class="contact-info">
                <h4>{{selectedConversation.name}}</h4>
                <p>{{selectedConversation.type}} • {{selectedConversation.status}}</p>
              </div>
            </div>
            <div class="chat-actions">
              <button class="chat-action-btn" (click)="callContact()">
                <span class="icon">📞</span>
              </button>
              <button class="chat-action-btn" (click)="videoCall()">
                <span class="icon">📹</span>
              </button>
            </div>
          </div>

          <div class="messages-container" #messagesContainer>
            <div class="message" 
                 *ngFor="let message of selectedConversation.messages"
                 [class.sent]="message.sender === 'me'"
                 [class.received]="message.sender !== 'me'">
              <div class="message-content">
                <div class="message-text">{{message.text}}</div>
                <div class="message-time">{{message.time | date:'shortTime'}}</div>
              </div>
            </div>
          </div>

          <div class="message-input-container">
            <div class="message-input-box">
              <input type="text" 
                     [(ngModel)]="newMessage" 
                     (keyup.enter)="sendMessage()"
                     placeholder="Type a message..."
                     class="message-input">
              <button class="send-btn" (click)="sendMessage()" [disabled]="!newMessage.trim()">
                <span class="icon">📤</span>
              </button>
            </div>
          </div>
        </div>

        <!-- Empty State -->
        <div class="empty-chat" *ngIf="!selectedConversation">
          <div class="empty-icon">💬</div>
          <h3>Select a conversation</h3>
          <p>Choose a conversation from the list to start messaging</p>
        </div>
      </div>

      <!-- Compose Modal -->
      <div class="modal-overlay" *ngIf="showComposeModal" (click)="showComposeModal = false">
        <div class="modal-content" (click)="$event.stopPropagation()">
          <div class="modal-header">
            <h2>✏️ Compose Message</h2>
            <button class="close-btn" (click)="showComposeModal = false">✕</button>
          </div>
          <form class="compose-form">
            <div class="form-group">
              <label>To</label>
              <select [(ngModel)]="newCompose.recipient" name="recipient">
                <option value="">Select recipient</option>
                <option *ngFor="let contact of getAllContacts()" [value]="contact.id">
                  {{contact.name}} ({{contact.type}})
                </option>
              </select>
            </div>
            <div class="form-group">
              <label>Subject</label>
              <input type="text" [(ngModel)]="newCompose.subject" name="subject" placeholder="Message subject">
            </div>
            <div class="form-group">
              <label>Message</label>
              <textarea [(ngModel)]="newCompose.message" name="message" rows="6" placeholder="Type your message..."></textarea>
            </div>
            <div class="modal-actions">
              <button type="button" class="cancel-btn" (click)="showComposeModal = false">Cancel</button>
              <button type="button" class="send-btn-modal" (click)="sendNewMessage()">
                <span class="icon">📤</span>
                Send Message
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .teacher-messages {
      padding: 0;
      background: #f5f7fa;
      min-height: 100vh;
    }

    .page-header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 30px;
      margin: -30px -30px 30px -30px;
    }

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      max-width: 1400px;
      margin: 0 auto;
    }

    .header-left h1 {
      margin: 0 0 8px 0;
      font-size: 28px;
      font-weight: 600;
    }

    .header-left p {
      margin: 0;
      opacity: 0.9;
      font-size: 16px;
    }

    .action-btn {
      background: rgba(255, 255, 255, 0.2);
      border: none;
      color: white;
      padding: 12px 20px;
      border-radius: 8px;
      cursor: pointer;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 8px;
      transition: all 0.3s ease;
    }

    .action-btn:hover {
      background: rgba(255, 255, 255, 0.3);
    }

    .messages-layout {
      display: grid;
      grid-template-columns: 350px 1fr;
      gap: 20px;
      height: calc(100vh - 200px);
    }

    .conversations-panel {
      background: white;
      border-radius: 16px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
      display: flex;
      flex-direction: column;
    }

    .conversations-header {
      padding: 20px;
      border-bottom: 1px solid #e2e8f0;
    }

    .conversations-header h3 {
      margin: 0 0 16px 0;
      color: #2d3748;
    }

    .search-input {
      width: 100%;
      padding: 10px 12px;
      border: 2px solid #e2e8f0;
      border-radius: 8px;
      font-size: 14px;
    }

    .conversations-list {
      flex: 1;
      overflow-y: auto;
    }

    .conversation-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 16px 20px;
      cursor: pointer;
      transition: background 0.3s ease;
      border-bottom: 1px solid #f7fafc;
    }

    .conversation-item:hover {
      background: #f8fafc;
    }

    .conversation-item.active {
      background: #e3f2fd;
      border-right: 3px solid #2196f3;
    }

    .conversation-avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: #667eea;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      flex-shrink: 0;
    }

    .conversation-content {
      flex: 1;
      min-width: 0;
    }

    .conversation-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 4px;
    }

    .conversation-name {
      font-weight: 600;
      color: #2d3748;
      font-size: 14px;
    }

    .conversation-time {
      font-size: 12px;
      color: #718096;
    }

    .conversation-preview {
      font-size: 13px;
      color: #718096;
      margin-bottom: 4px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .conversation-meta {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .conversation-type {
      font-size: 11px;
      color: #667eea;
      font-weight: 500;
    }

    .unread-count {
      background: #ef4444;
      color: white;
      border-radius: 50%;
      width: 18px;
      height: 18px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 10px;
      font-weight: 600;
    }

    .chat-panel {
      background: white;
      border-radius: 16px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
      display: flex;
      flex-direction: column;
    }

    .chat-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px;
      border-bottom: 1px solid #e2e8f0;
    }

    .chat-contact {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .contact-avatar {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      background: #667eea;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      font-size: 18px;
    }

    .contact-info h4 {
      margin: 0 0 2px 0;
      color: #2d3748;
    }

    .contact-info p {
      margin: 0;
      color: #718096;
      font-size: 12px;
    }

    .chat-actions {
      display: flex;
      gap: 8px;
    }

    .chat-action-btn {
      background: #f8fafc;
      border: 1px solid #e2e8f0;
      padding: 8px;
      border-radius: 8px;
      cursor: pointer;
      font-size: 16px;
    }

    .messages-container {
      flex: 1;
      padding: 20px;
      overflow-y: auto;
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    .message {
      display: flex;
    }

    .message.sent {
      justify-content: flex-end;
    }

    .message.received {
      justify-content: flex-start;
    }

    .message-content {
      max-width: 70%;
      padding: 12px 16px;
      border-radius: 16px;
      position: relative;
    }

    .message.sent .message-content {
      background: #667eea;
      color: white;
    }

    .message.received .message-content {
      background: #f8fafc;
      color: #2d3748;
    }

    .message-text {
      margin-bottom: 4px;
      line-height: 1.4;
    }

    .message-time {
      font-size: 11px;
      opacity: 0.7;
    }

    .message-input-container {
      padding: 20px;
      border-top: 1px solid #e2e8f0;
    }

    .message-input-box {
      display: flex;
      gap: 12px;
      align-items: center;
    }

    .message-input {
      flex: 1;
      padding: 12px 16px;
      border: 2px solid #e2e8f0;
      border-radius: 24px;
      font-size: 14px;
    }

    .send-btn {
      background: #667eea;
      border: none;
      color: white;
      padding: 12px;
      border-radius: 50%;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 44px;
      height: 44px;
    }

    .send-btn:disabled {
      background: #cbd5e0;
      cursor: not-allowed;
    }

    .empty-chat {
      background: white;
      border-radius: 16px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      text-align: center;
      padding: 60px 40px;
    }

    .empty-icon {
      font-size: 64px;
      margin-bottom: 20px;
      opacity: 0.5;
    }

    .empty-chat h3 {
      margin: 0 0 8px 0;
      color: #2d3748;
    }

    .empty-chat p {
      margin: 0;
      color: #718096;
    }

    /* Modal Styles */
    .modal-overlay {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1000;
    }

    .modal-content {
      background: white;
      border-radius: 12px;
      width: 90%;
      max-width: 600px;
      max-height: 90vh;
      overflow-y: auto;
    }

    .modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 24px;
      border-bottom: 1px solid #e2e8f0;
    }

    .modal-header h2 {
      margin: 0;
      color: #2d3748;
    }

    .close-btn {
      background: none;
      border: none;
      font-size: 20px;
      cursor: pointer;
      color: #718096;
    }

    .compose-form {
      padding: 24px;
    }

    .form-group {
      display: flex;
      flex-direction: column;
      margin-bottom: 16px;
    }

    .form-group label {
      margin-bottom: 8px;
      font-weight: 500;
      color: #2d3748;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
      padding: 12px;
      border: 2px solid #e2e8f0;
      border-radius: 6px;
      font-size: 14px;
    }

    .modal-actions {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
      margin-top: 24px;
      padding-top: 24px;
      border-top: 1px solid #e2e8f0;
    }

    .cancel-btn {
      background: #6c757d;
      border: none;
      color: white;
      padding: 12px 24px;
      border-radius: 6px;
      cursor: pointer;
    }

    .send-btn-modal {
      background: #667eea;
      border: none;
      color: white;
      padding: 12px 24px;
      border-radius: 6px;
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    @media (max-width: 768px) {
      .header-content {
        flex-direction: column;
        gap: 20px;
        text-align: center;
      }

      .messages-layout {
        grid-template-columns: 1fr;
        height: auto;
      }

      .conversations-panel {
        order: 2;
        max-height: 300px;
      }

      .chat-panel,
      .empty-chat {
        order: 1;
        min-height: 400px;
      }
    }
  `]
})
export class TeacherMessagesComponent {
  searchTerm = '';
  selectedConversation: any = null;
  newMessage = '';
  showComposeModal = false;

  newCompose = {
    recipient: '',
    subject: '',
    message: ''
  };

  conversations = [
    {
      id: 1,
      name: 'Robert Smith',
      type: 'Parent',
      status: 'Online',
      lastMessage: { text: 'Thank you for the update on John\'s progress', time: new Date(), sender: 'them' },
      unreadCount: 0,
      messages: [
        { text: 'Hello, I wanted to discuss John\'s recent math performance', time: new Date(Date.now() - 3600000), sender: 'them' },
        { text: 'Of course! John has been doing well overall. Let me share some details.', time: new Date(Date.now() - 3500000), sender: 'me' },
        { text: 'Thank you for the update on John\'s progress', time: new Date(), sender: 'them' }
      ]
    },
    {
      id: 2,
      name: 'Lisa Johnson',
      type: 'Parent',
      status: 'Offline',
      lastMessage: { text: 'When is the next parent-teacher conference?', time: new Date(Date.now() - 7200000), sender: 'them' },
      unreadCount: 2,
      messages: [
        { text: 'When is the next parent-teacher conference?', time: new Date(Date.now() - 7200000), sender: 'them' }
      ]
    },
    {
      id: 3,
      name: 'Principal Davis',
      type: 'Staff',
      status: 'Online',
      lastMessage: { text: 'Please submit your quarterly reports by Friday', time: new Date(Date.now() - 86400000), sender: 'them' },
      unreadCount: 0,
      messages: [
        { text: 'Please submit your quarterly reports by Friday', time: new Date(Date.now() - 86400000), sender: 'them' },
        { text: 'Will do! I\'ll have them ready by Thursday.', time: new Date(Date.now() - 86300000), sender: 'me' }
      ]
    }
  ];

  getFilteredConversations() {
    if (!this.searchTerm) return this.conversations;
    return this.conversations.filter(conv => 
      conv.name.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
      conv.type.toLowerCase().includes(this.searchTerm.toLowerCase())
    );
  }

  selectConversation(conversation: any): void {
    this.selectedConversation = conversation;
    conversation.unreadCount = 0;
  }

  sendMessage(): void {
    if (this.newMessage.trim() && this.selectedConversation) {
      const message = {
        text: this.newMessage.trim(),
        time: new Date(),
        sender: 'me'
      };
      this.selectedConversation.messages.push(message);
      this.selectedConversation.lastMessage = message;
      this.newMessage = '';
    }
  }

  getAllContacts() {
    return [
      { id: 1, name: 'Robert Smith', type: 'Parent' },
      { id: 2, name: 'Lisa Johnson', type: 'Parent' },
      { id: 3, name: 'Principal Davis', type: 'Staff' },
      { id: 4, name: 'Michael Chen', type: 'Teacher' }
    ];
  }

  sendNewMessage(): void {
    if (this.newCompose.recipient && this.newCompose.message) {
      console.log('Sending new message:', this.newCompose);
      this.showComposeModal = false;
      this.newCompose = { recipient: '', subject: '', message: '' };
    }
  }

  callContact(): void {
    console.log('Calling:', this.selectedConversation?.name);
  }

  videoCall(): void {
    console.log('Video calling:', this.selectedConversation?.name);
  }
}
