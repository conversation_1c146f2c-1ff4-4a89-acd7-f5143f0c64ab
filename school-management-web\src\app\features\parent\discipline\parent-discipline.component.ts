import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';

interface DisciplineRecord {
  id: number;
  title: string;
  description: string;
  teacher: string;
  date: Date;
  severity: 'minor' | 'moderate' | 'major';
  type: 'warning' | 'detention' | 'suspension' | 'counseling';
  childName: string;
  resolved: boolean;
  followUpRequired: boolean;
}

@Component({
  selector: 'app-parent-discipline',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="discipline-container">
      <!-- Header -->
      <div class="discipline-header">
        <div class="header-content">
          <h1>⚠️ Discipline Records</h1>
          <p>View discipline records and behavior reports for your children</p>
        </div>
        <div class="header-stats">
          <div class="stat-card total">
            <div class="stat-number">{{getTotalRecords()}}</div>
            <div class="stat-label">Total Records</div>
          </div>
          <div class="stat-card resolved">
            <div class="stat-number">{{getResolvedCount()}}</div>
            <div class="stat-label">Resolved</div>
          </div>
          <div class="stat-card pending">
            <div class="stat-number">{{getPendingCount()}}</div>
            <div class="stat-label">Pending</div>
          </div>
        </div>
      </div>

      <!-- Records List -->
      <div class="records-list">
        <div *ngFor="let record of disciplineRecords" class="record-card" [class]="record.severity">
          <div class="record-header">
            <div class="record-info">
              <div class="record-title">{{record.title}}</div>
              <div class="record-meta">
                <span class="record-teacher">👨‍🏫 {{record.teacher}}</span>
                <span class="record-child">👤 {{record.childName}}</span>
                <span class="record-type">{{getTypeIcon(record.type)}} {{record.type}}</span>
              </div>
            </div>
            <div class="record-badges">
              <div class="severity-badge" [class]="record.severity">
                {{getSeverityIcon(record.severity)}} {{record.severity}}
              </div>
              <div class="status-badge" [class]="record.resolved ? 'resolved' : 'pending'">
                {{record.resolved ? '✅ Resolved' : '⏳ Pending'}}
              </div>
            </div>
          </div>

          <div class="record-description">{{record.description}}</div>

          <div class="record-footer">
            <div class="record-date">📅 {{record.date | date:'MMM dd, yyyy'}}</div>
            <div class="record-actions">
              <button class="action-btn" (click)="viewDetails(record)">View Details</button>
              <button *ngIf="record.followUpRequired" class="action-btn warning" (click)="scheduleFollowUp(record)">
                Schedule Follow-up
              </button>
              <button class="action-btn secondary" (click)="contactTeacher(record)">Contact Teacher</button>
            </div>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div *ngIf="disciplineRecords.length === 0" class="empty-state">
        <div class="empty-icon">✅</div>
        <h3>No discipline records</h3>
        <p>Great news! There are no discipline records for your children.</p>
      </div>
    </div>
  `,
  styles: [`
    .discipline-container {
      min-height: 100vh;
      background: #f5f7fa;
      padding: 20px;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .discipline-header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 30px;
      border-radius: 12px;
      margin-bottom: 30px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;
      gap: 20px;
    }

    .header-content h1 {
      margin: 0 0 8px 0;
      font-size: 28px;
      font-weight: 600;
    }

    .header-content p {
      margin: 0;
      opacity: 0.9;
      font-size: 16px;
    }

    .header-stats {
      display: flex;
      gap: 20px;
    }

    .stat-card {
      background: rgba(255, 255, 255, 0.2);
      padding: 16px;
      border-radius: 8px;
      text-align: center;
      min-width: 80px;
    }

    .stat-number {
      font-size: 24px;
      font-weight: 600;
      margin-bottom: 4px;
    }

    .stat-label {
      font-size: 12px;
      opacity: 0.9;
    }

    .records-list {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    .record-card {
      background: white;
      padding: 20px;
      border-radius: 12px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
      border-left: 4px solid #ffc107;
      transition: all 0.3s ease;
    }

    .record-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .record-card.moderate {
      border-left-color: #fd7e14;
    }

    .record-card.major {
      border-left-color: #dc3545;
    }
  `]
})
export class ParentDisciplineComponent implements OnInit {
  disciplineRecords: DisciplineRecord[] = [];

  ngOnInit(): void {
    this.loadDisciplineRecords();
  }

  loadDisciplineRecords(): void {
    this.disciplineRecords = [
      {
        id: 1,
        title: 'Disruption in Class',
        description: 'Michael was talking during the lesson and disrupting other students. He was given a verbal warning and asked to move seats.',
        teacher: 'Ms. Emily Davis',
        date: new Date('2024-01-10'),
        severity: 'minor',
        type: 'warning',
        childName: 'Michael Johnson',
        resolved: true,
        followUpRequired: false
      },
      {
        id: 2,
        title: 'Late Assignment Submission',
        description: 'Sarah submitted her math homework 3 days late without prior notice. This is the second occurrence this month.',
        teacher: 'Dr. Sarah Wilson',
        date: new Date('2024-01-08'),
        severity: 'minor',
        type: 'warning',
        childName: 'Sarah Johnson',
        resolved: false,
        followUpRequired: true
      }
    ];
  }

  getTotalRecords(): number {
    return this.disciplineRecords.length;
  }

  getResolvedCount(): number {
    return this.disciplineRecords.filter(r => r.resolved).length;
  }

  getPendingCount(): number {
    return this.disciplineRecords.filter(r => !r.resolved).length;
  }

  getSeverityIcon(severity: string): string {
    switch (severity) {
      case 'minor': return '🟡';
      case 'moderate': return '🟠';
      case 'major': return '🔴';
      default: return '⚪';
    }
  }

  getTypeIcon(type: string): string {
    switch (type) {
      case 'warning': return '⚠️';
      case 'detention': return '🏫';
      case 'suspension': return '🚫';
      case 'counseling': return '🗣️';
      default: return '📝';
    }
  }

  viewDetails(record: DisciplineRecord): void {
    console.log('View details:', record);
  }

  scheduleFollowUp(record: DisciplineRecord): void {
    console.log('Schedule follow-up:', record);
  }

  contactTeacher(record: DisciplineRecord): void {
    console.log('Contact teacher:', record);
  }
}
