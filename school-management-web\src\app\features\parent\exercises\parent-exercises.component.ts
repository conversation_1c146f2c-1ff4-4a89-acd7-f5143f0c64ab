import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

interface Exercise {
  id: number;
  title: string;
  subject: string;
  teacher: string;
  dueDate: Date;
  assignedDate: Date;
  status: 'pending' | 'submitted' | 'graded' | 'overdue';
  grade?: number;
  maxGrade: number;
  description: string;
  childName: string;
  difficulty: 'easy' | 'medium' | 'hard';
  estimatedTime: string;
  submissionFile?: string;
}

@Component({
  selector: 'app-parent-exercises',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <div class="exercises-container">
      <!-- Header -->
      <div class="exercises-header">
        <div class="header-content">
          <h1>📝 Exercises & Assignments</h1>
          <p>Monitor your children's homework and assignment progress</p>
        </div>
        <div class="header-stats">
          <div class="stat-card">
            <div class="stat-number">{{getPendingCount()}}</div>
            <div class="stat-label">Pending</div>
          </div>
          <div class="stat-card">
            <div class="stat-number">{{getSubmittedCount()}}</div>
            <div class="stat-label">Submitted</div>
          </div>
          <div class="stat-card">
            <div class="stat-number">{{getGradedCount()}}</div>
            <div class="stat-label">Graded</div>
          </div>
        </div>
      </div>

      <!-- Filters -->
      <div class="filters-section">
        <div class="filter-tabs">
          <button class="filter-tab" [class.active]="activeFilter === 'all'" (click)="setFilter('all')">
            All Exercises
          </button>
          <button class="filter-tab" [class.active]="activeFilter === 'pending'" (click)="setFilter('pending')">
            Pending
          </button>
          <button class="filter-tab" [class.active]="activeFilter === 'submitted'" (click)="setFilter('submitted')">
            Submitted
          </button>
          <button class="filter-tab" [class.active]="activeFilter === 'graded'" (click)="setFilter('graded')">
            Graded
          </button>
          <button class="filter-tab" [class.active]="activeFilter === 'overdue'" (click)="setFilter('overdue')">
            Overdue
          </button>
        </div>
        <div class="filter-controls">
          <select class="child-selector" [(ngModel)]="selectedChild" (change)="filterExercises()">
            <option value="">All Children</option>
            <option value="Sarah Johnson">Sarah Johnson</option>
            <option value="Michael Johnson">Michael Johnson</option>
          </select>
          <select class="subject-selector" [(ngModel)]="selectedSubject" (change)="filterExercises()">
            <option value="">All Subjects</option>
            <option value="Mathematics">Mathematics</option>
            <option value="English">English</option>
            <option value="Science">Science</option>
            <option value="History">History</option>
            <option value="Art">Art</option>
          </select>
        </div>
      </div>

      <!-- Exercises List -->
      <div class="exercises-list">
        <div *ngFor="let exercise of filteredExercises" class="exercise-card" [class]="getExerciseClass(exercise)">
          <div class="exercise-header">
            <div class="exercise-title">{{exercise.title}}</div>
            <div class="exercise-status" [class]="exercise.status">{{getStatusText(exercise.status)}}</div>
          </div>

          <div class="exercise-meta">
            <span class="exercise-subject">📚 {{exercise.subject}}</span>
            <span class="exercise-teacher">👨‍🏫 {{exercise.teacher}}</span>
            <span class="exercise-child">👤 {{exercise.childName}}</span>
            <span class="exercise-difficulty" [class]="exercise.difficulty">{{getDifficultyIcon(exercise.difficulty)}} {{exercise.difficulty}}</span>
          </div>

          <div class="exercise-description">{{exercise.description}}</div>

          <div class="exercise-details">
            <div class="detail-item">
              <span class="detail-label">Assigned:</span>
              <span class="detail-value">{{exercise.assignedDate | date:'MMM dd, yyyy'}}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">Due:</span>
              <span class="detail-value" [class]="getDueDateClass(exercise)">{{exercise.dueDate | date:'MMM dd, yyyy HH:mm'}}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">Estimated Time:</span>
              <span class="detail-value">{{exercise.estimatedTime}}</span>
            </div>
            <div *ngIf="exercise.grade !== undefined" class="detail-item">
              <span class="detail-label">Grade:</span>
              <span class="detail-value grade">{{exercise.grade}}/{{exercise.maxGrade}}</span>
            </div>
          </div>

          <div class="exercise-actions">
            <button *ngIf="exercise.status === 'pending'" class="action-btn primary" (click)="viewExercise(exercise)">
              📖 View Details
            </button>
            <button *ngIf="exercise.status === 'submitted'" class="action-btn secondary" (click)="viewSubmission(exercise)">
              👁️ View Submission
            </button>
            <button *ngIf="exercise.status === 'graded'" class="action-btn success" (click)="viewGrade(exercise)">
              📊 View Grade
            </button>
            <button class="action-btn info" (click)="contactTeacher(exercise)">
              💬 Contact Teacher
            </button>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div *ngIf="filteredExercises.length === 0" class="empty-state">
        <div class="empty-icon">📝</div>
        <h3>No exercises found</h3>
        <p>Try adjusting your filters to see more exercises.</p>
      </div>
    </div>
  `,
  styles: [`
    .exercises-container {
      min-height: 100vh;
      background: #f5f7fa;
      padding: 20px;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .exercises-header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 30px;
      border-radius: 12px;
      margin-bottom: 30px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;
      gap: 20px;
    }

    .header-content h1 {
      margin: 0 0 8px 0;
      font-size: 28px;
      font-weight: 600;
    }

    .header-content p {
      margin: 0;
      opacity: 0.9;
      font-size: 16px;
    }

    .header-stats {
      display: flex;
      gap: 20px;
    }

    .stat-card {
      background: rgba(255, 255, 255, 0.2);
      padding: 16px;
      border-radius: 8px;
      text-align: center;
      min-width: 80px;
    }

    .stat-number {
      font-size: 24px;
      font-weight: 600;
      margin-bottom: 4px;
    }

    .stat-label {
      font-size: 12px;
      opacity: 0.9;
    }

    .filters-section {
      background: white;
      padding: 20px;
      border-radius: 12px;
      margin-bottom: 20px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    }

    .filter-tabs {
      display: flex;
      gap: 8px;
      margin-bottom: 16px;
      flex-wrap: wrap;
    }

    .filter-tab {
      padding: 8px 16px;
      border: 2px solid #e1e5e9;
      background: white;
      border-radius: 20px;
      cursor: pointer;
      transition: all 0.3s ease;
      font-size: 14px;
      font-weight: 500;
    }

    .filter-tab.active {
      background: #667eea;
      color: white;
      border-color: #667eea;
    }

    .filter-controls {
      display: flex;
      gap: 12px;
      flex-wrap: wrap;
    }

    .child-selector, .subject-selector {
      padding: 8px 12px;
      border: 2px solid #e1e5e9;
      border-radius: 8px;
      background: white;
    }

    .exercises-list {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    .exercise-card {
      background: white;
      padding: 20px;
      border-radius: 12px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
      border-left: 4px solid #2196f3;
      transition: all 0.3s ease;
    }

    .exercise-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .exercise-card.overdue {
      border-left-color: #f44336;
    }

    .exercise-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
    }

    .exercise-title {
      font-size: 18px;
      font-weight: 600;
      color: #333;
    }

    .exercise-status {
      padding: 4px 12px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 500;
    }

    .exercise-status.pending {
      background: #fff3cd;
      color: #856404;
    }

    .exercise-status.submitted {
      background: #d1ecf1;
      color: #0c5460;
    }

    .exercise-status.graded {
      background: #d4edda;
      color: #155724;
    }

    .exercise-status.overdue {
      background: #f8d7da;
      color: #721c24;
    }

    .exercise-meta {
      display: flex;
      gap: 16px;
      margin-bottom: 12px;
      flex-wrap: wrap;
      font-size: 14px;
      color: #666;
    }

    .exercise-description {
      color: #666;
      margin-bottom: 16px;
      line-height: 1.5;
    }

    .exercise-details {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 12px;
      margin-bottom: 16px;
    }

    .detail-item {
      display: flex;
      justify-content: space-between;
      padding: 8px 0;
      border-bottom: 1px solid #f0f0f0;
    }

    .detail-label {
      font-weight: 500;
      color: #666;
    }

    .detail-value {
      color: #333;
    }

    .detail-value.grade {
      font-weight: 600;
      color: #28a745;
    }

    .exercise-actions {
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
    }

    .action-btn {
      padding: 8px 16px;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      transition: all 0.3s ease;
    }

    .action-btn.primary {
      background: #667eea;
      color: white;
    }

    .action-btn.secondary {
      background: #6c757d;
      color: white;
    }

    .action-btn.success {
      background: #28a745;
      color: white;
    }

    .action-btn.info {
      background: #17a2b8;
      color: white;
    }

    .empty-state {
      text-align: center;
      padding: 60px 20px;
      color: #666;
    }

    .empty-icon {
      font-size: 64px;
      margin-bottom: 16px;
    }

    @media (max-width: 768px) {
      .exercises-header {
        flex-direction: column;
        align-items: stretch;
      }

      .header-stats {
        justify-content: center;
      }

      .exercise-details {
        grid-template-columns: 1fr;
      }
    }
  `]
})
export class ParentExercisesComponent implements OnInit {
  exercises: Exercise[] = [];
  filteredExercises: Exercise[] = [];
  activeFilter = 'all';
  selectedChild = '';
  selectedSubject = '';

  ngOnInit(): void {
    this.loadExercises();
    this.filteredExercises = this.exercises;
  }

  loadExercises(): void {
    this.exercises = [
      {
        id: 1,
        title: 'Algebra Practice Problems',
        subject: 'Mathematics',
        teacher: 'Dr. Sarah Wilson',
        dueDate: new Date('2024-01-20T23:59:00'),
        assignedDate: new Date('2024-01-15T09:00:00'),
        status: 'pending',
        maxGrade: 100,
        description: 'Complete exercises 1-20 from Chapter 5. Show all work and explain your reasoning.',
        childName: 'Sarah Johnson',
        difficulty: 'medium',
        estimatedTime: '2 hours'
      },
      {
        id: 2,
        title: 'Book Report: Charlotte\'s Web',
        subject: 'English',
        teacher: 'Ms. Emily Davis',
        dueDate: new Date('2024-01-18T23:59:00'),
        assignedDate: new Date('2024-01-10T09:00:00'),
        status: 'submitted',
        maxGrade: 100,
        description: 'Write a 500-word report analyzing the main themes and characters.',
        childName: 'Sarah Johnson',
        difficulty: 'easy',
        estimatedTime: '3 hours'
      },
      {
        id: 3,
        title: 'Science Lab Report',
        subject: 'Science',
        teacher: 'Mr. John Smith',
        dueDate: new Date('2024-01-16T23:59:00'),
        assignedDate: new Date('2024-01-12T09:00:00'),
        status: 'graded',
        grade: 85,
        maxGrade: 100,
        description: 'Document your observations from the plant growth experiment.',
        childName: 'Michael Johnson',
        difficulty: 'hard',
        estimatedTime: '1.5 hours'
      },
      {
        id: 4,
        title: 'Art Project: Self Portrait',
        subject: 'Art',
        teacher: 'Ms. Lisa Chen',
        dueDate: new Date('2024-01-12T23:59:00'),
        assignedDate: new Date('2024-01-08T09:00:00'),
        status: 'overdue',
        maxGrade: 100,
        description: 'Create a self-portrait using watercolors. Focus on color mixing techniques.',
        childName: 'Michael Johnson',
        difficulty: 'medium',
        estimatedTime: '2.5 hours'
      }
    ];
  }

  setFilter(filter: string): void {
    this.activeFilter = filter;
    this.filterExercises();
  }

  filterExercises(): void {
    let filtered = this.exercises;

    // Apply status filter
    if (this.activeFilter !== 'all') {
      filtered = filtered.filter(exercise => exercise.status === this.activeFilter);
    }

    // Apply child filter
    if (this.selectedChild) {
      filtered = filtered.filter(exercise => exercise.childName === this.selectedChild);
    }

    // Apply subject filter
    if (this.selectedSubject) {
      filtered = filtered.filter(exercise => exercise.subject === this.selectedSubject);
    }

    this.filteredExercises = filtered;
  }

  getPendingCount(): number {
    return this.exercises.filter(e => e.status === 'pending').length;
  }

  getSubmittedCount(): number {
    return this.exercises.filter(e => e.status === 'submitted').length;
  }

  getGradedCount(): number {
    return this.exercises.filter(e => e.status === 'graded').length;
  }

  getExerciseClass(exercise: Exercise): string {
    return exercise.status;
  }

  getStatusText(status: string): string {
    switch (status) {
      case 'pending': return 'Pending';
      case 'submitted': return 'Submitted';
      case 'graded': return 'Graded';
      case 'overdue': return 'Overdue';
      default: return status;
    }
  }

  getDifficultyIcon(difficulty: string): string {
    switch (difficulty) {
      case 'easy': return '🟢';
      case 'medium': return '🟡';
      case 'hard': return '🔴';
      default: return '⚪';
    }
  }

  getDueDateClass(exercise: Exercise): string {
    const now = new Date();
    const dueDate = new Date(exercise.dueDate);
    const timeDiff = dueDate.getTime() - now.getTime();
    const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24));

    if (daysDiff < 0) return 'overdue';
    if (daysDiff <= 1) return 'urgent';
    return '';
  }

  viewExercise(exercise: Exercise): void {
    console.log('View exercise:', exercise);
  }

  viewSubmission(exercise: Exercise): void {
    console.log('View submission:', exercise);
  }

  viewGrade(exercise: Exercise): void {
    console.log('View grade:', exercise);
  }

  contactTeacher(exercise: Exercise): void {
    console.log('Contact teacher:', exercise);
  }
}
