1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.AmanaSchool"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:4:5-67
15-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:4:22-64
16    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
16-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:5:5-80
16-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:5:22-77
17    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
17-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:6:5-81
17-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:6:22-78
18    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
18-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:7:5-82
18-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:7:22-79
19    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
19-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:8:5-83
19-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:8:22-80
20    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
20-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:9:5-80
20-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:9:22-78
21    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
21-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:10:5-76
21-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:10:22-74
22    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
22-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:11:5-76
22-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:11:22-74
23    <uses-permission android:name="android.permission.VIBRATE" />
23-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:12:5-65
23-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:12:22-63
24    <uses-permission android:name="android.permission.WAKE_LOCK" />
24-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:13:5-68
24-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:13:22-65
25    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
25-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:14:5-82
25-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:14:22-79
26
27    <queries>
27-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:51:5-56:15
28        <intent>
28-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:52:9-55:18
29            <action android:name="android.intent.action.PROCESS_TEXT" />
29-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:53:13-72
29-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:53:21-70
30
31            <data android:mimeType="text/plain" />
31-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:42:17-89
32        </intent>
33        <intent>
33-->[:file_picker] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-12:18
34            <action android:name="android.intent.action.GET_CONTENT" />
34-->[:file_picker] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-72
34-->[:file_picker] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:9:21-69
35
36            <data android:mimeType="*/*" />
36-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:42:17-89
37        </intent>
38        <intent>
38-->[net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-3\81deae67eaa10a6c4f1748ffaac2c185\transformed\jetified-appauth-0.11.1\AndroidManifest.xml:25:9-31:18
39            <action android:name="android.intent.action.VIEW" />
39-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:39:17-68
39-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:39:25-66
40
41            <category android:name="android.intent.category.BROWSABLE" />
41-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:41:17-77
41-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:41:27-75
42
43            <data android:scheme="https" />
43-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:42:17-89
43-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:42:23-62
44        </intent>
45    </queries>
46
47    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
47-->[:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:9:5-79
47-->[:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:9:22-76
48    <uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT" />
48-->[:flutter_local_notifications] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:9:5-81
48-->[:flutter_local_notifications] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:9:22-78
49    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
49-->[:flutter_local_notifications] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:10:5-79
49-->[:flutter_local_notifications] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:10:22-76
50
51    <permission
51-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\e39b5a0dabebdb51348de79427edb1cf\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
52        android:name="com.example.AmanaSchool.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
52-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\e39b5a0dabebdb51348de79427edb1cf\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
53        android:protectionLevel="signature" />
53-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\e39b5a0dabebdb51348de79427edb1cf\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
54
55    <uses-permission android:name="com.example.AmanaSchool.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
55-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\e39b5a0dabebdb51348de79427edb1cf\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
55-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\e39b5a0dabebdb51348de79427edb1cf\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
56
57    <application
57-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:15:5-49:19
58        android:name="androidx.multidex.MultiDexApplication"
58-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:17:9-61
59        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
59-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\e39b5a0dabebdb51348de79427edb1cf\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
60        android:debuggable="true"
61        android:enableOnBackInvokedCallback="true"
61-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:19:9-51
62        android:extractNativeLibs="true"
63        android:icon="@mipmap/demo"
63-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:18:9-36
64        android:label="@string/app_name"
64-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:16:9-41
65        android:requestLegacyExternalStorage="true" >
65-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:20:9-52
66        <activity
66-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:21:9-44:20
67            android:name="com.example.NovaSchool.MainActivity"
67-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:22:13-41
68            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
68-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:26:13-163
69            android:exported="true"
69-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:23:13-36
70            android:hardwareAccelerated="true"
70-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:27:13-47
71            android:launchMode="singleTop"
71-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:24:13-43
72            android:theme="@style/LaunchTheme"
72-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:25:13-47
73            android:windowSoftInputMode="adjustResize" >
73-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:28:13-55
74            <meta-data
74-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:30:13-33:19
75                android:name="io.flutter.embedding.android.NormalTheme"
75-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:31:17-72
76                android:resource="@style/NormalTheme" />
76-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:32:17-54
77
78            <intent-filter>
78-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:34:13-37:29
79                <action android:name="android.intent.action.MAIN" />
79-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:35:17-68
79-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:35:25-66
80
81                <category android:name="android.intent.category.LAUNCHER" />
81-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:36:17-76
81-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:36:27-74
82            </intent-filter>
83            <intent-filter>
83-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:38:13-43:29
84                <action android:name="android.intent.action.VIEW" />
84-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:39:17-68
84-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:39:25-66
85
86                <category android:name="android.intent.category.DEFAULT" />
86-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:40:17-75
86-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:40:27-73
87                <category android:name="android.intent.category.BROWSABLE" />
87-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:41:17-77
87-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:41:27-75
88
89                <data
89-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:42:17-89
90                    android:host="callback"
90-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:42:63-86
91                    android:scheme="com.example.NovaSchool" />
91-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:42:23-62
92            </intent-filter>
93        </activity>
94
95        <meta-data
95-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:46:9-48:33
96            android:name="flutterEmbedding"
96-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:47:13-44
97            android:value="2" />
97-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:48:13-30
98
99        <service
99-->[:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:14:9-17:72
100            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService"
100-->[:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:15:13-107
101            android:exported="false"
101-->[:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:16:13-37
102            android:permission="android.permission.BIND_JOB_SERVICE" />
102-->[:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:17:13-69
103        <service
103-->[:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:18:9-24:19
104            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService"
104-->[:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:19:13-97
105            android:exported="false" >
105-->[:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:20:13-37
106            <intent-filter>
106-->[:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:21:13-23:29
107                <action android:name="com.google.firebase.MESSAGING_EVENT" />
107-->[:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:22:17-78
107-->[:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:22:25-75
108            </intent-filter>
109        </service>
110
111        <receiver
111-->[:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:26:9-33:20
112            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver"
112-->[:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:27:13-98
113            android:exported="true"
113-->[:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:28:13-36
114            android:permission="com.google.android.c2dm.permission.SEND" >
114-->[:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:29:13-73
115            <intent-filter>
115-->[:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:30:13-32:29
116                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
116-->[:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:31:17-81
116-->[:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:31:25-78
117            </intent-filter>
118        </receiver>
119
120        <service
120-->[:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:35:9-39:19
121            android:name="com.google.firebase.components.ComponentDiscoveryService"
121-->[:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:35:18-89
122            android:directBootAware="true"
122-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2947ac83f2922b287048df084e924bb9\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
123            android:exported="false" >
123-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\f292f42d5744c12ab1fd61186974f62c\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:56:13-37
124            <meta-data
124-->[:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:36:13-38:85
125                android:name="com.google.firebase.components:io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar"
125-->[:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:37:17-128
126                android:value="com.google.firebase.components.ComponentRegistrar" />
126-->[:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:38:17-82
127            <meta-data
127-->[:firebase_core] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-11:85
128                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
128-->[:firebase_core] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:10:17-124
129                android:value="com.google.firebase.components.ComponentRegistrar" />
129-->[:firebase_core] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:11:17-82
130            <meta-data
130-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\f292f42d5744c12ab1fd61186974f62c\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:57:13-59:85
131                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
131-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\f292f42d5744c12ab1fd61186974f62c\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:58:17-122
132                android:value="com.google.firebase.components.ComponentRegistrar" />
132-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\f292f42d5744c12ab1fd61186974f62c\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:59:17-82
133            <meta-data
133-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\f292f42d5744c12ab1fd61186974f62c\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:60:13-62:85
134                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
134-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\f292f42d5744c12ab1fd61186974f62c\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:61:17-119
135                android:value="com.google.firebase.components.ComponentRegistrar" />
135-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\f292f42d5744c12ab1fd61186974f62c\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:62:17-82
136            <meta-data
136-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\82516a683c88bdb58e80ce62bbcb4d72\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
137                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
137-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\82516a683c88bdb58e80ce62bbcb4d72\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
138                android:value="com.google.firebase.components.ComponentRegistrar" />
138-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\82516a683c88bdb58e80ce62bbcb4d72\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
139            <meta-data
139-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\82516a683c88bdb58e80ce62bbcb4d72\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
140                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
140-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\82516a683c88bdb58e80ce62bbcb4d72\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
141                android:value="com.google.firebase.components.ComponentRegistrar" />
141-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\82516a683c88bdb58e80ce62bbcb4d72\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
142            <meta-data
142-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\23fd8345975bbfa8be7dbb31c2ca57b3\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
143                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
143-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\23fd8345975bbfa8be7dbb31c2ca57b3\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
144                android:value="com.google.firebase.components.ComponentRegistrar" />
144-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\23fd8345975bbfa8be7dbb31c2ca57b3\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
145            <meta-data
145-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2947ac83f2922b287048df084e924bb9\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
146                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
146-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2947ac83f2922b287048df084e924bb9\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
147                android:value="com.google.firebase.components.ComponentRegistrar" />
147-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2947ac83f2922b287048df084e924bb9\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
148            <meta-data
148-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\cb2c47f95a981749e29da656b646519f\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
149                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
149-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\cb2c47f95a981749e29da656b646519f\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
150                android:value="com.google.firebase.components.ComponentRegistrar" />
150-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\cb2c47f95a981749e29da656b646519f\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
151        </service>
152
153        <provider
153-->[:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:41:9-45:38
154            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider"
154-->[:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:42:13-102
155            android:authorities="com.example.AmanaSchool.flutterfirebasemessaginginitprovider"
155-->[:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:43:13-88
156            android:exported="false"
156-->[:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:44:13-37
157            android:initOrder="99" />
157-->[:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:45:13-35
158
159        <receiver
159-->[:flutter_local_notifications] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:14:9-16:40
160            android:name="com.dexterous.flutterlocalnotifications.ActionBroadcastReceiver"
160-->[:flutter_local_notifications] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:15:13-91
161            android:exported="false" />
161-->[:flutter_local_notifications] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:16:13-37
162        <receiver
162-->[:flutter_local_notifications] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:17:9-19:40
163            android:name="com.dexterous.flutterlocalnotifications.ScheduledNotificationReceiver"
163-->[:flutter_local_notifications] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:18:13-97
164            android:exported="false" />
164-->[:flutter_local_notifications] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:19:13-37
165        <receiver
165-->[:flutter_local_notifications] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:20:9-29:20
166            android:name="com.dexterous.flutterlocalnotifications.ScheduledNotificationBootReceiver"
166-->[:flutter_local_notifications] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:21:13-101
167            android:exported="false" >
167-->[:flutter_local_notifications] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:22:13-37
168            <intent-filter>
168-->[:flutter_local_notifications] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:23:13-28:29
169                <action android:name="android.intent.action.BOOT_COMPLETED" />
169-->[:flutter_local_notifications] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:24:17-79
169-->[:flutter_local_notifications] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:24:25-76
170                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
170-->[:flutter_local_notifications] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:25:17-84
170-->[:flutter_local_notifications] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:25:25-81
171                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
171-->[:flutter_local_notifications] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:26:17-82
171-->[:flutter_local_notifications] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:26:25-79
172                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
172-->[:flutter_local_notifications] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:27:17-82
172-->[:flutter_local_notifications] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:27:25-79
173            </intent-filter>
174        </receiver>
175
176        <provider
176-->[:open_file_android] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:9:9-19:20
177            android:name="com.crazecoder.openfile.FileProvider"
177-->[:open_file_android] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:10:13-64
178            android:authorities="com.example.AmanaSchool.fileProvider.com.crazecoder.openfile"
178-->[:open_file_android] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-88
179            android:exported="false"
179-->[:open_file_android] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:12:13-37
180            android:grantUriPermissions="true"
180-->[:open_file_android] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-47
181            android:requestLegacyExternalStorage="true" >
181-->[:open_file_android] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:14:13-56
182            <meta-data
182-->[:open_file_android] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:16:13-18:53
183                android:name="android.support.FILE_PROVIDER_PATHS"
183-->[:open_file_android] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:17:17-67
184                android:resource="@xml/filepaths" />
184-->[:open_file_android] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:18:17-50
185        </provider>
186
187        <activity
187-->[:url_launcher_android] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-11:74
188            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
188-->[:url_launcher_android] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-74
189            android:exported="false"
189-->[:url_launcher_android] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:10:13-37
190            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
190-->[:url_launcher_android] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-71
191
192        <receiver
192-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\f292f42d5744c12ab1fd61186974f62c\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:29:9-40:20
193            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
193-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\f292f42d5744c12ab1fd61186974f62c\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:30:13-78
194            android:exported="true"
194-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\f292f42d5744c12ab1fd61186974f62c\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:31:13-36
195            android:permission="com.google.android.c2dm.permission.SEND" >
195-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\f292f42d5744c12ab1fd61186974f62c\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:32:13-73
196            <intent-filter>
196-->[:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:30:13-32:29
197                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
197-->[:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:31:17-81
197-->[:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:31:25-78
198            </intent-filter>
199
200            <meta-data
200-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\f292f42d5744c12ab1fd61186974f62c\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:37:13-39:40
201                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
201-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\f292f42d5744c12ab1fd61186974f62c\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:38:17-92
202                android:value="true" />
202-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\f292f42d5744c12ab1fd61186974f62c\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:39:17-37
203        </receiver>
204        <!--
205             FirebaseMessagingService performs security checks at runtime,
206             but set to not exported to explicitly avoid allowing another app to call it.
207        -->
208        <service
208-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\f292f42d5744c12ab1fd61186974f62c\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:46:9-53:19
209            android:name="com.google.firebase.messaging.FirebaseMessagingService"
209-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\f292f42d5744c12ab1fd61186974f62c\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:47:13-82
210            android:directBootAware="true"
210-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\f292f42d5744c12ab1fd61186974f62c\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:48:13-43
211            android:exported="false" >
211-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\f292f42d5744c12ab1fd61186974f62c\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:49:13-37
212            <intent-filter android:priority="-500" >
212-->[:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:21:13-23:29
213                <action android:name="com.google.firebase.MESSAGING_EVENT" />
213-->[:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:22:17-78
213-->[:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:22:25-75
214            </intent-filter>
215        </service>
216
217        <activity
217-->[net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-3\81deae67eaa10a6c4f1748ffaac2c185\transformed\jetified-appauth-0.11.1\AndroidManifest.xml:35:9-40:77
218            android:name="net.openid.appauth.AuthorizationManagementActivity"
218-->[net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-3\81deae67eaa10a6c4f1748ffaac2c185\transformed\jetified-appauth-0.11.1\AndroidManifest.xml:36:13-78
219            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation|keyboard|keyboardHidden"
219-->[net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-3\81deae67eaa10a6c4f1748ffaac2c185\transformed\jetified-appauth-0.11.1\AndroidManifest.xml:37:13-115
220            android:exported="false"
220-->[net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-3\81deae67eaa10a6c4f1748ffaac2c185\transformed\jetified-appauth-0.11.1\AndroidManifest.xml:38:13-37
221            android:launchMode="singleTask"
221-->[net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-3\81deae67eaa10a6c4f1748ffaac2c185\transformed\jetified-appauth-0.11.1\AndroidManifest.xml:39:13-44
222            android:theme="@style/Theme.AppCompat.Translucent.NoTitleBar" />
222-->[net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-3\81deae67eaa10a6c4f1748ffaac2c185\transformed\jetified-appauth-0.11.1\AndroidManifest.xml:40:13-74
223        <activity
223-->[net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-3\81deae67eaa10a6c4f1748ffaac2c185\transformed\jetified-appauth-0.11.1\AndroidManifest.xml:41:9-52:20
224            android:name="net.openid.appauth.RedirectUriReceiverActivity"
224-->[net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-3\81deae67eaa10a6c4f1748ffaac2c185\transformed\jetified-appauth-0.11.1\AndroidManifest.xml:42:13-74
225            android:exported="true" >
225-->[net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-3\81deae67eaa10a6c4f1748ffaac2c185\transformed\jetified-appauth-0.11.1\AndroidManifest.xml:43:13-36
226            <intent-filter>
226-->[net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-3\81deae67eaa10a6c4f1748ffaac2c185\transformed\jetified-appauth-0.11.1\AndroidManifest.xml:44:13-51:29
227                <action android:name="android.intent.action.VIEW" />
227-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:39:17-68
227-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:39:25-66
228
229                <category android:name="android.intent.category.DEFAULT" />
229-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:40:17-75
229-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:40:27-73
230                <category android:name="android.intent.category.BROWSABLE" />
230-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:41:17-77
230-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:41:27-75
231
232                <data android:scheme="com.example.NovaSchool" />
232-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:42:17-89
232-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:42:23-62
233            </intent-filter>
234        </activity>
235
236        <uses-library
236-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\406aa8a46ba8a926b7fea9cf7eb85cb1\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
237            android:name="androidx.window.extensions"
237-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\406aa8a46ba8a926b7fea9cf7eb85cb1\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
238            android:required="false" />
238-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\406aa8a46ba8a926b7fea9cf7eb85cb1\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
239        <uses-library
239-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\406aa8a46ba8a926b7fea9cf7eb85cb1\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
240            android:name="androidx.window.sidecar"
240-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\406aa8a46ba8a926b7fea9cf7eb85cb1\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
241            android:required="false" />
241-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\406aa8a46ba8a926b7fea9cf7eb85cb1\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
242
243        <provider
243-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0119e1a605cd1162e47ac2939613edbf\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
244            android:name="androidx.startup.InitializationProvider"
244-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0119e1a605cd1162e47ac2939613edbf\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
245            android:authorities="com.example.AmanaSchool.androidx-startup"
245-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0119e1a605cd1162e47ac2939613edbf\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
246            android:exported="false" >
246-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0119e1a605cd1162e47ac2939613edbf\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
247            <meta-data
247-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0119e1a605cd1162e47ac2939613edbf\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
248                android:name="androidx.emoji2.text.EmojiCompatInitializer"
248-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0119e1a605cd1162e47ac2939613edbf\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
249                android:value="androidx.startup" />
249-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0119e1a605cd1162e47ac2939613edbf\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
250            <meta-data
250-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\4ef77465e2fc8a8db167e9f551e38af7\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
251                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
251-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\4ef77465e2fc8a8db167e9f551e38af7\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
252                android:value="androidx.startup" />
252-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\4ef77465e2fc8a8db167e9f551e38af7\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
253            <meta-data
253-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\22d5d51df25a3be260604f40ecd5d44b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
254                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
254-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\22d5d51df25a3be260604f40ecd5d44b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
255                android:value="androidx.startup" />
255-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\22d5d51df25a3be260604f40ecd5d44b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
256        </provider>
257        <provider
257-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2947ac83f2922b287048df084e924bb9\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
258            android:name="com.google.firebase.provider.FirebaseInitProvider"
258-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2947ac83f2922b287048df084e924bb9\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
259            android:authorities="com.example.AmanaSchool.firebaseinitprovider"
259-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2947ac83f2922b287048df084e924bb9\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
260            android:directBootAware="true"
260-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2947ac83f2922b287048df084e924bb9\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
261            android:exported="false"
261-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2947ac83f2922b287048df084e924bb9\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
262            android:initOrder="100" />
262-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2947ac83f2922b287048df084e924bb9\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
263
264        <activity
264-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\24edfc8d79318ad978ebca556f2be80c\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
265            android:name="com.google.android.gms.common.api.GoogleApiActivity"
265-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\24edfc8d79318ad978ebca556f2be80c\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:19-85
266            android:exported="false"
266-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\24edfc8d79318ad978ebca556f2be80c\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:22:19-43
267            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
267-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\24edfc8d79318ad978ebca556f2be80c\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:21:19-78
268
269        <meta-data
269-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2c4d8acbdbca7d5c68adac6b679ea4c3\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
270            android:name="com.google.android.gms.version"
270-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2c4d8acbdbca7d5c68adac6b679ea4c3\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
271            android:value="@integer/google_play_services_version" />
271-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2c4d8acbdbca7d5c68adac6b679ea4c3\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
272
273        <receiver
273-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\22d5d51df25a3be260604f40ecd5d44b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
274            android:name="androidx.profileinstaller.ProfileInstallReceiver"
274-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\22d5d51df25a3be260604f40ecd5d44b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
275            android:directBootAware="false"
275-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\22d5d51df25a3be260604f40ecd5d44b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
276            android:enabled="true"
276-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\22d5d51df25a3be260604f40ecd5d44b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
277            android:exported="true"
277-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\22d5d51df25a3be260604f40ecd5d44b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
278            android:permission="android.permission.DUMP" >
278-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\22d5d51df25a3be260604f40ecd5d44b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
279            <intent-filter>
279-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\22d5d51df25a3be260604f40ecd5d44b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
280                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
280-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\22d5d51df25a3be260604f40ecd5d44b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
280-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\22d5d51df25a3be260604f40ecd5d44b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
281            </intent-filter>
282            <intent-filter>
282-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\22d5d51df25a3be260604f40ecd5d44b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
283                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
283-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\22d5d51df25a3be260604f40ecd5d44b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
283-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\22d5d51df25a3be260604f40ecd5d44b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
284            </intent-filter>
285            <intent-filter>
285-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\22d5d51df25a3be260604f40ecd5d44b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
286                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
286-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\22d5d51df25a3be260604f40ecd5d44b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
286-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\22d5d51df25a3be260604f40ecd5d44b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
287            </intent-filter>
288            <intent-filter>
288-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\22d5d51df25a3be260604f40ecd5d44b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
289                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
289-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\22d5d51df25a3be260604f40ecd5d44b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
289-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\22d5d51df25a3be260604f40ecd5d44b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
290            </intent-filter>
291        </receiver>
292
293        <service
293-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\c5dd6fc0d353cf4e5e09e983fec394b0\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
294            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
294-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\c5dd6fc0d353cf4e5e09e983fec394b0\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
295            android:exported="false" >
295-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\c5dd6fc0d353cf4e5e09e983fec394b0\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
296            <meta-data
296-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\c5dd6fc0d353cf4e5e09e983fec394b0\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
297                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
297-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\c5dd6fc0d353cf4e5e09e983fec394b0\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
298                android:value="cct" />
298-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\c5dd6fc0d353cf4e5e09e983fec394b0\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
299        </service>
300        <service
300-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\5501637c06234181a2ed9becb6441ee1\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
301            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
301-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\5501637c06234181a2ed9becb6441ee1\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
302            android:exported="false"
302-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\5501637c06234181a2ed9becb6441ee1\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
303            android:permission="android.permission.BIND_JOB_SERVICE" >
303-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\5501637c06234181a2ed9becb6441ee1\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
304        </service>
305
306        <receiver
306-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\5501637c06234181a2ed9becb6441ee1\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
307            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
307-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\5501637c06234181a2ed9becb6441ee1\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
308            android:exported="false" />
308-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\5501637c06234181a2ed9becb6441ee1\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
309    </application>
310
311</manifest>
