import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';

@Component({
  selector: 'app-admin-settings',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <div class="admin-settings">
      <!-- Header -->
      <header class="page-header">
        <div class="header-content">
          <div class="header-left">
            <button class="back-btn" (click)="goBack()">
              <span class="icon">←</span>
              Back to Dashboard
            </button>
            <h1>⚙️ System Settings</h1>
            <p>Configure system preferences and security</p>
          </div>
          <div class="header-actions">
            <button class="save-btn" (click)="saveSettings()">
              <span class="icon">💾</span>
              Save Changes
            </button>
          </div>
        </div>
      </header>

      <!-- Settings Tabs -->
      <section class="tabs-section">
        <div class="tabs-content">
          <div class="tab-buttons">
            <button 
              class="tab-btn" 
              [class.active]="activeTab === 'general'"
              (click)="setActiveTab('general')">
              🏫 General
            </button>
            <button 
              class="tab-btn" 
              [class.active]="activeTab === 'security'"
              (click)="setActiveTab('security')">
              🔒 Security
            </button>
            <button 
              class="tab-btn" 
              [class.active]="activeTab === 'notifications'"
              (click)="setActiveTab('notifications')">
              🔔 Notifications
            </button>
            <button 
              class="tab-btn" 
              [class.active]="activeTab === 'backup'"
              (click)="setActiveTab('backup')">
              💾 Backup
            </button>
          </div>
        </div>
      </section>

      <!-- Settings Content -->
      <section class="settings-content">
        <!-- General Settings -->
        <div class="settings-section" *ngIf="activeTab === 'general'">
          <div class="section-header">
            <h2>🏫 General Settings</h2>
            <p>Basic school information and system preferences</p>
          </div>
          
          <div class="settings-grid">
            <div class="setting-card">
              <h3>School Information</h3>
              <div class="form-group">
                <label>School Name</label>
                <input type="text" [(ngModel)]="settings.general.schoolName" name="schoolName">
              </div>
              <div class="form-group">
                <label>School Address</label>
                <textarea [(ngModel)]="settings.general.schoolAddress" name="schoolAddress" rows="3"></textarea>
              </div>
              <div class="form-row">
                <div class="form-group">
                  <label>Phone Number</label>
                  <input type="tel" [(ngModel)]="settings.general.phoneNumber" name="phoneNumber">
                </div>
                <div class="form-group">
                  <label>Email Address</label>
                  <input type="email" [(ngModel)]="settings.general.emailAddress" name="emailAddress">
                </div>
              </div>
            </div>

            <div class="setting-card">
              <h3>Academic Year</h3>
              <div class="form-row">
                <div class="form-group">
                  <label>Current Academic Year</label>
                  <select [(ngModel)]="settings.general.academicYear" name="academicYear">
                    <option value="2023-2024">2023-2024</option>
                    <option value="2024-2025">2024-2025</option>
                    <option value="2025-2026">2025-2026</option>
                  </select>
                </div>
                <div class="form-group">
                  <label>Semester</label>
                  <select [(ngModel)]="settings.general.semester" name="semester">
                    <option value="1">First Semester</option>
                    <option value="2">Second Semester</option>
                  </select>
                </div>
              </div>
              <div class="form-row">
                <div class="form-group">
                  <label>School Start Time</label>
                  <input type="time" [(ngModel)]="settings.general.schoolStartTime" name="schoolStartTime">
                </div>
                <div class="form-group">
                  <label>School End Time</label>
                  <input type="time" [(ngModel)]="settings.general.schoolEndTime" name="schoolEndTime">
                </div>
              </div>
            </div>

            <div class="setting-card">
              <h3>System Preferences</h3>
              <div class="form-group">
                <label>Default Language</label>
                <select [(ngModel)]="settings.general.defaultLanguage" name="defaultLanguage">
                  <option value="en">English</option>
                  <option value="fr">French</option>
                  <option value="ar">Arabic</option>
                </select>
              </div>
              <div class="form-group">
                <label>Time Zone</label>
                <select [(ngModel)]="settings.general.timeZone" name="timeZone">
                  <option value="UTC">UTC</option>
                  <option value="Africa/Tunis">Africa/Tunis</option>
                  <option value="Europe/Paris">Europe/Paris</option>
                </select>
              </div>
              <div class="checkbox-group">
                <label class="checkbox-label">
                  <input type="checkbox" [(ngModel)]="settings.general.enableMaintenance" name="enableMaintenance">
                  <span class="checkmark"></span>
                  Enable Maintenance Mode
                </label>
              </div>
            </div>
          </div>
        </div>

        <!-- Security Settings -->
        <div class="settings-section" *ngIf="activeTab === 'security'">
          <div class="section-header">
            <h2>🔒 Security Settings</h2>
            <p>Configure authentication and security policies</p>
          </div>
          
          <div class="settings-grid">
            <div class="setting-card">
              <h3>Password Policy</h3>
              <div class="form-group">
                <label>Minimum Password Length</label>
                <input type="number" [(ngModel)]="settings.security.minPasswordLength" name="minPasswordLength" min="6" max="20">
              </div>
              <div class="checkbox-group">
                <label class="checkbox-label">
                  <input type="checkbox" [(ngModel)]="settings.security.requireUppercase" name="requireUppercase">
                  <span class="checkmark"></span>
                  Require Uppercase Letters
                </label>
                <label class="checkbox-label">
                  <input type="checkbox" [(ngModel)]="settings.security.requireNumbers" name="requireNumbers">
                  <span class="checkmark"></span>
                  Require Numbers
                </label>
                <label class="checkbox-label">
                  <input type="checkbox" [(ngModel)]="settings.security.requireSpecialChars" name="requireSpecialChars">
                  <span class="checkmark"></span>
                  Require Special Characters
                </label>
              </div>
            </div>

            <div class="setting-card">
              <h3>Session Management</h3>
              <div class="form-group">
                <label>Session Timeout (minutes)</label>
                <input type="number" [(ngModel)]="settings.security.sessionTimeout" name="sessionTimeout" min="15" max="480">
              </div>
              <div class="form-group">
                <label>Max Login Attempts</label>
                <input type="number" [(ngModel)]="settings.security.maxLoginAttempts" name="maxLoginAttempts" min="3" max="10">
              </div>
              <div class="checkbox-group">
                <label class="checkbox-label">
                  <input type="checkbox" [(ngModel)]="settings.security.enableTwoFactor" name="enableTwoFactor">
                  <span class="checkmark"></span>
                  Enable Two-Factor Authentication
                </label>
              </div>
            </div>

            <div class="setting-card">
              <h3>Data Protection</h3>
              <div class="checkbox-group">
                <label class="checkbox-label">
                  <input type="checkbox" [(ngModel)]="settings.security.enableDataEncryption" name="enableDataEncryption">
                  <span class="checkmark"></span>
                  Enable Data Encryption
                </label>
                <label class="checkbox-label">
                  <input type="checkbox" [(ngModel)]="settings.security.enableAuditLog" name="enableAuditLog">
                  <span class="checkmark"></span>
                  Enable Audit Logging
                </label>
                <label class="checkbox-label">
                  <input type="checkbox" [(ngModel)]="settings.security.enableIPWhitelist" name="enableIPWhitelist">
                  <span class="checkmark"></span>
                  Enable IP Whitelist
                </label>
              </div>
            </div>
          </div>
        </div>

        <!-- Notification Settings -->
        <div class="settings-section" *ngIf="activeTab === 'notifications'">
          <div class="section-header">
            <h2>🔔 Notification Settings</h2>
            <p>Configure email and system notifications</p>
          </div>
          
          <div class="settings-grid">
            <div class="setting-card">
              <h3>Email Configuration</h3>
              <div class="form-group">
                <label>SMTP Server</label>
                <input type="text" [(ngModel)]="settings.notifications.smtpServer" name="smtpServer">
              </div>
              <div class="form-row">
                <div class="form-group">
                  <label>SMTP Port</label>
                  <input type="number" [(ngModel)]="settings.notifications.smtpPort" name="smtpPort">
                </div>
                <div class="form-group">
                  <label>Encryption</label>
                  <select [(ngModel)]="settings.notifications.smtpEncryption" name="smtpEncryption">
                    <option value="none">None</option>
                    <option value="tls">TLS</option>
                    <option value="ssl">SSL</option>
                  </select>
                </div>
              </div>
              <div class="form-row">
                <div class="form-group">
                  <label>Username</label>
                  <input type="text" [(ngModel)]="settings.notifications.smtpUsername" name="smtpUsername">
                </div>
                <div class="form-group">
                  <label>Password</label>
                  <input type="password" [(ngModel)]="settings.notifications.smtpPassword" name="smtpPassword">
                </div>
              </div>
            </div>

            <div class="setting-card">
              <h3>Notification Types</h3>
              <div class="checkbox-group">
                <label class="checkbox-label">
                  <input type="checkbox" [(ngModel)]="settings.notifications.enableUserRegistration" name="enableUserRegistration">
                  <span class="checkmark"></span>
                  User Registration Notifications
                </label>
                <label class="checkbox-label">
                  <input type="checkbox" [(ngModel)]="settings.notifications.enableGradeUpdates" name="enableGradeUpdates">
                  <span class="checkmark"></span>
                  Grade Update Notifications
                </label>
                <label class="checkbox-label">
                  <input type="checkbox" [(ngModel)]="settings.notifications.enableAttendanceAlerts" name="enableAttendanceAlerts">
                  <span class="checkmark"></span>
                  Attendance Alert Notifications
                </label>
                <label class="checkbox-label">
                  <input type="checkbox" [(ngModel)]="settings.notifications.enableSystemAlerts" name="enableSystemAlerts">
                  <span class="checkmark"></span>
                  System Alert Notifications
                </label>
              </div>
            </div>
          </div>
        </div>

        <!-- Backup Settings -->
        <div class="settings-section" *ngIf="activeTab === 'backup'">
          <div class="section-header">
            <h2>💾 Backup Settings</h2>
            <p>Configure automatic backups and data retention</p>
          </div>
          
          <div class="settings-grid">
            <div class="setting-card">
              <h3>Backup Schedule</h3>
              <div class="form-group">
                <label>Backup Frequency</label>
                <select [(ngModel)]="settings.backup.frequency" name="backupFrequency">
                  <option value="daily">Daily</option>
                  <option value="weekly">Weekly</option>
                  <option value="monthly">Monthly</option>
                </select>
              </div>
              <div class="form-group">
                <label>Backup Time</label>
                <input type="time" [(ngModel)]="settings.backup.time" name="backupTime">
              </div>
              <div class="form-group">
                <label>Retention Period (days)</label>
                <input type="number" [(ngModel)]="settings.backup.retentionDays" name="retentionDays" min="7" max="365">
              </div>
            </div>

            <div class="setting-card">
              <h3>Backup Status</h3>
              <div class="backup-status">
                <div class="status-item">
                  <span class="status-label">Last Backup:</span>
                  <span class="status-value">{{lastBackupDate}}</span>
                </div>
                <div class="status-item">
                  <span class="status-label">Backup Size:</span>
                  <span class="status-value">{{backupSize}}</span>
                </div>
                <div class="status-item">
                  <span class="status-label">Status:</span>
                  <span class="status-value success">{{backupStatus}}</span>
                </div>
              </div>
              <div class="backup-actions">
                <button class="action-btn primary" (click)="createBackup()">
                  <span class="icon">💾</span>
                  Create Backup Now
                </button>
                <button class="action-btn secondary" (click)="restoreBackup()">
                  <span class="icon">🔄</span>
                  Restore Backup
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  `,
  styles: [`
    .admin-settings {
      min-height: 100vh;
      background: #f5f7fa;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .page-header {
      background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
      color: white;
      padding: 30px;
      margin-bottom: 30px;
    }

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      max-width: 1400px;
      margin: 0 auto;
    }

    .back-btn {
      background: rgba(255, 255, 255, 0.2);
      border: none;
      color: white;
      padding: 8px 16px;
      border-radius: 6px;
      cursor: pointer;
      margin-bottom: 16px;
      transition: all 0.3s ease;
    }

    .back-btn:hover {
      background: rgba(255, 255, 255, 0.3);
    }

    .header-left h1 {
      margin: 0 0 8px 0;
      font-size: 28px;
      font-weight: 600;
    }

    .header-left p {
      margin: 0;
      opacity: 0.9;
      font-size: 16px;
    }

    .save-btn {
      background: #27ae60;
      border: none;
      color: white;
      padding: 12px 24px;
      border-radius: 8px;
      cursor: pointer;
      font-weight: 600;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .save-btn:hover {
      background: #229954;
      transform: translateY(-2px);
    }

    .tabs-section {
      max-width: 1400px;
      margin: 0 auto 30px auto;
      padding: 0 30px;
    }

    .tabs-content {
      background: white;
      border-radius: 12px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
      overflow: hidden;
    }

    .tab-buttons {
      display: flex;
      border-bottom: 1px solid #e1e5e9;
    }

    .tab-btn {
      background: none;
      border: none;
      padding: 16px 24px;
      cursor: pointer;
      font-size: 16px;
      font-weight: 500;
      color: #7f8c8d;
      transition: all 0.3s ease;
      border-bottom: 3px solid transparent;
    }

    .tab-btn.active {
      color: #2c3e50;
      border-bottom-color: #3498db;
      background: #f8f9fa;
    }

    .tab-btn:hover:not(.active) {
      background: #f8f9fa;
    }

    .settings-content {
      max-width: 1400px;
      margin: 0 auto;
      padding: 0 30px;
    }

    .settings-section {
      background: white;
      border-radius: 12px;
      padding: 24px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    }

    .section-header {
      margin-bottom: 24px;
      padding-bottom: 16px;
      border-bottom: 1px solid #e1e5e9;
    }

    .section-header h2 {
      margin: 0 0 8px 0;
      color: #2c3e50;
      font-size: 22px;
    }

    .section-header p {
      margin: 0;
      color: #7f8c8d;
      font-size: 14px;
    }

    .settings-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
      gap: 24px;
    }

    .setting-card {
      background: #f8f9fa;
      border-radius: 8px;
      padding: 20px;
    }

    .setting-card h3 {
      margin: 0 0 16px 0;
      color: #2c3e50;
      font-size: 18px;
    }

    .form-group {
      margin-bottom: 16px;
    }

    .form-group label {
      display: block;
      margin-bottom: 8px;
      font-weight: 500;
      color: #2c3e50;
      font-size: 14px;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
      width: 100%;
      padding: 12px;
      border: 2px solid #e1e5e9;
      border-radius: 6px;
      font-size: 14px;
      box-sizing: border-box;
    }

    .form-group input:focus,
    .form-group select:focus,
    .form-group textarea:focus {
      outline: none;
      border-color: #3498db;
    }

    .form-row {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 16px;
    }

    .checkbox-group {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    .checkbox-label {
      display: flex;
      align-items: center;
      gap: 12px;
      cursor: pointer;
      font-size: 14px;
      color: #2c3e50;
    }

    .checkbox-label input[type="checkbox"] {
      width: auto;
      margin: 0;
    }

    .backup-status {
      background: white;
      border-radius: 6px;
      padding: 16px;
      margin-bottom: 16px;
    }

    .status-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
    }

    .status-label {
      color: #7f8c8d;
      font-size: 14px;
    }

    .status-value {
      font-weight: 600;
      color: #2c3e50;
    }

    .status-value.success {
      color: #27ae60;
    }

    .backup-actions {
      display: flex;
      gap: 12px;
    }

    .action-btn {
      border: none;
      padding: 12px 20px;
      border-radius: 6px;
      cursor: pointer;
      font-weight: 500;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .action-btn.primary {
      background: #3498db;
      color: white;
    }

    .action-btn.primary:hover {
      background: #2980b9;
    }

    .action-btn.secondary {
      background: #95a5a6;
      color: white;
    }

    .action-btn.secondary:hover {
      background: #7f8c8d;
    }

    @media (max-width: 768px) {
      .header-content {
        flex-direction: column;
        gap: 20px;
        text-align: center;
      }

      .tab-buttons {
        flex-direction: column;
      }

      .settings-grid {
        grid-template-columns: 1fr;
      }

      .form-row {
        grid-template-columns: 1fr;
      }

      .backup-actions {
        flex-direction: column;
      }
    }
  `]
})
export class AdminSettingsComponent {
  activeTab = 'general';
  lastBackupDate = '2024-01-15 02:00 AM';
  backupSize = '2.4 GB';
  backupStatus = 'Successful';

  settings = {
    general: {
      schoolName: 'Demo School Management System',
      schoolAddress: '123 Education Street, Learning City, LC 12345',
      phoneNumber: '+****************',
      emailAddress: '<EMAIL>',
      academicYear: '2023-2024',
      semester: '1',
      schoolStartTime: '08:00',
      schoolEndTime: '15:30',
      defaultLanguage: 'en',
      timeZone: 'UTC',
      enableMaintenance: false
    },
    security: {
      minPasswordLength: 8,
      requireUppercase: true,
      requireNumbers: true,
      requireSpecialChars: false,
      sessionTimeout: 60,
      maxLoginAttempts: 5,
      enableTwoFactor: false,
      enableDataEncryption: true,
      enableAuditLog: true,
      enableIPWhitelist: false
    },
    notifications: {
      smtpServer: 'smtp.gmail.com',
      smtpPort: 587,
      smtpEncryption: 'tls',
      smtpUsername: '<EMAIL>',
      smtpPassword: '',
      enableUserRegistration: true,
      enableGradeUpdates: true,
      enableAttendanceAlerts: true,
      enableSystemAlerts: true
    },
    backup: {
      frequency: 'daily',
      time: '02:00',
      retentionDays: 30
    }
  };

  constructor(private router: Router) {}

  goBack(): void {
    this.router.navigate(['/admin']);
  }

  setActiveTab(tab: string): void {
    this.activeTab = tab;
  }

  saveSettings(): void {
    console.log('Saving settings:', this.settings);
    // Implement save functionality
    alert('Settings saved successfully!');
  }

  createBackup(): void {
    console.log('Creating backup...');
    // Implement backup functionality
    alert('Backup created successfully!');
  }

  restoreBackup(): void {
    if (confirm('Are you sure you want to restore from backup? This will overwrite current data.')) {
      console.log('Restoring backup...');
      // Implement restore functionality
      alert('Backup restored successfully!');
    }
  }
}
