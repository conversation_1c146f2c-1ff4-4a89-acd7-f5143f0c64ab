spring:
  application:
    name: school-management-system
  
  # Database Configuration
  datasource:
    url: **************************************************
    username: postgres
    password: root
    driver-class-name: org.postgresql.Driver
    
  # JPA Configuration
  jpa:
    hibernate:
      ddl-auto: update  # Creates tables automatically
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
    database-platform: org.hibernate.dialect.PostgreSQLDialect
  
  # Server Configuration
  server:
    port: 2023  # Same port as your existing API endpoints
    servlet:
      context-path: /api
  
  # Security Configuration (for later)
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: http://localhost:9003/realms/Eco-Code

# Logging Configuration
logging:
  level:
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
    com.ecocode: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"

# Custom Application Properties
app:
  school:
    name: "Demo School Management System"
    timezone: "Africa/Tunis"
  jwt:
    secret: "mySecretKey"
    expiration: 86400000  # 24 hours
