import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

interface Course {
  id: number;
  name: string;
  teacher: string;
  subject: string;
  grade: string;
  schedule: string;
  room: string;
  description: string;
  materials: string[];
  progress: number;
  nextClass: string;
  assignments: number;
  status: 'active' | 'completed' | 'upcoming';
  childName: string;
}

@Component({
  selector: 'app-parent-courses',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <div class="courses-container">
      <!-- Header -->
      <div class="courses-header">
        <div class="header-content">
          <h1>📚 Courses Overview</h1>
          <p>Monitor your children's academic progress and course materials</p>
        </div>
        <div class="header-actions">
          <div class="search-box">
            <input
              type="text"
              placeholder="Search courses..."
              [(ngModel)]="searchTerm"
              (input)="filterCourses()"
              class="search-input">
            <span class="search-icon">🔍</span>
          </div>
          <select [(ngModel)]="selectedChild" (change)="filterCourses()" class="child-filter">
            <option value="">All Children</option>
            <option value="<PERSON>"><PERSON></option>
            <option value="<PERSON>"><PERSON></option>
          </select>
        </div>
      </div>

      <!-- Summary Cards -->
      <div class="summary-section">
        <div class="summary-card">
          <div class="summary-icon">📖</div>
          <div class="summary-content">
            <h3>{{getTotalCourses()}}</h3>
            <p>Total Courses</p>
          </div>
        </div>
        <div class="summary-card">
          <div class="summary-icon">✅</div>
          <div class="summary-content">
            <h3>{{getActiveCourses()}}</h3>
            <p>Active Courses</p>
          </div>
        </div>
        <div class="summary-card">
          <div class="summary-icon">📝</div>
          <div class="summary-content">
            <h3>{{getTotalAssignments()}}</h3>
            <p>Total Assignments</p>
          </div>
        </div>
        <div class="summary-card">
          <div class="summary-icon">📊</div>
          <div class="summary-content">
            <h3>{{getAverageProgress()}}%</h3>
            <p>Average Progress</p>
          </div>
        </div>
      </div>

      <!-- Courses Grid -->
      <div class="courses-grid">
        <div *ngFor="let course of filteredCourses" class="course-card" [class]="course.status">
          <div class="course-header">
            <div class="course-info">
              <h3>{{course.name}}</h3>
              <p class="course-meta">{{course.subject}} • {{course.grade}}</p>
              <p class="child-name">👤 {{course.childName}}</p>
            </div>
            <div class="course-status">
              <span class="status-badge" [class]="course.status">{{course.status | titlecase}}</span>
            </div>
          </div>

          <div class="course-details">
            <div class="detail-row">
              <span class="label">👨‍🏫 Teacher:</span>
              <span class="value">{{course.teacher}}</span>
            </div>
            <div class="detail-row">
              <span class="label">📅 Schedule:</span>
              <span class="value">{{course.schedule}}</span>
            </div>
            <div class="detail-row">
              <span class="label">🏫 Room:</span>
              <span class="value">{{course.room}}</span>
            </div>
            <div class="detail-row">
              <span class="label">⏰ Next Class:</span>
              <span class="value">{{course.nextClass}}</span>
            </div>
          </div>

          <div class="course-progress">
            <div class="progress-header">
              <span>Progress</span>
              <span class="progress-value">{{course.progress}}%</span>
            </div>
            <div class="progress-bar">
              <div class="progress-fill" [style.width.%]="course.progress"></div>
            </div>
          </div>

          <div class="course-description">
            <p>{{course.description}}</p>
          </div>

          <div class="course-materials" *ngIf="course.materials.length > 0">
            <h4>📋 Course Materials</h4>
            <div class="materials-list">
              <span *ngFor="let material of course.materials" class="material-tag">{{material}}</span>
            </div>
          </div>

          <div class="course-actions">
            <button class="action-btn primary" (click)="viewCourseDetails(course)">
              <span class="btn-icon">👁️</span>
              View Details
            </button>
            <button class="action-btn secondary" (click)="contactTeacher(course)">
              <span class="btn-icon">💬</span>
              Contact Teacher
            </button>
            <button class="action-btn info" (click)="viewAssignments(course)">
              <span class="btn-icon">📝</span>
              Assignments ({{course.assignments}})
            </button>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div *ngIf="filteredCourses.length === 0" class="empty-state">
        <div class="empty-icon">📚</div>
        <h3>No courses found</h3>
        <p>Try adjusting your search criteria or filters.</p>
      </div>
    </div>
  `,
  styles: [`
    .courses-container {
      min-height: 100vh;
      background: #f5f7fa;
      padding: 20px;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .courses-header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 30px;
      border-radius: 12px;
      margin-bottom: 30px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;
      gap: 20px;
    }

    .header-content h1 {
      margin: 0 0 8px 0;
      font-size: 28px;
      font-weight: 600;
    }

    .header-content p {
      margin: 0;
      opacity: 0.9;
      font-size: 16px;
    }

    .header-actions {
      display: flex;
      gap: 16px;
      align-items: center;
      flex-wrap: wrap;
    }

    .search-box {
      position: relative;
    }

    .search-input {
      padding: 12px 40px 12px 16px;
      border: none;
      border-radius: 8px;
      background: rgba(255, 255, 255, 0.2);
      color: white;
      placeholder-color: rgba(255, 255, 255, 0.7);
      min-width: 250px;
    }

    .search-input::placeholder {
      color: rgba(255, 255, 255, 0.7);
    }

    .search-icon {
      position: absolute;
      right: 12px;
      top: 50%;
      transform: translateY(-50%);
      opacity: 0.7;
    }

    .child-filter {
      padding: 12px 16px;
      border: none;
      border-radius: 8px;
      background: rgba(255, 255, 255, 0.2);
      color: white;
      cursor: pointer;
    }

    .summary-section {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 20px;
      margin-bottom: 30px;
    }

    .summary-card {
      background: white;
      padding: 24px;
      border-radius: 12px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
      display: flex;
      align-items: center;
      gap: 16px;
      transition: transform 0.3s ease;
    }

    .summary-card:hover {
      transform: translateY(-2px);
    }

    .summary-icon {
      font-size: 32px;
      background: #f8f9fa;
      padding: 12px;
      border-radius: 8px;
    }

    .summary-content h3 {
      margin: 0 0 4px 0;
      font-size: 24px;
      font-weight: 600;
      color: #333;
    }

    .summary-content p {
      margin: 0;
      color: #666;
      font-size: 14px;
    }

    .courses-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
      gap: 24px;
    }

    .course-card {
      background: white;
      border-radius: 12px;
      padding: 24px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
      transition: all 0.3s ease;
      border-left: 4px solid #667eea;
    }

    .course-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .course-card.active {
      border-left-color: #28a745;
    }

    .course-card.completed {
      border-left-color: #6c757d;
    }

    .course-card.upcoming {
      border-left-color: #ffc107;
    }

    .course-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 16px;
    }

    .course-info h3 {
      margin: 0 0 4px 0;
      color: #333;
      font-size: 18px;
      font-weight: 600;
    }

    .course-meta {
      margin: 0 0 8px 0;
      color: #666;
      font-size: 14px;
    }

    .child-name {
      margin: 0;
      color: #667eea;
      font-size: 14px;
      font-weight: 500;
    }

    .status-badge {
      padding: 4px 12px;
      border-radius: 20px;
      font-size: 12px;
      font-weight: 500;
      text-transform: uppercase;
    }

    .status-badge.active {
      background: #d4edda;
      color: #155724;
    }

    .status-badge.completed {
      background: #e2e3e5;
      color: #383d41;
    }

    .status-badge.upcoming {
      background: #fff3cd;
      color: #856404;
    }

    .course-details {
      margin-bottom: 16px;
    }

    .detail-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
    }

    .detail-row .label {
      color: #666;
      font-size: 14px;
    }

    .detail-row .value {
      color: #333;
      font-weight: 500;
      font-size: 14px;
    }

    .course-progress {
      margin-bottom: 16px;
    }

    .progress-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
      font-size: 14px;
      font-weight: 500;
    }

    .progress-bar {
      height: 8px;
      background: #e9ecef;
      border-radius: 4px;
      overflow: hidden;
    }

    .progress-fill {
      height: 100%;
      background: linear-gradient(90deg, #667eea, #764ba2);
      transition: width 0.3s ease;
    }

    .course-description {
      margin-bottom: 16px;
    }

    .course-description p {
      margin: 0;
      color: #666;
      font-size: 14px;
      line-height: 1.5;
    }

    .course-materials {
      margin-bottom: 20px;
    }

    .course-materials h4 {
      margin: 0 0 12px 0;
      color: #333;
      font-size: 14px;
      font-weight: 600;
    }

    .materials-list {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
    }

    .material-tag {
      background: #f8f9fa;
      color: #495057;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      border: 1px solid #dee2e6;
    }

    .course-actions {
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
    }

    .action-btn {
      flex: 1;
      min-width: 120px;
      padding: 10px 16px;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 6px;
    }

    .action-btn.primary {
      background: #667eea;
      color: white;
    }

    .action-btn.primary:hover {
      background: #5a6fd8;
    }

    .action-btn.secondary {
      background: #6c757d;
      color: white;
    }

    .action-btn.secondary:hover {
      background: #5a6268;
    }

    .action-btn.info {
      background: #17a2b8;
      color: white;
    }

    .action-btn.info:hover {
      background: #138496;
    }

    .empty-state {
      text-align: center;
      padding: 60px 20px;
      color: #666;
    }

    .empty-icon {
      font-size: 64px;
      margin-bottom: 16px;
    }

    .empty-state h3 {
      margin: 0 0 8px 0;
      color: #333;
    }

    .empty-state p {
      margin: 0;
    }

    @media (max-width: 768px) {
      .courses-header {
        flex-direction: column;
        align-items: stretch;
      }

      .header-actions {
        justify-content: stretch;
      }

      .search-input {
        min-width: 100%;
      }

      .courses-grid {
        grid-template-columns: 1fr;
      }

      .course-actions {
        flex-direction: column;
      }

      .action-btn {
        min-width: 100%;
      }
    }
  `]
})
export class ParentCoursesComponent implements OnInit {
  searchTerm = '';
  selectedChild = '';
  courses: Course[] = [];
  filteredCourses: Course[] = [];

  ngOnInit(): void {
    this.loadCourses();
    this.filteredCourses = this.courses;
  }

  loadCourses(): void {
    this.courses = [
      {
        id: 1,
        name: 'Advanced Mathematics',
        teacher: 'Dr. Sarah Wilson',
        subject: 'Mathematics',
        grade: 'Grade 5A',
        schedule: 'Mon, Wed, Fri - 9:00 AM',
        room: 'Room 201',
        description: 'Advanced mathematical concepts including algebra, geometry, and problem-solving techniques.',
        materials: ['Textbook Ch. 1-5', 'Calculator', 'Graph Paper', 'Workbook'],
        progress: 75,
        nextClass: 'Monday, 9:00 AM',
        assignments: 3,
        status: 'active',
        childName: 'Sarah Johnson'
      },
      {
        id: 2,
        name: 'English Literature',
        teacher: 'Ms. Emily Davis',
        subject: 'English',
        grade: 'Grade 5A',
        schedule: 'Tue, Thu - 10:30 AM',
        room: 'Room 105',
        description: 'Exploring classic and contemporary literature, developing reading comprehension and writing skills.',
        materials: ['Reading List', 'Journal', 'Dictionary'],
        progress: 82,
        nextClass: 'Tuesday, 10:30 AM',
        assignments: 2,
        status: 'active',
        childName: 'Sarah Johnson'
      },
      {
        id: 3,
        name: 'Science Exploration',
        teacher: 'Mr. James Rodriguez',
        subject: 'Science',
        grade: 'Grade 5A',
        schedule: 'Mon, Wed - 2:00 PM',
        room: 'Lab 1',
        description: 'Hands-on science experiments covering physics, chemistry, and biology fundamentals.',
        materials: ['Lab Manual', 'Safety Goggles', 'Lab Notebook'],
        progress: 68,
        nextClass: 'Wednesday, 2:00 PM',
        assignments: 4,
        status: 'active',
        childName: 'Sarah Johnson'
      },
      {
        id: 4,
        name: 'Art & Creativity',
        teacher: 'Ms. Lisa Chen',
        subject: 'Art',
        grade: 'Grade 3B',
        schedule: 'Tue, Fri - 11:30 AM',
        room: 'Art Studio',
        description: 'Developing artistic skills through various mediums including painting, drawing, and sculpture.',
        materials: ['Art Supplies', 'Sketchbook', 'Paints'],
        progress: 90,
        nextClass: 'Friday, 11:30 AM',
        assignments: 1,
        status: 'active',
        childName: 'Michael Johnson'
      },
      {
        id: 5,
        name: 'Physical Education',
        teacher: 'Coach Mike Thompson',
        subject: 'PE',
        grade: 'Grade 3B',
        schedule: 'Mon, Wed, Fri - 1:00 PM',
        room: 'Gymnasium',
        description: 'Physical fitness, team sports, and healthy lifestyle education.',
        materials: ['Sports Uniform', 'Water Bottle'],
        progress: 95,
        nextClass: 'Monday, 1:00 PM',
        assignments: 0,
        status: 'active',
        childName: 'Michael Johnson'
      },
      {
        id: 6,
        name: 'Music Fundamentals',
        teacher: 'Mr. David Park',
        subject: 'Music',
        grade: 'Grade 3B',
        schedule: 'Thu - 9:00 AM',
        room: 'Music Room',
        description: 'Introduction to music theory, rhythm, and basic instrument playing.',
        materials: ['Music Book', 'Recorder'],
        progress: 78,
        nextClass: 'Thursday, 9:00 AM',
        assignments: 1,
        status: 'active',
        childName: 'Michael Johnson'
      }
    ];
  }

  filterCourses(): void {
    this.filteredCourses = this.courses.filter(course => {
      const matchesSearch = course.name.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
                           course.teacher.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
                           course.subject.toLowerCase().includes(this.searchTerm.toLowerCase());

      const matchesChild = !this.selectedChild || course.childName === this.selectedChild;

      return matchesSearch && matchesChild;
    });
  }

  getTotalCourses(): number {
    return this.filteredCourses.length;
  }

  getActiveCourses(): number {
    return this.filteredCourses.filter(course => course.status === 'active').length;
  }

  getTotalAssignments(): number {
    return this.filteredCourses.reduce((total, course) => total + course.assignments, 0);
  }

  getAverageProgress(): number {
    if (this.filteredCourses.length === 0) return 0;
    const total = this.filteredCourses.reduce((sum, course) => sum + course.progress, 0);
    return Math.round(total / this.filteredCourses.length);
  }

  viewCourseDetails(course: Course): void {
    console.log('Viewing course details:', course);
    // Implement course details modal or navigation
  }

  contactTeacher(course: Course): void {
    console.log('Contacting teacher:', course.teacher);
    // Implement teacher contact functionality
  }

  viewAssignments(course: Course): void {
    console.log('Viewing assignments for:', course.name);
    // Implement assignments view
  }
}
