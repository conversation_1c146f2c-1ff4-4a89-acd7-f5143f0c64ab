# School Management System - Database Setup Guide

## 🗄️ PostgreSQL & PgAdmin Configuration

### Step 1: Configure PgAdmin

1. **Open PgAdmin** (should be installed on your system)

2. **Create Server Connection:**
   - Right-click "Servers" → "Register" → "Server"
   - **General Tab:**
     - Name: `School Management DB`
   - **Connection Tab:**
     - Host: `localhost`
     - Port: `5432`
     - Database: `postgres`
     - Username: `postgres`
     - Password: `[Your PostgreSQL password]`

3. **Test Connection** - Click "Save" and ensure it connects successfully

### Step 2: Create Database

1. **In PgAdmin:**
   - Right-click your server → "Create" → "Database"
   - Database name: `school_management`
   - Owner: `postgres`
   - Click "Save"

2. **Verify Database Creation:**
   - Expand your server → "Databases"
   - You should see `school_management` listed

### Step 3: Update Application Configuration

1. **Edit the password in:** `backend/school-management-backend/src/main/resources/application.yml`
   
   ```yaml
   spring:
     datasource:
       password: YOUR_ACTUAL_POSTGRESQL_PASSWORD  # Replace this
   ```

### Step 4: Run the Backend

1. **Navigate to backend directory:**
   ```bash
   cd backend/school-management-backend
   ```

2. **Run with Maven:**
   ```bash
   mvn spring-boot:run
   ```

3. **Or build and run JAR:**
   ```bash
   mvn clean package
   java -jar target/school-management-backend-1.0.0.jar
   ```

### Step 5: Test the Setup

1. **Health Check:**
   - URL: http://localhost:2023/api/test/health
   - Should return: `{"status": "UP", "message": "School Management System API is running"}`

2. **Database Check:**
   - URL: http://localhost:2023/api/test/database
   - Should return: `{"database": "Connected", "status": "SUCCESS"}`

3. **System Info:**
   - URL: http://localhost:2023/api/test/info
   - Shows application details

### Step 6: View Database Tables in PgAdmin

1. **After running the backend once:**
   - In PgAdmin: `school_management` → "Schemas" → "public" → "Tables"
   - You should see the `users` table created automatically

2. **View Table Structure:**
   - Right-click `users` table → "View/Edit Data" → "All Rows"

### Common Issues & Solutions

**Issue 1: Connection Refused**
- Ensure PostgreSQL service is running
- Check if port 5432 is available
- Verify username/password

**Issue 2: Database Not Found**
- Make sure you created the `school_management` database
- Check database name spelling in application.yml

**Issue 3: Permission Denied**
- Ensure the PostgreSQL user has proper permissions
- Try connecting as superuser first

### Next Steps

1. ✅ Database connected
2. ✅ Basic API endpoints working
3. 🔄 Add more entities (Student, Teacher, Course, etc.)
4. 🔄 Implement authentication
5. 🔄 Connect Angular frontend

### Useful PgAdmin Features

- **Query Tool:** Execute custom SQL queries
- **Dashboard:** Monitor database performance
- **Backup/Restore:** Manage database backups
- **User Management:** Create additional database users
