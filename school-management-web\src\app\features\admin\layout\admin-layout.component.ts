import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterOutlet } from '@angular/router';

@Component({
  selector: 'app-admin-layout',
  standalone: true,
  imports: [CommonModule, RouterOutlet],
  template: `
    <div class="admin-layout">
      <h1>Admin Portal</h1>
      <router-outlet></router-outlet>
    </div>
  `,
  styles: [`
    .admin-layout {
      padding: 20px;
      min-height: 100vh;
      background-color: #f8f9fa;
    }
    h1 {
      color: #333;
      margin-bottom: 20px;
    }
  `]
})
export class AdminLayoutComponent {}
