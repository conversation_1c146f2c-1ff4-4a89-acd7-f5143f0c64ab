# Test PostgreSQL Connection Script
Write-Host "🔍 Testing PostgreSQL Connection..." -ForegroundColor Yellow

# Test if PostgreSQL service is running
$service = Get-Service -Name "postgresql-x64-16" -ErrorAction SilentlyContinue
if ($service) {
    Write-Host "✅ PostgreSQL Service Status: $($service.Status)" -ForegroundColor Green
} else {
    Write-Host "❌ PostgreSQL service not found" -ForegroundColor Red
    exit 1
}

# Test port connectivity
Write-Host "🔍 Testing port 5432..." -ForegroundColor Yellow
try {
    $connection = Test-NetConnection -ComputerName "localhost" -Port 5432 -WarningAction SilentlyContinue
    if ($connection.TcpTestSucceeded) {
        Write-Host "✅ Port 5432 is accessible" -ForegroundColor Green
    } else {
        Write-Host "❌ Port 5432 is not accessible" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Error testing port: $($_.Exception.Message)" -ForegroundColor Red
}

# Test psql command if available
Write-Host "🔍 Testing psql command..." -ForegroundColor Yellow
try {
    $psqlPath = Get-Command psql -ErrorAction SilentlyContinue
    if ($psqlPath) {
        Write-Host "✅ psql found at: $($psqlPath.Source)" -ForegroundColor Green
        
        # Test connection with psql
        Write-Host "🔍 Testing database connection..." -ForegroundColor Yellow
        $env:PGPASSWORD = "root"
        $result = & psql -h localhost -U postgres -d postgres -c "SELECT version();" 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Database connection successful!" -ForegroundColor Green
            Write-Host "📊 PostgreSQL Version:" -ForegroundColor Cyan
            Write-Host $result -ForegroundColor White
        } else {
            Write-Host "❌ Database connection failed:" -ForegroundColor Red
            Write-Host $result -ForegroundColor Red
        }
    } else {
        Write-Host "⚠️  psql command not found in PATH" -ForegroundColor Yellow
        Write-Host "💡 You can still use pgAdmin to connect" -ForegroundColor Cyan
    }
} catch {
    Write-Host "❌ Error testing psql: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n📋 Next Steps:" -ForegroundColor Yellow
Write-Host "1. Open pgAdmin 4 from Start menu" -ForegroundColor White
Write-Host "2. Create server connection with:" -ForegroundColor White
Write-Host "   - Host: localhost" -ForegroundColor Cyan
Write-Host "   - Port: 5432" -ForegroundColor Cyan
Write-Host "   - Username: postgres" -ForegroundColor Cyan
Write-Host "   - Password: root" -ForegroundColor Cyan
Write-Host "3. Create database: school_management" -ForegroundColor White
Write-Host "4. Run Spring Boot backend" -ForegroundColor White
