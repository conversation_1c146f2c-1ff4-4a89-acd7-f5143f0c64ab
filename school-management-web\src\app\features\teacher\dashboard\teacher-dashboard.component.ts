import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';

@Component({
  selector: 'app-teacher-dashboard',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="teacher-dashboard">
      <!-- Welcome Header -->
      <section class="welcome-section">
        <div class="welcome-content">
          <div class="welcome-text">
            <h1>👋 Welcome back, <PERSON>!</h1>
            <p>Ready to inspire young minds today? Here's your overview for {{currentDate | date:'fullDate'}}</p>
          </div>
          <div class="welcome-stats">
            <div class="stat-item">
              <span class="stat-number">{{todayClasses}}</span>
              <span class="stat-label">Classes Today</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">{{totalStudents}}</span>
              <span class="stat-label">Total Students</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">{{pendingGrades}}</span>
              <span class="stat-label">Pending Grades</span>
            </div>
          </div>
        </div>
      </section>

      <!-- Quick Actions -->
      <section class="quick-actions-section">
        <h2>⚡ Quick Actions</h2>
        <div class="actions-grid">
          <button class="action-card" (click)="navigateToClasses()">
            <div class="action-icon classes-icon">🏫</div>
            <div class="action-content">
              <h3>My Classes</h3>
              <p>View and manage your classes</p>
              <span class="action-badge">{{myClasses.length}} classes</span>
            </div>
          </button>

          <button class="action-card" (click)="navigateToGrades()">
            <div class="action-icon grades-icon">📊</div>
            <div class="action-content">
              <h3>Grade Management</h3>
              <p>Enter and review student grades</p>
              <span class="action-badge">{{pendingGrades}} pending</span>
            </div>
          </button>

          <button class="action-card" (click)="navigateToAttendance()">
            <div class="action-icon attendance-icon">📋</div>
            <div class="action-content">
              <h3>Attendance</h3>
              <p>Mark student attendance</p>
              <span class="action-badge">Today's classes</span>
            </div>
          </button>

          <button class="action-card" (click)="navigateToExercises()">
            <div class="action-icon exercises-icon">📝</div>
            <div class="action-content">
              <h3>Exercises</h3>
              <p>Create and manage exercises</p>
              <span class="action-badge">{{totalExercises}} exercises</span>
            </div>
          </button>
        </div>
      </section>

      <!-- Today's Schedule -->
      <section class="schedule-section">
        <h2>📅 Today's Schedule</h2>
        <div class="schedule-container">
          <div class="schedule-item" *ngFor="let class of todaySchedule">
            <div class="schedule-time">
              <span class="time">{{class.time}}</span>
              <span class="duration">{{class.duration}}</span>
            </div>
            <div class="schedule-details">
              <h4>{{class.subject}}</h4>
              <p>{{class.className}} • {{class.room}}</p>
              <span class="schedule-status" [ngClass]="class.status">{{class.status}}</span>
            </div>
            <div class="schedule-actions">
              <button class="schedule-btn" (click)="markAttendance(class)">
                <span class="btn-icon">📋</span>
                Attendance
              </button>
              <button class="schedule-btn" (click)="viewClass(class)">
                <span class="btn-icon">👁️</span>
                View
              </button>
            </div>
          </div>
        </div>
      </section>

      <!-- Recent Activity & Notifications -->
      <div class="bottom-sections">
        <!-- Recent Activity -->
        <section class="activity-section">
          <h2>📈 Recent Activity</h2>
          <div class="activity-list">
            <div class="activity-item" *ngFor="let activity of recentActivities">
              <div class="activity-icon" [ngClass]="activity.type">{{activity.icon}}</div>
              <div class="activity-content">
                <p class="activity-text">{{activity.description}}</p>
                <span class="activity-time">{{activity.time}}</span>
              </div>
            </div>
          </div>
        </section>

        <!-- Notifications -->
        <section class="notifications-section">
          <h2>🔔 Notifications</h2>
          <div class="notifications-list">
            <div class="notification-item" *ngFor="let notification of notifications" [ngClass]="notification.priority">
              <div class="notification-content">
                <h4>{{notification.title}}</h4>
                <p>{{notification.message}}</p>
                <span class="notification-time">{{notification.time}}</span>
              </div>
              <button class="notification-close" (click)="dismissNotification(notification)">✕</button>
            </div>
          </div>
        </section>
      </div>

      <!-- Class Overview -->
      <section class="classes-overview-section">
        <h2>🏫 My Classes Overview</h2>
        <div class="classes-grid">
          <div class="class-card" *ngFor="let class of myClasses">
            <div class="class-header">
              <div class="class-info">
                <h3>{{class.subject}}</h3>
                <p>{{class.className}} • {{class.students}} students</p>
              </div>
              <div class="class-grade">{{class.grade}}</div>
            </div>
            <div class="class-stats">
              <div class="stat">
                <span class="stat-value">{{class.averageGrade}}%</span>
                <span class="stat-label">Avg Grade</span>
              </div>
              <div class="stat">
                <span class="stat-value">{{class.attendance}}%</span>
                <span class="stat-label">Attendance</span>
              </div>
              <div class="stat">
                <span class="stat-value">{{class.assignments}}</span>
                <span class="stat-label">Assignments</span>
              </div>
            </div>
            <div class="class-actions">
              <button class="class-btn primary" (click)="viewClassDetails(class)">View Details</button>
              <button class="class-btn secondary" (click)="manageClass(class)">Manage</button>
            </div>
          </div>
        </div>
      </section>
    </div>
  `,
  styles: [`
    .teacher-dashboard {
      padding: 0;
      background: #f5f7fa;
      min-height: 100vh;
    }

    .welcome-section {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 40px 30px;
      margin: -30px -30px 30px -30px;
      border-radius: 0 0 20px 20px;
    }

    .welcome-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      max-width: 1400px;
      margin: 0 auto;
    }

    .welcome-text h1 {
      margin: 0 0 8px 0;
      font-size: 32px;
      font-weight: 600;
    }

    .welcome-text p {
      margin: 0;
      font-size: 16px;
      opacity: 0.9;
    }

    .welcome-stats {
      display: flex;
      gap: 30px;
    }

    .stat-item {
      text-align: center;
    }

    .stat-number {
      display: block;
      font-size: 28px;
      font-weight: 700;
      margin-bottom: 4px;
    }

    .stat-label {
      font-size: 14px;
      opacity: 0.8;
    }

    .quick-actions-section {
      margin-bottom: 40px;
    }

    .quick-actions-section h2 {
      margin: 0 0 24px 0;
      color: #2d3748;
      font-size: 24px;
    }

    .actions-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 24px;
      margin-bottom: 40px;
    }

    .action-card {
      background: white;
      border: none;
      border-radius: 16px;
      padding: 24px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
      transition: all 0.3s ease;
      cursor: pointer;
      text-align: left;
      display: flex;
      align-items: center;
      gap: 20px;
    }

    .action-card:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .action-icon {
      width: 60px;
      height: 60px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      flex-shrink: 0;
    }

    .classes-icon { background: #e3f2fd; }
    .grades-icon { background: #f3e5f5; }
    .attendance-icon { background: #e8f5e8; }
    .exercises-icon { background: #fff3e0; }

    .action-content h3 {
      margin: 0 0 8px 0;
      color: #2d3748;
      font-size: 18px;
    }

    .action-content p {
      margin: 0 0 12px 0;
      color: #718096;
      font-size: 14px;
    }

    .action-badge {
      background: #667eea;
      color: white;
      padding: 4px 12px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 600;
    }

    .schedule-section {
      margin-bottom: 40px;
    }

    .schedule-section h2 {
      margin: 0 0 24px 0;
      color: #2d3748;
      font-size: 24px;
    }

    .schedule-container {
      background: white;
      border-radius: 16px;
      padding: 24px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    }

    .schedule-item {
      display: flex;
      align-items: center;
      gap: 20px;
      padding: 20px 0;
      border-bottom: 1px solid #e2e8f0;
    }

    .schedule-item:last-child {
      border-bottom: none;
    }

    .schedule-time {
      display: flex;
      flex-direction: column;
      align-items: center;
      min-width: 80px;
    }

    .time {
      font-weight: 600;
      color: #2d3748;
      font-size: 16px;
    }

    .duration {
      font-size: 12px;
      color: #718096;
    }

    .schedule-details {
      flex: 1;
    }

    .schedule-details h4 {
      margin: 0 0 4px 0;
      color: #2d3748;
      font-size: 16px;
    }

    .schedule-details p {
      margin: 0 0 8px 0;
      color: #718096;
      font-size: 14px;
    }

    .schedule-status {
      padding: 4px 12px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 600;
    }

    .schedule-status.upcoming {
      background: #e3f2fd;
      color: #1976d2;
    }

    .schedule-status.current {
      background: #e8f5e8;
      color: #388e3c;
    }

    .schedule-status.completed {
      background: #f5f5f5;
      color: #757575;
    }

    .schedule-actions {
      display: flex;
      gap: 12px;
    }

    .schedule-btn {
      background: #f8fafc;
      border: 1px solid #e2e8f0;
      padding: 8px 16px;
      border-radius: 8px;
      cursor: pointer;
      font-size: 14px;
      display: flex;
      align-items: center;
      gap: 6px;
      transition: all 0.3s ease;
    }

    .schedule-btn:hover {
      background: #667eea;
      color: white;
      border-color: #667eea;
    }

    .bottom-sections {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 30px;
      margin-bottom: 40px;
    }

    .activity-section,
    .notifications-section {
      background: white;
      border-radius: 16px;
      padding: 24px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    }

    .activity-section h2,
    .notifications-section h2 {
      margin: 0 0 20px 0;
      color: #2d3748;
      font-size: 20px;
    }

    .activity-item,
    .notification-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px 0;
      border-bottom: 1px solid #f7fafc;
    }

    .activity-item:last-child,
    .notification-item:last-child {
      border-bottom: none;
    }

    .activity-icon {
      width: 32px;
      height: 32px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
    }

    .activity-icon.grade { background: #f3e5f5; }
    .activity-icon.attendance { background: #e8f5e8; }
    .activity-icon.exercise { background: #fff3e0; }

    .activity-text {
      margin: 0 0 4px 0;
      color: #2d3748;
      font-size: 14px;
    }

    .activity-time {
      font-size: 12px;
      color: #718096;
    }

    .notification-item {
      justify-content: space-between;
      align-items: flex-start;
    }

    .notification-item.high {
      background: #fef5e7;
      border-left: 4px solid #f6ad55;
      padding-left: 16px;
      margin-left: -20px;
    }

    .notification-content h4 {
      margin: 0 0 4px 0;
      color: #2d3748;
      font-size: 14px;
    }

    .notification-content p {
      margin: 0 0 4px 0;
      color: #718096;
      font-size: 13px;
    }

    .notification-time {
      font-size: 12px;
      color: #a0aec0;
    }

    .notification-close {
      background: none;
      border: none;
      cursor: pointer;
      color: #a0aec0;
      font-size: 16px;
    }

    .classes-overview-section h2 {
      margin: 0 0 24px 0;
      color: #2d3748;
      font-size: 24px;
    }

    .classes-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
      gap: 24px;
    }

    .class-card {
      background: white;
      border-radius: 16px;
      padding: 24px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
      transition: transform 0.3s ease;
    }

    .class-card:hover {
      transform: translateY(-2px);
    }

    .class-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 20px;
    }

    .class-info h3 {
      margin: 0 0 4px 0;
      color: #2d3748;
      font-size: 18px;
    }

    .class-info p {
      margin: 0;
      color: #718096;
      font-size: 14px;
    }

    .class-grade {
      background: #667eea;
      color: white;
      padding: 8px 12px;
      border-radius: 8px;
      font-weight: 600;
      font-size: 14px;
    }

    .class-stats {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;
    }

    .stat {
      text-align: center;
    }

    .stat-value {
      display: block;
      font-size: 20px;
      font-weight: 600;
      color: #2d3748;
      margin-bottom: 4px;
    }

    .stat-label {
      font-size: 12px;
      color: #718096;
    }

    .class-actions {
      display: flex;
      gap: 12px;
    }

    .class-btn {
      flex: 1;
      padding: 10px 16px;
      border-radius: 8px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: all 0.3s ease;
    }

    .class-btn.primary {
      background: #667eea;
      color: white;
      border: none;
    }

    .class-btn.primary:hover {
      background: #5a67d8;
    }

    .class-btn.secondary {
      background: white;
      color: #667eea;
      border: 1px solid #667eea;
    }

    .class-btn.secondary:hover {
      background: #667eea;
      color: white;
    }

    @media (max-width: 768px) {
      .welcome-content {
        flex-direction: column;
        gap: 20px;
        text-align: center;
      }

      .welcome-stats {
        justify-content: center;
      }

      .actions-grid {
        grid-template-columns: 1fr;
      }

      .action-card {
        flex-direction: column;
        text-align: center;
      }

      .bottom-sections {
        grid-template-columns: 1fr;
      }

      .schedule-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
      }

      .schedule-actions {
        width: 100%;
        justify-content: space-between;
      }

      .classes-grid {
        grid-template-columns: 1fr;
      }
    }
  `]
})
export class TeacherDashboardComponent {
  currentDate = new Date();
  todayClasses = 4;
  totalStudents = 125;
  pendingGrades = 12;
  totalExercises = 28;

  todaySchedule = [
    {
      time: '08:00',
      duration: '50 min',
      subject: 'Mathematics',
      className: 'Grade 5A',
      room: 'Room 101',
      status: 'completed'
    },
    {
      time: '09:30',
      duration: '50 min',
      subject: 'Algebra',
      className: 'Grade 5B',
      room: 'Room 101',
      status: 'current'
    },
    {
      time: '11:00',
      duration: '50 min',
      subject: 'Mathematics',
      className: 'Grade 4A',
      room: 'Room 101',
      status: 'upcoming'
    },
    {
      time: '14:00',
      duration: '50 min',
      subject: 'Advanced Math',
      className: 'Grade 6A',
      room: 'Room 101',
      status: 'upcoming'
    }
  ];

  recentActivities = [
    {
      icon: '📊',
      type: 'grade',
      description: 'Graded 25 math assignments for Grade 5A',
      time: '2 hours ago'
    },
    {
      icon: '📋',
      type: 'attendance',
      description: 'Marked attendance for Grade 5B',
      time: '3 hours ago'
    },
    {
      icon: '📝',
      type: 'exercise',
      description: 'Created new algebra exercise set',
      time: '1 day ago'
    }
  ];

  notifications = [
    {
      title: 'Parent Meeting Scheduled',
      message: 'Meeting with John Smith\'s parents tomorrow at 3 PM',
      time: '1 hour ago',
      priority: 'high'
    },
    {
      title: 'Grade Submission Deadline',
      message: 'Submit grades for Grade 5A by Friday',
      time: '2 hours ago',
      priority: 'medium'
    }
  ];

  myClasses = [
    {
      subject: 'Mathematics',
      className: 'Grade 5A',
      grade: '5A',
      students: 28,
      averageGrade: 85,
      attendance: 92,
      assignments: 12
    },
    {
      subject: 'Algebra',
      className: 'Grade 5B',
      grade: '5B',
      students: 26,
      averageGrade: 78,
      attendance: 88,
      assignments: 10
    },
    {
      subject: 'Mathematics',
      className: 'Grade 4A',
      grade: '4A',
      students: 24,
      averageGrade: 82,
      attendance: 95,
      assignments: 8
    }
  ];

  constructor(private router: Router) {}

  navigateToClasses(): void {
    this.router.navigate(['/teacher/classes']);
  }

  navigateToGrades(): void {
    this.router.navigate(['/teacher/grades']);
  }

  navigateToAttendance(): void {
    this.router.navigate(['/teacher/attendance']);
  }

  navigateToExercises(): void {
    this.router.navigate(['/teacher/exercises']);
  }

  markAttendance(classItem: any): void {
    console.log('Mark attendance for:', classItem);
    this.router.navigate(['/teacher/attendance'], { queryParams: { class: classItem.className } });
  }

  viewClass(classItem: any): void {
    console.log('View class:', classItem);
    this.router.navigate(['/teacher/classes'], { queryParams: { class: classItem.className } });
  }

  dismissNotification(notification: any): void {
    this.notifications = this.notifications.filter(n => n !== notification);
  }

  viewClassDetails(classItem: any): void {
    console.log('View class details:', classItem);
    this.router.navigate(['/teacher/classes'], { queryParams: { class: classItem.className } });
  }

  manageClass(classItem: any): void {
    console.log('Manage class:', classItem);
    this.router.navigate(['/teacher/classes'], { queryParams: { class: classItem.className, action: 'manage' } });
  }
}
