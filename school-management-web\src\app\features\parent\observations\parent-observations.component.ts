import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';

interface Observation {
  id: number;
  title: string;
  description: string;
  teacher: string;
  subject: string;
  date: Date;
  type: 'positive' | 'neutral' | 'concern';
  childName: string;
  category: 'academic' | 'behavior' | 'social' | 'participation';
}

@Component({
  selector: 'app-parent-observations',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="observations-container">
      <!-- Header -->
      <div class="observations-header">
        <div class="header-content">
          <h1>👁️ Teacher Observations</h1>
          <p>View teacher observations and feedback about your children</p>
        </div>
        <div class="header-stats">
          <div class="stat-card positive">
            <div class="stat-number">{{getPositiveCount()}}</div>
            <div class="stat-label">Positive</div>
          </div>
          <div class="stat-card neutral">
            <div class="stat-number">{{getNeutralCount()}}</div>
            <div class="stat-label">Neutral</div>
          </div>
          <div class="stat-card concern">
            <div class="stat-number">{{getConcernCount()}}</div>
            <div class="stat-label">Concerns</div>
          </div>
        </div>
      </div>

      <!-- Observations List -->
      <div class="observations-list">
        <div *ngFor="let observation of observations" class="observation-card" [class]="observation.type">
          <div class="observation-header">
            <div class="observation-info">
              <div class="observation-title">{{observation.title}}</div>
              <div class="observation-meta">
                <span class="observation-teacher">👨‍🏫 {{observation.teacher}}</span>
                <span class="observation-subject">📚 {{observation.subject}}</span>
                <span class="observation-child">👤 {{observation.childName}}</span>
                <span class="observation-category">{{getCategoryIcon(observation.category)}} {{observation.category}}</span>
              </div>
            </div>
            <div class="observation-badge" [class]="observation.type">
              {{getTypeIcon(observation.type)}} {{getTypeText(observation.type)}}
            </div>
          </div>

          <div class="observation-description">{{observation.description}}</div>

          <div class="observation-footer">
            <div class="observation-date">📅 {{observation.date | date:'MMM dd, yyyy'}}</div>
            <div class="observation-actions">
              <button class="action-btn" (click)="viewDetails(observation)">View Details</button>
              <button class="action-btn secondary" (click)="contactTeacher(observation)">Contact Teacher</button>
            </div>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div *ngIf="observations.length === 0" class="empty-state">
        <div class="empty-icon">👁️</div>
        <h3>No observations yet</h3>
        <p>Teacher observations will appear here when available.</p>
      </div>
    </div>
  `,
  styles: [`
    .observations-container {
      min-height: 100vh;
      background: #f5f7fa;
      padding: 20px;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .observations-header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 30px;
      border-radius: 12px;
      margin-bottom: 30px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;
      gap: 20px;
    }

    .header-content h1 {
      margin: 0 0 8px 0;
      font-size: 28px;
      font-weight: 600;
    }

    .header-content p {
      margin: 0;
      opacity: 0.9;
      font-size: 16px;
    }

    .header-stats {
      display: flex;
      gap: 20px;
    }

    .stat-card {
      background: rgba(255, 255, 255, 0.2);
      padding: 16px;
      border-radius: 8px;
      text-align: center;
      min-width: 80px;
    }

    .stat-number {
      font-size: 24px;
      font-weight: 600;
      margin-bottom: 4px;
    }

    .stat-label {
      font-size: 12px;
      opacity: 0.9;
    }

    .observations-list {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    .observation-card {
      background: white;
      padding: 20px;
      border-radius: 12px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
      border-left: 4px solid #28a745;
      transition: all 0.3s ease;
    }

    .observation-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .observation-card.neutral {
      border-left-color: #6c757d;
    }

    .observation-card.concern {
      border-left-color: #dc3545;
    }

    .observation-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 16px;
    }

    .observation-title {
      font-size: 18px;
      font-weight: 600;
      color: #333;
      margin-bottom: 8px;
    }

    .observation-meta {
      display: flex;
      gap: 16px;
      font-size: 14px;
      color: #666;
      flex-wrap: wrap;
    }

    .observation-badge {
      padding: 6px 12px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 500;
    }

    .observation-badge.positive {
      background: #d4edda;
      color: #155724;
    }

    .observation-badge.neutral {
      background: #e2e3e5;
      color: #383d41;
    }

    .observation-badge.concern {
      background: #f8d7da;
      color: #721c24;
    }

    .observation-description {
      color: #666;
      line-height: 1.6;
      margin-bottom: 16px;
    }

    .observation-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;
      gap: 12px;
    }

    .observation-date {
      color: #666;
      font-size: 14px;
    }

    .observation-actions {
      display: flex;
      gap: 8px;
    }

    .action-btn {
      padding: 6px 12px;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 12px;
      background: #667eea;
      color: white;
      transition: all 0.3s ease;
    }

    .action-btn.secondary {
      background: #6c757d;
    }

    .action-btn:hover {
      opacity: 0.8;
    }

    .empty-state {
      text-align: center;
      padding: 60px 20px;
      color: #666;
    }

    .empty-icon {
      font-size: 64px;
      margin-bottom: 16px;
    }

    @media (max-width: 768px) {
      .observations-header {
        flex-direction: column;
        align-items: stretch;
      }

      .header-stats {
        justify-content: center;
      }

      .observation-header {
        flex-direction: column;
        gap: 12px;
      }

      .observation-meta {
        flex-direction: column;
        gap: 8px;
      }
    }
  `]
})
export class ParentObservationsComponent implements OnInit {
  observations: Observation[] = [];

  ngOnInit(): void {
    this.loadObservations();
  }

  loadObservations(): void {
    this.observations = [
      {
        id: 1,
        title: 'Excellent Math Performance',
        description: 'Sarah showed exceptional understanding of algebraic concepts today. She helped other students and demonstrated strong problem-solving skills.',
        teacher: 'Dr. Sarah Wilson',
        subject: 'Mathematics',
        date: new Date('2024-01-15'),
        type: 'positive',
        childName: 'Sarah Johnson',
        category: 'academic'
      },
      {
        id: 2,
        title: 'Great Teamwork in Science Lab',
        description: 'Michael worked very well with his lab partner during the chemistry experiment. He showed leadership and cooperation skills.',
        teacher: 'Mr. John Smith',
        subject: 'Science',
        date: new Date('2024-01-12'),
        type: 'positive',
        childName: 'Michael Johnson',
        category: 'social'
      },
      {
        id: 3,
        title: 'Needs Improvement in Participation',
        description: 'Sarah has been quiet in English class discussions. Encouraging her to participate more actively would benefit her learning.',
        teacher: 'Ms. Emily Davis',
        subject: 'English',
        date: new Date('2024-01-10'),
        type: 'concern',
        childName: 'Sarah Johnson',
        category: 'participation'
      },
      {
        id: 4,
        title: 'Consistent Homework Completion',
        description: 'Michael has been consistently completing his homework on time and with good quality. Keep up the good work!',
        teacher: 'Dr. Sarah Wilson',
        subject: 'Mathematics',
        date: new Date('2024-01-08'),
        type: 'positive',
        childName: 'Michael Johnson',
        category: 'academic'
      }
    ];
  }

  getPositiveCount(): number {
    return this.observations.filter(o => o.type === 'positive').length;
  }

  getNeutralCount(): number {
    return this.observations.filter(o => o.type === 'neutral').length;
  }

  getConcernCount(): number {
    return this.observations.filter(o => o.type === 'concern').length;
  }

  getTypeIcon(type: string): string {
    switch (type) {
      case 'positive': return '✅';
      case 'neutral': return 'ℹ️';
      case 'concern': return '⚠️';
      default: return '📝';
    }
  }

  getTypeText(type: string): string {
    switch (type) {
      case 'positive': return 'Positive';
      case 'neutral': return 'Neutral';
      case 'concern': return 'Concern';
      default: return type;
    }
  }

  getCategoryIcon(category: string): string {
    switch (category) {
      case 'academic': return '📚';
      case 'behavior': return '🎭';
      case 'social': return '👥';
      case 'participation': return '🙋';
      default: return '📝';
    }
  }

  viewDetails(observation: Observation): void {
    console.log('View details:', observation);
  }

  contactTeacher(observation: Observation): void {
    console.log('Contact teacher:', observation);
  }
}
