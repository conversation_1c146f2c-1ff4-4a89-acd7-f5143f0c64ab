import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';

interface Payment {
  id: number;
  description: string;
  amount: number;
  dueDate: Date;
  paidDate?: Date;
  status: 'pending' | 'paid' | 'overdue';
  childName: string;
  type: 'tuition' | 'lunch' | 'transport' | 'activity' | 'other';
}

@Component({
  selector: 'app-parent-payments',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="payments-container">
      <!-- Header -->
      <div class="payments-header">
        <div class="header-content">
          <h1>💳 Payments & Fees</h1>
          <p>Manage school fees and payment history</p>
        </div>
        <div class="header-stats">
          <div class="stat-card pending">
            <div class="stat-number">\${{getTotalPending()}}</div>
            <div class="stat-label">Pending</div>
          </div>
          <div class="stat-card paid">
            <div class="stat-number">\${{getTotalPaid()}}</div>
            <div class="stat-label">Paid This Year</div>
          </div>
        </div>
      </div>

      <!-- Payments List -->
      <div class="payments-list">
        <div *ngFor="let payment of payments" class="payment-card" [class]="payment.status">
          <div class="payment-header">
            <div class="payment-info">
              <div class="payment-description">{{payment.description}}</div>
              <div class="payment-meta">
                <span class="payment-type">{{getTypeIcon(payment.type)}} {{payment.type}}</span>
                <span class="payment-child">👤 {{payment.childName}}</span>
              </div>
            </div>
            <div class="payment-amount">\${{payment.amount}}</div>
          </div>

          <div class="payment-details">
            <div class="detail-item">
              <span class="detail-label">Due Date:</span>
              <span class="detail-value" [class]="getDueDateClass(payment)">{{payment.dueDate | date:'MMM dd, yyyy'}}</span>
            </div>
            <div *ngIf="payment.paidDate" class="detail-item">
              <span class="detail-label">Paid Date:</span>
              <span class="detail-value">{{payment.paidDate | date:'MMM dd, yyyy'}}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">Status:</span>
              <span class="detail-value status" [class]="payment.status">{{getStatusText(payment.status)}}</span>
            </div>
          </div>

          <div class="payment-actions">
            <button *ngIf="payment.status === 'pending' || payment.status === 'overdue'"
                    class="action-btn primary" (click)="payNow(payment)">
              💳 Pay Now
            </button>
            <button class="action-btn secondary" (click)="viewDetails(payment)">
              📄 View Details
            </button>
            <button *ngIf="payment.status === 'paid'" class="action-btn info" (click)="downloadReceipt(payment)">
              📥 Download Receipt
            </button>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .payments-container {
      min-height: 100vh;
      background: #f5f7fa;
      padding: 20px;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .payments-header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 30px;
      border-radius: 12px;
      margin-bottom: 30px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;
      gap: 20px;
    }

    .header-content h1 {
      margin: 0 0 8px 0;
      font-size: 28px;
      font-weight: 600;
    }

    .header-content p {
      margin: 0;
      opacity: 0.9;
      font-size: 16px;
    }

    .header-stats {
      display: flex;
      gap: 20px;
    }

    .stat-card {
      background: rgba(255, 255, 255, 0.2);
      padding: 16px;
      border-radius: 8px;
      text-align: center;
      min-width: 120px;
    }

    .stat-number {
      font-size: 24px;
      font-weight: 600;
      margin-bottom: 4px;
    }

    .stat-label {
      font-size: 12px;
      opacity: 0.9;
    }

    .payments-list {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    .payment-card {
      background: white;
      padding: 20px;
      border-radius: 12px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
      border-left: 4px solid #28a745;
      transition: all 0.3s ease;
    }

    .payment-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .payment-card.pending {
      border-left-color: #ffc107;
    }

    .payment-card.overdue {
      border-left-color: #dc3545;
    }

    .payment-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 16px;
    }

    .payment-description {
      font-size: 18px;
      font-weight: 600;
      color: #333;
      margin-bottom: 8px;
    }

    .payment-meta {
      display: flex;
      gap: 16px;
      font-size: 14px;
      color: #666;
    }

    .payment-amount {
      font-size: 24px;
      font-weight: 600;
      color: #333;
    }

    .payment-details {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 12px;
      margin-bottom: 16px;
    }

    .detail-item {
      display: flex;
      justify-content: space-between;
      padding: 8px 0;
      border-bottom: 1px solid #f0f0f0;
    }

    .detail-label {
      font-weight: 500;
      color: #666;
    }

    .detail-value {
      color: #333;
    }

    .detail-value.overdue {
      color: #dc3545;
      font-weight: 600;
    }

    .detail-value.status.pending {
      color: #ffc107;
      font-weight: 600;
    }

    .detail-value.status.paid {
      color: #28a745;
      font-weight: 600;
    }

    .detail-value.status.overdue {
      color: #dc3545;
      font-weight: 600;
    }

    .payment-actions {
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
    }

    .action-btn {
      padding: 8px 16px;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      transition: all 0.3s ease;
    }

    .action-btn.primary {
      background: #28a745;
      color: white;
    }

    .action-btn.secondary {
      background: #6c757d;
      color: white;
    }

    .action-btn.info {
      background: #17a2b8;
      color: white;
    }

    @media (max-width: 768px) {
      .payments-header {
        flex-direction: column;
        align-items: stretch;
      }

      .header-stats {
        justify-content: center;
      }

      .payment-header {
        flex-direction: column;
        gap: 12px;
      }

      .payment-details {
        grid-template-columns: 1fr;
      }
    }
  `]
})
export class ParentPaymentsComponent implements OnInit {
  payments: Payment[] = [];

  ngOnInit(): void {
    this.loadPayments();
  }

  loadPayments(): void {
    this.payments = [
      {
        id: 1,
        description: 'Monthly Tuition Fee - January 2024',
        amount: 850,
        dueDate: new Date('2024-01-15'),
        status: 'pending',
        childName: 'Sarah Johnson',
        type: 'tuition'
      },
      {
        id: 2,
        description: 'School Lunch Program - January 2024',
        amount: 120,
        dueDate: new Date('2024-01-10'),
        paidDate: new Date('2024-01-08'),
        status: 'paid',
        childName: 'Sarah Johnson',
        type: 'lunch'
      },
      {
        id: 3,
        description: 'Transportation Fee - January 2024',
        amount: 200,
        dueDate: new Date('2024-01-20'),
        status: 'pending',
        childName: 'Michael Johnson',
        type: 'transport'
      },
      {
        id: 4,
        description: 'Art Supplies Fee',
        amount: 45,
        dueDate: new Date('2024-01-05'),
        status: 'overdue',
        childName: 'Michael Johnson',
        type: 'activity'
      }
    ];
  }

  getTotalPending(): number {
    return this.payments
      .filter(p => p.status === 'pending' || p.status === 'overdue')
      .reduce((sum, p) => sum + p.amount, 0);
  }

  getTotalPaid(): number {
    return this.payments
      .filter(p => p.status === 'paid')
      .reduce((sum, p) => sum + p.amount, 0);
  }

  getTypeIcon(type: string): string {
    switch (type) {
      case 'tuition': return '🎓';
      case 'lunch': return '🍽️';
      case 'transport': return '🚌';
      case 'activity': return '🎨';
      default: return '💰';
    }
  }

  getStatusText(status: string): string {
    switch (status) {
      case 'pending': return 'Pending';
      case 'paid': return 'Paid';
      case 'overdue': return 'Overdue';
      default: return status;
    }
  }

  getDueDateClass(payment: Payment): string {
    if (payment.status === 'overdue') return 'overdue';
    const now = new Date();
    const dueDate = new Date(payment.dueDate);
    const timeDiff = dueDate.getTime() - now.getTime();
    const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24));

    if (daysDiff <= 3) return 'urgent';
    return '';
  }

  payNow(payment: Payment): void {
    console.log('Pay now:', payment);
    // Implement payment logic
  }

  viewDetails(payment: Payment): void {
    console.log('View details:', payment);
  }

  downloadReceipt(payment: Payment): void {
    console.log('Download receipt:', payment);
  }
}
