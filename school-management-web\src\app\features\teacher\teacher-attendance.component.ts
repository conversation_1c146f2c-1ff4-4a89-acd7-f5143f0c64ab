import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';

@Component({
  selector: 'app-teacher-attendance',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <div class="teacher-attendance">
      <!-- Header -->
      <header class="page-header">
        <div class="header-content">
          <div class="header-left">
            <h1>📋 Attendance Management</h1>
            <p>Track and manage student attendance for all your classes</p>
          </div>
          <div class="header-actions">
            <button class="action-btn" (click)="markTodayAttendance()">
              <span class="icon">✅</span>
              Mark Today
            </button>
            <button class="action-btn secondary" (click)="exportAttendance()">
              <span class="icon">📊</span>
              Export
            </button>
          </div>
        </div>
      </header>

      <!-- Date and Class Selection -->
      <section class="selection-section">
        <div class="selection-controls">
          <div class="date-selector">
            <label>Select Date:</label>
            <input type="date" [(ngModel)]="selectedDate" (change)="onDateChange()" class="date-input">
          </div>
          <div class="class-selector">
            <label>Select Class:</label>
            <select [(ngModel)]="selectedClass" (change)="onClassChange()" class="class-select">
              <option *ngFor="let class of classes" [value]="class.id">{{class.name}}</option>
            </select>
          </div>
        </div>
      </section>

      <!-- Attendance Marking -->
      <section class="attendance-section" *ngIf="getSelectedClass()">
        <div class="attendance-container">
          <div class="attendance-header">
            <h2>{{getSelectedClass()?.name}} - {{selectedDate | date:'fullDate'}}</h2>
            <div class="attendance-summary">
              <div class="summary-item present">
                <span class="summary-number">{{getPresentCount()}}</span>
                <span class="summary-label">Present</span>
              </div>
              <div class="summary-item absent">
                <span class="summary-number">{{getAbsentCount()}}</span>
                <span class="summary-label">Absent</span>
              </div>
              <div class="summary-item late">
                <span class="summary-number">{{getLateCount()}}</span>
                <span class="summary-label">Late</span>
              </div>
              <div class="summary-item total">
                <span class="summary-number">{{getTotalStudents()}}</span>
                <span class="summary-label">Total</span>
              </div>
            </div>
          </div>

          <div class="attendance-grid">
            <div class="student-attendance-card" *ngFor="let student of getSelectedClass()?.students">
              <div class="student-info">
                <div class="student-avatar">{{student.name.charAt(0)}}</div>
                <div class="student-details">
                  <div class="student-name">{{student.name}}</div>
                  <div class="student-id">ID: {{student.id}}</div>
                  <div class="attendance-rate">{{student.attendanceRate}}% attendance</div>
                </div>
              </div>
              <div class="attendance-controls">
                <button 
                  class="attendance-btn present" 
                  [class.active]="getAttendanceStatus(student.id) === 'present'"
                  (click)="markAttendance(student.id, 'present')">
                  <span class="icon">✅</span>
                  Present
                </button>
                <button 
                  class="attendance-btn absent" 
                  [class.active]="getAttendanceStatus(student.id) === 'absent'"
                  (click)="markAttendance(student.id, 'absent')">
                  <span class="icon">❌</span>
                  Absent
                </button>
                <button 
                  class="attendance-btn late" 
                  [class.active]="getAttendanceStatus(student.id) === 'late'"
                  (click)="markAttendance(student.id, 'late')">
                  <span class="icon">⏰</span>
                  Late
                </button>
              </div>
              <div class="attendance-notes" *ngIf="getAttendanceNotes(student.id)">
                <textarea 
                  [(ngModel)]="attendanceData[getAttendanceKey(student.id)].notes"
                  placeholder="Add notes..."
                  class="notes-input"></textarea>
              </div>
            </div>
          </div>

          <div class="attendance-actions">
            <button class="save-btn" (click)="saveAttendance()">
              <span class="icon">💾</span>
              Save Attendance
            </button>
            <button class="bulk-btn" (click)="markAllPresent()">
              <span class="icon">✅</span>
              Mark All Present
            </button>
            <button class="bulk-btn secondary" (click)="clearAll()">
              <span class="icon">🔄</span>
              Clear All
            </button>
          </div>
        </div>
      </section>

      <!-- Attendance Statistics -->
      <section class="stats-section">
        <h2>📊 Attendance Statistics</h2>
        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-icon">📈</div>
            <div class="stat-content">
              <div class="stat-number">{{getOverallAttendanceRate()}}%</div>
              <div class="stat-label">Overall Rate</div>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">⭐</div>
            <div class="stat-content">
              <div class="stat-number">{{getPerfectAttendanceCount()}}</div>
              <div class="stat-label">Perfect Attendance</div>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">⚠️</div>
            <div class="stat-content">
              <div class="stat-number">{{getLowAttendanceCount()}}</div>
              <div class="stat-label">Low Attendance</div>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">📅</div>
            <div class="stat-content">
              <div class="stat-number">{{getDaysTracked()}}</div>
              <div class="stat-label">Days Tracked</div>
            </div>
          </div>
        </div>
      </section>

      <!-- Recent Attendance History -->
      <section class="history-section">
        <h2>📅 Recent Attendance History</h2>
        <div class="history-table-container">
          <table class="history-table">
            <thead>
              <tr>
                <th>Date</th>
                <th>Class</th>
                <th>Present</th>
                <th>Absent</th>
                <th>Late</th>
                <th>Rate</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let record of getRecentAttendance()">
                <td>{{record.date | date:'shortDate'}}</td>
                <td>{{record.className}}</td>
                <td class="present-count">{{record.present}}</td>
                <td class="absent-count">{{record.absent}}</td>
                <td class="late-count">{{record.late}}</td>
                <td>
                  <span class="rate-badge" [class]="getRateClass(record.rate)">
                    {{record.rate}}%
                  </span>
                </td>
                <td>
                  <button class="action-btn small" (click)="viewRecord(record)">
                    <span class="icon">👁️</span>
                  </button>
                  <button class="action-btn small secondary" (click)="editRecord(record)">
                    <span class="icon">✏️</span>
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </section>
    </div>
  `,
  styles: [`
    .teacher-attendance {
      padding: 0;
      background: #f5f7fa;
      min-height: 100vh;
    }

    .page-header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 30px;
      margin: -30px -30px 30px -30px;
    }

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      max-width: 1400px;
      margin: 0 auto;
    }

    .header-left h1 {
      margin: 0 0 8px 0;
      font-size: 28px;
      font-weight: 600;
    }

    .header-left p {
      margin: 0;
      opacity: 0.9;
      font-size: 16px;
    }

    .header-actions {
      display: flex;
      gap: 12px;
    }

    .action-btn {
      background: rgba(255, 255, 255, 0.2);
      border: none;
      color: white;
      padding: 12px 20px;
      border-radius: 8px;
      cursor: pointer;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 8px;
      transition: all 0.3s ease;
    }

    .action-btn:hover {
      background: rgba(255, 255, 255, 0.3);
    }

    .action-btn.secondary {
      background: transparent;
      border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .action-btn.small {
      padding: 6px 12px;
      font-size: 12px;
      background: #667eea;
      color: white;
      border: none;
      border-radius: 6px;
    }

    .action-btn.small.secondary {
      background: #6c757d;
    }

    .selection-section {
      margin-bottom: 30px;
    }

    .selection-controls {
      display: flex;
      gap: 24px;
      align-items: end;
    }

    .date-selector,
    .class-selector {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    .date-selector label,
    .class-selector label {
      font-weight: 500;
      color: #2d3748;
    }

    .date-input,
    .class-select {
      padding: 12px;
      border: 2px solid #e2e8f0;
      border-radius: 8px;
      font-size: 14px;
      background: white;
    }

    .attendance-section {
      margin-bottom: 40px;
    }

    .attendance-container {
      background: white;
      border-radius: 16px;
      padding: 24px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    }

    .attendance-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;
    }

    .attendance-header h2 {
      margin: 0;
      color: #2d3748;
      font-size: 20px;
    }

    .attendance-summary {
      display: flex;
      gap: 20px;
    }

    .summary-item {
      text-align: center;
      padding: 12px 16px;
      border-radius: 8px;
    }

    .summary-item.present { background: #d4edda; }
    .summary-item.absent { background: #f8d7da; }
    .summary-item.late { background: #fff3cd; }
    .summary-item.total { background: #e3f2fd; }

    .summary-number {
      display: block;
      font-size: 20px;
      font-weight: 700;
      margin-bottom: 4px;
    }

    .summary-label {
      font-size: 12px;
      font-weight: 500;
    }

    .attendance-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
      gap: 20px;
      margin-bottom: 24px;
    }

    .student-attendance-card {
      background: #f8fafc;
      border-radius: 12px;
      padding: 20px;
      border: 2px solid transparent;
      transition: all 0.3s ease;
    }

    .student-attendance-card:hover {
      border-color: #667eea;
    }

    .student-info {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 16px;
    }

    .student-avatar {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      background: #667eea;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      font-size: 18px;
    }

    .student-name {
      font-weight: 600;
      color: #2d3748;
      margin-bottom: 2px;
    }

    .student-id {
      font-size: 12px;
      color: #718096;
      margin-bottom: 2px;
    }

    .attendance-rate {
      font-size: 12px;
      color: #667eea;
      font-weight: 500;
    }

    .attendance-controls {
      display: flex;
      gap: 8px;
      margin-bottom: 12px;
    }

    .attendance-btn {
      flex: 1;
      padding: 8px 12px;
      border: 2px solid #e2e8f0;
      border-radius: 6px;
      background: white;
      cursor: pointer;
      font-size: 12px;
      font-weight: 500;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 4px;
      transition: all 0.3s ease;
    }

    .attendance-btn.present.active {
      background: #d4edda;
      border-color: #28a745;
      color: #155724;
    }

    .attendance-btn.absent.active {
      background: #f8d7da;
      border-color: #dc3545;
      color: #721c24;
    }

    .attendance-btn.late.active {
      background: #fff3cd;
      border-color: #ffc107;
      color: #856404;
    }

    .notes-input {
      width: 100%;
      padding: 8px;
      border: 1px solid #e2e8f0;
      border-radius: 4px;
      font-size: 12px;
      resize: vertical;
      min-height: 60px;
    }

    .attendance-actions {
      display: flex;
      gap: 12px;
      justify-content: center;
      padding-top: 24px;
      border-top: 1px solid #e2e8f0;
    }

    .save-btn {
      background: #28a745;
      border: none;
      color: white;
      padding: 12px 24px;
      border-radius: 8px;
      cursor: pointer;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .bulk-btn {
      background: #667eea;
      border: none;
      color: white;
      padding: 12px 20px;
      border-radius: 8px;
      cursor: pointer;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .bulk-btn.secondary {
      background: #6c757d;
    }

    .stats-section {
      background: white;
      border-radius: 16px;
      padding: 24px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
      margin-bottom: 40px;
    }

    .stats-section h2 {
      margin: 0 0 24px 0;
      color: #2d3748;
      font-size: 20px;
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
    }

    .stat-card {
      background: #f8fafc;
      border-radius: 12px;
      padding: 20px;
      display: flex;
      align-items: center;
      gap: 16px;
    }

    .stat-icon {
      width: 50px;
      height: 50px;
      border-radius: 12px;
      background: #667eea;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
    }

    .stat-number {
      font-size: 24px;
      font-weight: 700;
      color: #2d3748;
      margin-bottom: 4px;
    }

    .stat-label {
      font-size: 14px;
      color: #718096;
    }

    .history-section {
      background: white;
      border-radius: 16px;
      padding: 24px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
      margin-bottom: 40px;
    }

    .history-section h2 {
      margin: 0 0 24px 0;
      color: #2d3748;
      font-size: 20px;
    }

    .history-table-container {
      overflow-x: auto;
    }

    .history-table {
      width: 100%;
      border-collapse: collapse;
      min-width: 600px;
    }

    .history-table th {
      background: #f8fafc;
      padding: 12px;
      text-align: left;
      font-weight: 600;
      color: #2d3748;
      border-bottom: 2px solid #e2e8f0;
    }

    .history-table td {
      padding: 12px;
      border-bottom: 1px solid #f1f3f4;
    }

    .present-count { color: #28a745; font-weight: 600; }
    .absent-count { color: #dc3545; font-weight: 600; }
    .late-count { color: #ffc107; font-weight: 600; }

    .rate-badge {
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 600;
    }

    .rate-badge.excellent { background: #d4edda; color: #155724; }
    .rate-badge.good { background: #cce5ff; color: #004085; }
    .rate-badge.average { background: #fff3cd; color: #856404; }
    .rate-badge.poor { background: #f8d7da; color: #721c24; }

    @media (max-width: 768px) {
      .header-content {
        flex-direction: column;
        gap: 20px;
        text-align: center;
      }

      .attendance-header {
        flex-direction: column;
        gap: 16px;
      }

      .attendance-summary {
        justify-content: center;
        flex-wrap: wrap;
      }

      .attendance-grid {
        grid-template-columns: 1fr;
      }

      .stats-grid {
        grid-template-columns: repeat(2, 1fr);
      }

      .selection-controls {
        flex-direction: column;
        gap: 16px;
      }
    }
  `]
})
export class TeacherAttendanceComponent {
  selectedDate = new Date().toISOString().split('T')[0];
  selectedClass = 1;

  attendanceData: { [key: string]: {
    status: string;
    notes: string;
    timestamp: Date;
    markedBy: string;
    lateMinutes?: number;
    excused?: boolean;
  } } = {};

  attendanceHistory: any[] = [
    {
      date: new Date('2024-01-22'),
      classId: 1,
      className: 'Mathematics 5A',
      records: [
        { studentId: 'S001', status: 'present', timestamp: new Date('2024-01-22T09:00:00') },
        { studentId: 'S002', status: 'present', timestamp: new Date('2024-01-22T09:00:00') },
        { studentId: 'S003', status: 'late', timestamp: new Date('2024-01-22T09:15:00'), lateMinutes: 15 }
      ]
    },
    {
      date: new Date('2024-01-21'),
      classId: 2,
      className: 'Algebra 5B',
      records: [
        { studentId: 'S004', status: 'present', timestamp: new Date('2024-01-21T10:30:00') },
        { studentId: 'S005', status: 'absent', notes: 'Sick leave', excused: true }
      ]
    }
  ];

  classes = [
    {
      id: 1,
      name: 'Mathematics 5A',
      students: [
        { id: 'S001', name: 'John Smith', attendanceRate: 95 },
        { id: 'S002', name: 'Emma Johnson', attendanceRate: 98 },
        { id: 'S003', name: 'Michael Brown', attendanceRate: 85 }
      ]
    },
    {
      id: 2,
      name: 'Algebra 5B',
      students: [
        { id: 'S004', name: 'Sarah Davis', attendanceRate: 90 },
        { id: 'S005', name: 'Alex Wilson', attendanceRate: 82 }
      ]
    }
  ];

  recentAttendance = [
    { date: new Date('2024-01-22'), className: 'Mathematics 5A', present: 26, absent: 2, late: 0, rate: 93 },
    { date: new Date('2024-01-21'), className: 'Algebra 5B', present: 24, absent: 1, late: 1, rate: 92 },
    { date: new Date('2024-01-20'), className: 'Mathematics 5A', present: 28, absent: 0, late: 0, rate: 100 }
  ];

  constructor(private router: Router) {}

  onDateChange(): void {
    this.loadAttendanceData();
  }

  onClassChange(): void {
    this.loadAttendanceData();
  }

  getSelectedClass() {
    return this.classes.find(c => c.id === this.selectedClass);
  }

  getAttendanceKey(studentId: string): string {
    return `${this.selectedDate}-${this.selectedClass}-${studentId}`;
  }

  getAttendanceStatus(studentId: string): string {
    const key = this.getAttendanceKey(studentId);
    return this.attendanceData[key]?.status || '';
  }

  getAttendanceNotes(studentId: string): string {
    const key = this.getAttendanceKey(studentId);
    return this.attendanceData[key]?.notes || '';
  }

  markAttendance(studentId: string, status: string): void {
    const key = this.getAttendanceKey(studentId);
    const now = new Date();

    if (!this.attendanceData[key]) {
      this.attendanceData[key] = {
        status: '',
        notes: '',
        timestamp: now,
        markedBy: 'Sarah Johnson'
      };
    }

    this.attendanceData[key].status = status;
    this.attendanceData[key].timestamp = now;

    // Handle late attendance
    if (status === 'late') {
      this.promptForLateMinutes(studentId, key);
    }

    // Handle absent attendance
    if (status === 'absent') {
      this.promptForAbsentReason(studentId, key);
    }
  }

  promptForLateMinutes(studentId: string, key: string): void {
    const minutes = prompt('How many minutes late?');
    if (minutes && !isNaN(Number(minutes))) {
      this.attendanceData[key].lateMinutes = Number(minutes);
    }
  }

  promptForAbsentReason(studentId: string, key: string): void {
    const reason = prompt('Reason for absence (optional):');
    if (reason) {
      this.attendanceData[key].notes = reason;
      const isExcused = confirm('Is this an excused absence?');
      this.attendanceData[key].excused = isExcused;
    }
  }

  bulkMarkAttendance(status: string): void {
    const selectedClass = this.getSelectedClass();
    if (selectedClass) {
      selectedClass.students.forEach(student => {
        this.markAttendance(student.id, status);
      });
    }
  }

  generateAttendanceReport(): void {
    const selectedClass = this.getSelectedClass();
    if (selectedClass) {
      const report = {
        date: this.selectedDate,
        className: selectedClass.name,
        totalStudents: selectedClass.students.length,
        present: this.getPresentCount(),
        absent: this.getAbsentCount(),
        late: this.getLateCount(),
        attendanceRate: Math.round((this.getPresentCount() / selectedClass.students.length) * 100),
        details: selectedClass.students.map(student => {
          const attendance = this.attendanceData[this.getAttendanceKey(student.id)];
          return {
            studentName: student.name,
            studentId: student.id,
            status: attendance?.status || 'not-marked',
            notes: attendance?.notes || '',
            timestamp: attendance?.timestamp,
            lateMinutes: attendance?.lateMinutes
          };
        })
      };

      console.log('Generated attendance report:', report);
      this.downloadAttendanceReport(report);
    }
  }

  downloadAttendanceReport(report: any): void {
    const csvContent = this.generateAttendanceCSV(report);
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `attendance_${report.className}_${report.date}.csv`;
    link.click();
    window.URL.revokeObjectURL(url);
  }

  generateAttendanceCSV(report: any): string {
    const headers = ['Student Name', 'Student ID', 'Status', 'Time Marked', 'Late Minutes', 'Notes'];
    const rows = report.details.map((detail: any) => [
      detail.studentName,
      detail.studentId,
      detail.status,
      detail.timestamp ? detail.timestamp.toLocaleTimeString() : '',
      detail.lateMinutes || '',
      detail.notes || ''
    ]);

    const csvContent = [headers, ...rows]
      .map(row => row.map((field: any) => `"${field}"`).join(','))
      .join('\n');

    return csvContent;
  }

  sendAttendanceNotifications(): void {
    const absentStudents = this.getAbsentStudents();
    if (absentStudents.length > 0) {
      absentStudents.forEach(student => {
        console.log(`Sending absence notification for ${student.name} to ${student.parentEmail}`);
      });
      alert(`Attendance notifications sent to ${absentStudents.length} parents.`);
    }
  }

  getAbsentStudents(): any[] {
    const selectedClass = this.getSelectedClass();
    if (!selectedClass) return [];

    return selectedClass.students.filter(student => {
      const status = this.getAttendanceStatus(student.id);
      return status === 'absent';
    });
  }

  getPresentCount(): number {
    const selectedClass = this.getSelectedClass();
    if (!selectedClass) return 0;
    return selectedClass.students.filter(s => this.getAttendanceStatus(s.id) === 'present').length;
  }

  getAbsentCount(): number {
    const selectedClass = this.getSelectedClass();
    if (!selectedClass) return 0;
    return selectedClass.students.filter(s => this.getAttendanceStatus(s.id) === 'absent').length;
  }

  getLateCount(): number {
    const selectedClass = this.getSelectedClass();
    if (!selectedClass) return 0;
    return selectedClass.students.filter(s => this.getAttendanceStatus(s.id) === 'late').length;
  }

  getTotalStudents(): number {
    const selectedClass = this.getSelectedClass();
    return selectedClass?.students.length || 0;
  }

  markAllPresent(): void {
    const selectedClass = this.getSelectedClass();
    if (selectedClass) {
      selectedClass.students.forEach(student => {
        this.markAttendance(student.id, 'present');
      });
    }
  }

  clearAll(): void {
    const selectedClass = this.getSelectedClass();
    if (selectedClass) {
      selectedClass.students.forEach(student => {
        const key = this.getAttendanceKey(student.id);
        delete this.attendanceData[key];
      });
    }
  }

  saveAttendance(): void {
    console.log('Saving attendance data:', this.attendanceData);
    // Here you would typically send the data to your backend
  }

  loadAttendanceData(): void {
    // Here you would typically load existing attendance data from your backend
    console.log('Loading attendance data for:', this.selectedDate, this.selectedClass);
  }

  markTodayAttendance(): void {
    this.selectedDate = new Date().toISOString().split('T')[0];
    this.loadAttendanceData();
  }

  exportAttendance(): void {
    console.log('Export attendance data');
  }

  getOverallAttendanceRate(): number {
    const allStudents = this.classes.flatMap(c => c.students);
    if (allStudents.length === 0) return 0;
    return Math.round(allStudents.reduce((sum, student) => sum + student.attendanceRate, 0) / allStudents.length);
  }

  getPerfectAttendanceCount(): number {
    const allStudents = this.classes.flatMap(c => c.students);
    return allStudents.filter(s => s.attendanceRate === 100).length;
  }

  getLowAttendanceCount(): number {
    const allStudents = this.classes.flatMap(c => c.students);
    return allStudents.filter(s => s.attendanceRate < 80).length;
  }

  getDaysTracked(): number {
    return 45; // Mock data
  }

  getRecentAttendance() {
    return this.recentAttendance;
  }

  getRateClass(rate: number): string {
    if (rate >= 95) return 'excellent';
    if (rate >= 85) return 'good';
    if (rate >= 75) return 'average';
    return 'poor';
  }

  viewRecord(record: any): void {
    console.log('View record:', record);
  }

  editRecord(record: any): void {
    console.log('Edit record:', record);
  }
}
