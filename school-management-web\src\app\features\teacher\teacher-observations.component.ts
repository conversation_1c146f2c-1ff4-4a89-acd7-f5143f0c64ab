import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';

@Component({
  selector: 'app-teacher-observations',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <div class="teacher-observations">
      <!-- Header -->
      <header class="page-header">
        <div class="header-content">
          <div class="header-left">
            <h1>👁️ Student Observations</h1>
            <p>Record and track behavioral observations and student progress notes</p>
          </div>
          <div class="header-actions">
            <button class="action-btn" (click)="showAddModal = true">
              <span class="icon">➕</span>
              Add Observation
            </button>
            <button class="action-btn secondary" (click)="exportObservations()">
              <span class="icon">📊</span>
              Export
            </button>
          </div>
        </div>
      </header>

      <!-- Filter Section -->
      <section class="filter-section">
        <div class="filter-controls">
          <select [(ngModel)]="selectedClass" (change)="onFilterChange()" class="filter-select">
            <option value="">All Classes</option>
            <option *ngFor="let class of classes" [value]="class.id">{{class.name}}</option>
          </select>
          <select [(ngModel)]="selectedStudent" (change)="onFilterChange()" class="filter-select">
            <option value="">All Students</option>
            <option *ngFor="let student of getStudentsForClass()" [value]="student.id">{{student.name}}</option>
          </select>
          <select [(ngModel)]="selectedType" (change)="onFilterChange()" class="filter-select">
            <option value="">All Types</option>
            <option value="behavioral">Behavioral</option>
            <option value="academic">Academic</option>
            <option value="social">Social</option>
            <option value="positive">Positive</option>
            <option value="concern">Concern</option>
          </select>
          <input type="date" [(ngModel)]="selectedDate" (change)="onFilterChange()" class="filter-date">
        </div>
      </section>

      <!-- Observations List -->
      <section class="observations-section">
        <div class="observations-grid">
          <div class="observation-card" *ngFor="let observation of getFilteredObservations()">
            <div class="observation-header">
              <div class="observation-type" [ngClass]="observation.type">{{observation.type | titlecase}}</div>
              <div class="observation-date">{{observation.date | date:'shortDate'}}</div>
            </div>
            <div class="observation-content">
              <div class="student-info">
                <div class="student-avatar">{{observation.studentName.charAt(0)}}</div>
                <div class="student-details">
                  <h4>{{observation.studentName}}</h4>
                  <p>{{observation.className}} • ID: {{observation.studentId}}</p>
                </div>
              </div>
              <div class="observation-text">
                <h5>{{observation.title}}</h5>
                <p>{{observation.description}}</p>
              </div>
              <div class="observation-meta">
                <span class="meta-item">
                  <span class="meta-icon">📚</span>
                  {{observation.subject}}
                </span>
                <span class="meta-item">
                  <span class="meta-icon">⏰</span>
                  {{observation.time}}
                </span>
                <span class="meta-item" *ngIf="observation.severity">
                  <span class="meta-icon">⚠️</span>
                  {{observation.severity}}
                </span>
              </div>
              <div class="observation-tags" *ngIf="observation.tags?.length">
                <span class="tag" *ngFor="let tag of observation.tags">{{tag}}</span>
              </div>
            </div>
            <div class="observation-actions">
              <button class="action-btn small" (click)="viewObservation(observation)">
                <span class="icon">👁️</span>
                View
              </button>
              <button class="action-btn small secondary" (click)="editObservation(observation)">
                <span class="icon">✏️</span>
                Edit
              </button>
              <button class="action-btn small" (click)="shareObservation(observation)">
                <span class="icon">📤</span>
                Share
              </button>
            </div>
          </div>
        </div>
      </section>

      <!-- Statistics -->
      <section class="stats-section">
        <h2>📊 Observation Statistics</h2>
        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-icon">📝</div>
            <div class="stat-content">
              <div class="stat-number">{{getTotalObservations()}}</div>
              <div class="stat-label">Total Observations</div>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">⭐</div>
            <div class="stat-content">
              <div class="stat-number">{{getPositiveObservations()}}</div>
              <div class="stat-label">Positive Notes</div>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">⚠️</div>
            <div class="stat-content">
              <div class="stat-number">{{getConcernObservations()}}</div>
              <div class="stat-label">Concerns</div>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">📅</div>
            <div class="stat-content">
              <div class="stat-number">{{getThisWeekObservations()}}</div>
              <div class="stat-label">This Week</div>
            </div>
          </div>
        </div>
      </section>

      <!-- Add Observation Modal -->
      <div class="modal-overlay" *ngIf="showAddModal" (click)="showAddModal = false">
        <div class="modal-content" (click)="$event.stopPropagation()">
          <div class="modal-header">
            <h2>➕ Add New Observation</h2>
            <button class="close-btn" (click)="showAddModal = false">✕</button>
          </div>
          <form class="observation-form">
            <div class="form-row">
              <div class="form-group">
                <label>Student</label>
                <select [(ngModel)]="newObservation.studentId" name="studentId" (change)="onStudentSelect()">
                  <option value="">Select Student</option>
                  <option *ngFor="let student of getAllStudents()" [value]="student.id">
                    {{student.name}} ({{student.className}})
                  </option>
                </select>
              </div>
              <div class="form-group">
                <label>Type</label>
                <select [(ngModel)]="newObservation.type" name="observationType">
                  <option value="">Select Type</option>
                  <option value="behavioral">Behavioral</option>
                  <option value="academic">Academic</option>
                  <option value="social">Social</option>
                  <option value="positive">Positive</option>
                  <option value="concern">Concern</option>
                </select>
              </div>
            </div>
            <div class="form-row">
              <div class="form-group">
                <label>Subject</label>
                <select [(ngModel)]="newObservation.subject" name="subject">
                  <option value="">Select Subject</option>
                  <option value="Mathematics">Mathematics</option>
                  <option value="Science">Science</option>
                  <option value="English">English</option>
                  <option value="General">General</option>
                </select>
              </div>
              <div class="form-group">
                <label>Severity</label>
                <select [(ngModel)]="newObservation.severity" name="severity">
                  <option value="">Select Severity</option>
                  <option value="Low">Low</option>
                  <option value="Medium">Medium</option>
                  <option value="High">High</option>
                </select>
              </div>
            </div>
            <div class="form-group">
              <label>Title</label>
              <input type="text" [(ngModel)]="newObservation.title" name="title" placeholder="Brief observation title">
            </div>
            <div class="form-group">
              <label>Description</label>
              <textarea [(ngModel)]="newObservation.description" name="description" rows="4" 
                placeholder="Detailed observation description..."></textarea>
            </div>
            <div class="form-row">
              <div class="form-group">
                <label>Date</label>
                <input type="date" [(ngModel)]="newObservation.date" name="date">
              </div>
              <div class="form-group">
                <label>Time</label>
                <input type="time" [(ngModel)]="newObservation.time" name="time">
              </div>
            </div>
            <div class="form-group">
              <label>Tags (comma-separated)</label>
              <input type="text" [(ngModel)]="newObservation.tagsString" name="tags" 
                placeholder="e.g., participation, homework, behavior">
            </div>
            <div class="modal-actions">
              <button type="button" class="cancel-btn" (click)="showAddModal = false">Cancel</button>
              <button type="button" class="save-btn" (click)="addObservation()">
                <span class="icon">💾</span>
                Save Observation
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .teacher-observations {
      padding: 0;
      background: #f5f7fa;
      min-height: 100vh;
    }

    .page-header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 30px;
      margin: -30px -30px 30px -30px;
    }

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      max-width: 1400px;
      margin: 0 auto;
    }

    .header-left h1 {
      margin: 0 0 8px 0;
      font-size: 28px;
      font-weight: 600;
    }

    .header-left p {
      margin: 0;
      opacity: 0.9;
      font-size: 16px;
    }

    .header-actions {
      display: flex;
      gap: 12px;
    }

    .action-btn {
      background: rgba(255, 255, 255, 0.2);
      border: none;
      color: white;
      padding: 12px 20px;
      border-radius: 8px;
      cursor: pointer;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 8px;
      transition: all 0.3s ease;
    }

    .action-btn:hover {
      background: rgba(255, 255, 255, 0.3);
    }

    .action-btn.secondary {
      background: transparent;
      border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .action-btn.small {
      padding: 6px 12px;
      font-size: 12px;
      background: #667eea;
      color: white;
      border: none;
      border-radius: 6px;
    }

    .action-btn.small.secondary {
      background: #6c757d;
    }

    .filter-section {
      margin-bottom: 30px;
    }

    .filter-controls {
      display: flex;
      gap: 16px;
      flex-wrap: wrap;
    }

    .filter-select,
    .filter-date {
      padding: 12px;
      border: 2px solid #e2e8f0;
      border-radius: 8px;
      font-size: 14px;
      background: white;
      min-width: 150px;
    }

    .observations-section {
      margin-bottom: 40px;
    }

    .observations-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
      gap: 24px;
    }

    .observation-card {
      background: white;
      border-radius: 16px;
      padding: 24px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
      transition: transform 0.3s ease;
    }

    .observation-card:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .observation-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
    }

    .observation-type {
      padding: 4px 12px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 600;
    }

    .observation-type.behavioral { background: #e3f2fd; color: #1976d2; }
    .observation-type.academic { background: #f3e5f5; color: #7b1fa2; }
    .observation-type.social { background: #e8f5e8; color: #388e3c; }
    .observation-type.positive { background: #e8f5e8; color: #2e7d32; }
    .observation-type.concern { background: #fff3e0; color: #f57c00; }

    .observation-date {
      font-size: 12px;
      color: #718096;
      font-weight: 500;
    }

    .student-info {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 16px;
    }

    .student-avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: #667eea;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
    }

    .student-details h4 {
      margin: 0 0 2px 0;
      color: #2d3748;
      font-size: 16px;
    }

    .student-details p {
      margin: 0;
      color: #718096;
      font-size: 12px;
    }

    .observation-text {
      margin-bottom: 16px;
    }

    .observation-text h5 {
      margin: 0 0 8px 0;
      color: #2d3748;
      font-size: 14px;
      font-weight: 600;
    }

    .observation-text p {
      margin: 0;
      color: #4a5568;
      font-size: 14px;
      line-height: 1.5;
    }

    .observation-meta {
      display: flex;
      flex-wrap: wrap;
      gap: 12px;
      margin-bottom: 12px;
    }

    .meta-item {
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 12px;
      color: #718096;
    }

    .meta-icon {
      font-size: 14px;
    }

    .observation-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 6px;
      margin-bottom: 16px;
    }

    .tag {
      background: #f0f4ff;
      color: #667eea;
      padding: 2px 8px;
      border-radius: 12px;
      font-size: 11px;
      font-weight: 500;
    }

    .observation-actions {
      display: flex;
      gap: 8px;
    }

    .stats-section {
      background: white;
      border-radius: 16px;
      padding: 24px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
      margin-bottom: 40px;
    }

    .stats-section h2 {
      margin: 0 0 24px 0;
      color: #2d3748;
      font-size: 20px;
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
    }

    .stat-card {
      background: #f8fafc;
      border-radius: 12px;
      padding: 20px;
      display: flex;
      align-items: center;
      gap: 16px;
    }

    .stat-icon {
      width: 50px;
      height: 50px;
      border-radius: 12px;
      background: #667eea;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
    }

    .stat-number {
      font-size: 24px;
      font-weight: 700;
      color: #2d3748;
      margin-bottom: 4px;
    }

    .stat-label {
      font-size: 14px;
      color: #718096;
    }

    /* Modal Styles */
    .modal-overlay {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1000;
    }

    .modal-content {
      background: white;
      border-radius: 12px;
      width: 90%;
      max-width: 700px;
      max-height: 90vh;
      overflow-y: auto;
    }

    .modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 24px;
      border-bottom: 1px solid #e2e8f0;
    }

    .modal-header h2 {
      margin: 0;
      color: #2d3748;
    }

    .close-btn {
      background: none;
      border: none;
      font-size: 20px;
      cursor: pointer;
      color: #718096;
    }

    .observation-form {
      padding: 24px;
    }

    .form-row {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 16px;
      margin-bottom: 16px;
    }

    .form-group {
      display: flex;
      flex-direction: column;
      margin-bottom: 16px;
    }

    .form-group label {
      margin-bottom: 8px;
      font-weight: 500;
      color: #2d3748;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
      padding: 12px;
      border: 2px solid #e2e8f0;
      border-radius: 6px;
      font-size: 14px;
    }

    .form-group textarea {
      resize: vertical;
      min-height: 100px;
    }

    .modal-actions {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
      margin-top: 24px;
      padding-top: 24px;
      border-top: 1px solid #e2e8f0;
    }

    .cancel-btn {
      background: #6c757d;
      border: none;
      color: white;
      padding: 12px 24px;
      border-radius: 6px;
      cursor: pointer;
    }

    .save-btn {
      background: #667eea;
      border: none;
      color: white;
      padding: 12px 24px;
      border-radius: 6px;
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    @media (max-width: 768px) {
      .header-content {
        flex-direction: column;
        gap: 20px;
        text-align: center;
      }

      .observations-grid {
        grid-template-columns: 1fr;
      }

      .stats-grid {
        grid-template-columns: repeat(2, 1fr);
      }

      .form-row {
        grid-template-columns: 1fr;
      }

      .filter-controls {
        flex-direction: column;
      }

      .filter-select,
      .filter-date {
        min-width: auto;
      }
    }
  `]
})
export class TeacherObservationsComponent {
  selectedClass = '';
  selectedStudent = '';
  selectedType = '';
  selectedDate = '';
  showAddModal = false;

  newObservation = {
    studentId: '',
    type: '',
    subject: '',
    severity: '',
    title: '',
    description: '',
    date: new Date().toISOString().split('T')[0],
    time: new Date().toTimeString().slice(0, 5),
    tagsString: ''
  };

  classes = [
    {
      id: 1,
      name: 'Mathematics 5A',
      students: [
        { id: 'S001', name: 'John Smith', className: 'Mathematics 5A' },
        { id: 'S002', name: 'Emma Johnson', className: 'Mathematics 5A' },
        { id: 'S003', name: 'Michael Brown', className: 'Mathematics 5A' }
      ]
    },
    {
      id: 2,
      name: 'Algebra 5B',
      students: [
        { id: 'S004', name: 'Sarah Davis', className: 'Algebra 5B' },
        { id: 'S005', name: 'Alex Wilson', className: 'Algebra 5B' }
      ]
    }
  ];

  observations = [
    {
      id: 1,
      studentId: 'S001',
      studentName: 'John Smith',
      className: 'Mathematics 5A',
      type: 'positive',
      subject: 'Mathematics',
      title: 'Excellent Problem Solving',
      description: 'John demonstrated exceptional problem-solving skills during today\'s algebra lesson. He helped other students understand complex concepts.',
      date: new Date('2024-01-22'),
      time: '10:30',
      severity: 'Low',
      tags: ['participation', 'leadership', 'helpful']
    },
    {
      id: 2,
      studentId: 'S002',
      studentName: 'Emma Johnson',
      className: 'Mathematics 5A',
      type: 'concern',
      subject: 'Mathematics',
      title: 'Difficulty with Homework',
      description: 'Emma has been struggling with homework completion. Missing assignments for the past week.',
      date: new Date('2024-01-21'),
      time: '14:00',
      severity: 'Medium',
      tags: ['homework', 'academic']
    },
    {
      id: 3,
      studentId: 'S003',
      studentName: 'Michael Brown',
      className: 'Mathematics 5A',
      type: 'behavioral',
      subject: 'General',
      title: 'Disruptive Behavior',
      description: 'Michael was talking during instruction time and distracting other students.',
      date: new Date('2024-01-20'),
      time: '09:15',
      severity: 'High',
      tags: ['behavior', 'disruption']
    }
  ];

  constructor(private router: Router) {}

  onFilterChange(): void {
    // Filter logic is handled in getFilteredObservations()
  }

  getStudentsForClass() {
    if (!this.selectedClass) return this.getAllStudents();
    const classData = this.classes.find(c => c.id.toString() === this.selectedClass);
    return classData?.students || [];
  }

  getAllStudents() {
    return this.classes.flatMap(c => c.students);
  }

  getFilteredObservations() {
    let filtered = this.observations;

    if (this.selectedClass) {
      const classData = this.classes.find(c => c.id.toString() === this.selectedClass);
      if (classData) {
        const studentIds = classData.students.map(s => s.id);
        filtered = filtered.filter(o => studentIds.includes(o.studentId));
      }
    }

    if (this.selectedStudent) {
      filtered = filtered.filter(o => o.studentId === this.selectedStudent);
    }

    if (this.selectedType) {
      filtered = filtered.filter(o => o.type === this.selectedType);
    }

    if (this.selectedDate) {
      const selectedDateObj = new Date(this.selectedDate);
      filtered = filtered.filter(o => 
        o.date.toDateString() === selectedDateObj.toDateString()
      );
    }

    return filtered;
  }

  onStudentSelect(): void {
    if (this.newObservation.studentId) {
      const student = this.getAllStudents().find(s => s.id === this.newObservation.studentId);
      if (student) {
        // Auto-fill class information
        console.log('Selected student:', student);
      }
    }
  }

  addObservation(): void {
    if (this.newObservation.studentId && this.newObservation.type && this.newObservation.title) {
      const student = this.getAllStudents().find(s => s.id === this.newObservation.studentId);
      if (student) {
        const newObs = {
          id: this.observations.length + 1,
          studentId: this.newObservation.studentId,
          studentName: student.name,
          className: student.className,
          type: this.newObservation.type,
          subject: this.newObservation.subject,
          title: this.newObservation.title,
          description: this.newObservation.description,
          date: new Date(this.newObservation.date),
          time: this.newObservation.time,
          severity: this.newObservation.severity,
          tags: this.newObservation.tagsString.split(',').map(tag => tag.trim()).filter(tag => tag)
        };
        this.observations.push(newObs);
        this.showAddModal = false;
        this.resetNewObservation();
      }
    }
  }

  resetNewObservation(): void {
    this.newObservation = {
      studentId: '',
      type: '',
      subject: '',
      severity: '',
      title: '',
      description: '',
      date: new Date().toISOString().split('T')[0],
      time: new Date().toTimeString().slice(0, 5),
      tagsString: ''
    };
  }

  viewObservation(observation: any): void {
    console.log('View observation:', observation);
  }

  editObservation(observation: any): void {
    console.log('Edit observation:', observation);
  }

  shareObservation(observation: any): void {
    console.log('Share observation:', observation);
    this.router.navigate(['/teacher/messages'], { queryParams: { observation: observation.id } });
  }

  exportObservations(): void {
    console.log('Export observations');
  }

  getTotalObservations(): number {
    return this.observations.length;
  }

  getPositiveObservations(): number {
    return this.observations.filter(o => o.type === 'positive').length;
  }

  getConcernObservations(): number {
    return this.observations.filter(o => o.type === 'concern').length;
  }

  getThisWeekObservations(): number {
    const oneWeekAgo = new Date();
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
    return this.observations.filter(o => o.date >= oneWeekAgo).length;
  }
}
