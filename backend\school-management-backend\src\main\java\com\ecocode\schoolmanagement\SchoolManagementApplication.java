package com.ecocode.schoolmanagement;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@SpringBootApplication
@EnableJpaRepositories
@EnableTransactionManagement
public class SchoolManagementApplication {

    public static void main(String[] args) {
        SpringApplication.run(SchoolManagementApplication.class, args);
        System.out.println("🎓 School Management System Started Successfully!");
        System.out.println("📊 Database: PostgreSQL");
        System.out.println("🌐 API Base URL: http://localhost:2023/api");
        System.out.println("📋 PgAdmin URL: http://localhost:5432");
    }
}
