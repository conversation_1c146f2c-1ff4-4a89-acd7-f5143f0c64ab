export interface Message {
  id: number;
  senderId: string;
  senderName: string;
  senderType: 'parent' | 'teacher' | 'admin';
  receiverId: string;
  receiverName: string;
  receiverType: 'parent' | 'teacher' | 'admin';
  subject: string;
  content: string;
  timestamp: string;
  isRead: boolean;
  attachments?: MessageAttachment[];
  conversationId?: string;
}

export interface MessageAttachment {
  id: number;
  fileName: string;
  fileUrl: string;
  fileType: string;
  fileSize: number;
  uploadDate: string;
}

export interface Conversation {
  id: string;
  participants: ConversationParticipant[];
  lastMessage: Message;
  unreadCount: number;
  createdDate: string;
  updatedDate: string;
}

export interface ConversationParticipant {
  userId: string;
  userName: string;
  userType: 'parent' | 'teacher' | 'admin';
  avatar?: string;
  lastSeen?: string;
}

export interface ChatMessage {
  id: number;
  conversationId: string;
  senderId: string;
  senderName: string;
  content: string;
  timestamp: string;
  messageType: 'text' | 'file' | 'image';
  attachments?: MessageAttachment[];
  isRead: boolean;
}

export interface NotificationCount {
  unreadMessages: number;
  unreadNotifications: number;
  total: number;
}

export interface Notification {
  id: number;
  userId: string;
  title: string;
  message: string;
  type: 'info' | 'warning' | 'success' | 'error';
  category: 'academic' | 'payment' | 'discipline' | 'general';
  isRead: boolean;
  createdDate: string;
  actionUrl?: string;
  data?: any;
}
