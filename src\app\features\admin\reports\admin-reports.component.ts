import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';

@Component({
  selector: 'app-admin-reports',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <div class="admin-reports">
      <!-- Header -->
      <header class="page-header">
        <div class="header-content">
          <div class="header-left">
            <button class="back-btn" (click)="goBack()">
              <span class="icon">←</span>
              Back to Dashboard
            </button>
            <h1>📊 Reports & Analytics</h1>
            <p>System performance insights and detailed reports</p>
          </div>
          <div class="header-actions">
            <button class="export-btn" (click)="exportReport()">
              <span class="icon">📥</span>
              Export Report
            </button>
          </div>
        </div>
      </header>

      <!-- Report Categories -->
      <section class="categories-section">
        <div class="categories-grid">
          <div class="category-card" 
               [class.active]="selectedCategory === 'academic'"
               (click)="setCategory('academic')">
            <div class="category-icon">📚</div>
            <h3>Academic Reports</h3>
            <p>Student performance and grades</p>
          </div>
          
          <div class="category-card" 
               [class.active]="selectedCategory === 'attendance'"
               (click)="setCategory('attendance')">
            <div class="category-icon">📅</div>
            <h3>Attendance Reports</h3>
            <p>Student and teacher attendance</p>
          </div>
          
          <div class="category-card" 
               [class.active]="selectedCategory === 'financial'"
               (click)="setCategory('financial')">
            <div class="category-icon">💰</div>
            <h3>Financial Reports</h3>
            <p>Fee collection and expenses</p>
          </div>
          
          <div class="category-card" 
               [class.active]="selectedCategory === 'system'"
               (click)="setCategory('system')">
            <div class="category-icon">⚙️</div>
            <h3>System Reports</h3>
            <p>Usage and performance metrics</p>
          </div>
        </div>
      </section>

      <!-- Report Content -->
      <section class="report-content">
        <!-- Academic Reports -->
        <div class="report-section" *ngIf="selectedCategory === 'academic'">
          <div class="section-header">
            <h2>📚 Academic Performance</h2>
            <div class="filters">
              <select [(ngModel)]="academicFilter" name="academicFilter">
                <option value="all">All Classes</option>
                <option value="grade1">Grade 1</option>
                <option value="grade2">Grade 2</option>
                <option value="grade3">Grade 3</option>
                <option value="grade4">Grade 4</option>
                <option value="grade5">Grade 5</option>
              </select>
            </div>
          </div>
          
          <div class="charts-grid">
            <div class="chart-card">
              <h4>📈 Grade Distribution</h4>
              <div class="chart-placeholder">
                <div class="grade-bar" *ngFor="let grade of gradeDistribution">
                  <span class="grade-label">{{grade.grade}}</span>
                  <div class="bar-container">
                    <div class="bar" [style.width.%]="grade.percentage"></div>
                  </div>
                  <span class="grade-value">{{grade.count}}</span>
                </div>
              </div>
            </div>
            
            <div class="chart-card">
              <h4>📊 Subject Performance</h4>
              <div class="performance-list">
                <div class="performance-item" *ngFor="let subject of subjectPerformance">
                  <div class="subject-info">
                    <span class="subject-name">{{subject.name}}</span>
                    <span class="subject-average">{{subject.average}}%</span>
                  </div>
                  <div class="progress-bar">
                    <div class="progress" [style.width.%]="subject.average"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Attendance Reports -->
        <div class="report-section" *ngIf="selectedCategory === 'attendance'">
          <div class="section-header">
            <h2>📅 Attendance Overview</h2>
            <div class="filters">
              <select [(ngModel)]="attendanceFilter" name="attendanceFilter">
                <option value="week">This Week</option>
                <option value="month">This Month</option>
                <option value="semester">This Semester</option>
              </select>
            </div>
          </div>
          
          <div class="attendance-stats">
            <div class="stat-card">
              <div class="stat-icon">👨‍🎓</div>
              <div class="stat-content">
                <h3>{{attendanceStats.studentRate}}%</h3>
                <p>Student Attendance</p>
              </div>
            </div>
            <div class="stat-card">
              <div class="stat-icon">👩‍🏫</div>
              <div class="stat-content">
                <h3>{{attendanceStats.teacherRate}}%</h3>
                <p>Teacher Attendance</p>
              </div>
            </div>
            <div class="stat-card">
              <div class="stat-icon">📈</div>
              <div class="stat-content">
                <h3>{{attendanceStats.trend}}</h3>
                <p>Weekly Trend</p>
              </div>
            </div>
          </div>
          
          <div class="attendance-table">
            <table class="data-table">
              <thead>
                <tr>
                  <th>Class</th>
                  <th>Total Students</th>
                  <th>Present Today</th>
                  <th>Attendance Rate</th>
                  <th>Status</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let attendance of classAttendance">
                  <td>{{attendance.class}}</td>
                  <td>{{attendance.total}}</td>
                  <td>{{attendance.present}}</td>
                  <td>{{attendance.rate}}%</td>
                  <td>
                    <span class="status-badge" [ngClass]="getAttendanceStatus(attendance.rate)">
                      {{getAttendanceStatusText(attendance.rate)}}
                    </span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- Financial Reports -->
        <div class="report-section" *ngIf="selectedCategory === 'financial'">
          <div class="section-header">
            <h2>💰 Financial Overview</h2>
            <div class="filters">
              <select [(ngModel)]="financialFilter" name="financialFilter">
                <option value="month">This Month</option>
                <option value="quarter">This Quarter</option>
                <option value="year">This Year</option>
              </select>
            </div>
          </div>
          
          <div class="financial-summary">
            <div class="summary-card income">
              <div class="summary-icon">💵</div>
              <div class="summary-content">
                <h3>${{financialData.totalIncome.toLocaleString()}}</h3>
                <p>Total Income</p>
                <span class="trend positive">+{{financialData.incomeGrowth}}%</span>
              </div>
            </div>
            <div class="summary-card expenses">
              <div class="summary-icon">💸</div>
              <div class="summary-content">
                <h3>${{financialData.totalExpenses.toLocaleString()}}</h3>
                <p>Total Expenses</p>
                <span class="trend negative">+{{financialData.expenseGrowth}}%</span>
              </div>
            </div>
            <div class="summary-card profit">
              <div class="summary-icon">📈</div>
              <div class="summary-content">
                <h3>${{financialData.netProfit.toLocaleString()}}</h3>
                <p>Net Profit</p>
                <span class="trend positive">+{{financialData.profitGrowth}}%</span>
              </div>
            </div>
          </div>
        </div>

        <!-- System Reports -->
        <div class="report-section" *ngIf="selectedCategory === 'system'">
          <div class="section-header">
            <h2>⚙️ System Performance</h2>
            <div class="filters">
              <select [(ngModel)]="systemFilter" name="systemFilter">
                <option value="realtime">Real-time</option>
                <option value="daily">Daily</option>
                <option value="weekly">Weekly</option>
              </select>
            </div>
          </div>
          
          <div class="system-metrics">
            <div class="metric-card">
              <h4>🖥️ Server Performance</h4>
              <div class="metric-item">
                <span class="metric-label">CPU Usage:</span>
                <div class="metric-bar">
                  <div class="metric-fill" [style.width.%]="systemMetrics.cpu"></div>
                </div>
                <span class="metric-value">{{systemMetrics.cpu}}%</span>
              </div>
              <div class="metric-item">
                <span class="metric-label">Memory Usage:</span>
                <div class="metric-bar">
                  <div class="metric-fill" [style.width.%]="systemMetrics.memory"></div>
                </div>
                <span class="metric-value">{{systemMetrics.memory}}%</span>
              </div>
              <div class="metric-item">
                <span class="metric-label">Disk Usage:</span>
                <div class="metric-bar">
                  <div class="metric-fill" [style.width.%]="systemMetrics.disk"></div>
                </div>
                <span class="metric-value">{{systemMetrics.disk}}%</span>
              </div>
            </div>
            
            <div class="metric-card">
              <h4>👥 User Activity</h4>
              <div class="activity-stats">
                <div class="activity-item">
                  <span class="activity-label">Active Users:</span>
                  <span class="activity-value">{{systemMetrics.activeUsers}}</span>
                </div>
                <div class="activity-item">
                  <span class="activity-label">Daily Logins:</span>
                  <span class="activity-value">{{systemMetrics.dailyLogins}}</span>
                </div>
                <div class="activity-item">
                  <span class="activity-label">Peak Hours:</span>
                  <span class="activity-value">{{systemMetrics.peakHours}}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  `,
  styles: [`
    .admin-reports {
      min-height: 100vh;
      background: #f5f7fa;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .page-header {
      background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
      color: white;
      padding: 30px;
      margin-bottom: 30px;
    }

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      max-width: 1400px;
      margin: 0 auto;
    }

    .back-btn {
      background: rgba(255, 255, 255, 0.2);
      border: none;
      color: white;
      padding: 8px 16px;
      border-radius: 6px;
      cursor: pointer;
      margin-bottom: 16px;
      transition: all 0.3s ease;
    }

    .back-btn:hover {
      background: rgba(255, 255, 255, 0.3);
    }

    .header-left h1 {
      margin: 0 0 8px 0;
      font-size: 28px;
      font-weight: 600;
    }

    .header-left p {
      margin: 0;
      opacity: 0.9;
      font-size: 16px;
    }

    .export-btn {
      background: #27ae60;
      border: none;
      color: white;
      padding: 12px 24px;
      border-radius: 8px;
      cursor: pointer;
      font-weight: 600;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .export-btn:hover {
      background: #229954;
      transform: translateY(-2px);
    }

    .categories-section {
      max-width: 1400px;
      margin: 0 auto 30px auto;
      padding: 0 30px;
    }

    .categories-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 20px;
    }

    .category-card {
      background: white;
      border-radius: 12px;
      padding: 24px;
      cursor: pointer;
      transition: all 0.3s ease;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
      border: 2px solid transparent;
    }

    .category-card:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .category-card.active {
      border-color: #3498db;
      background: #f8fafe;
    }

    .category-icon {
      font-size: 32px;
      margin-bottom: 16px;
    }

    .category-card h3 {
      margin: 0 0 8px 0;
      color: #2c3e50;
      font-size: 18px;
    }

    .category-card p {
      margin: 0;
      color: #7f8c8d;
      font-size: 14px;
    }

    .report-content {
      max-width: 1400px;
      margin: 0 auto;
      padding: 0 30px;
    }

    .report-section {
      background: white;
      border-radius: 12px;
      padding: 24px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    }

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;
      padding-bottom: 16px;
      border-bottom: 1px solid #e1e5e9;
    }

    .section-header h2 {
      margin: 0;
      color: #2c3e50;
      font-size: 22px;
    }

    .filters select {
      padding: 8px 12px;
      border: 2px solid #e1e5e9;
      border-radius: 6px;
      font-size: 14px;
    }

    .charts-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
      gap: 24px;
    }

    .chart-card {
      background: #f8f9fa;
      border-radius: 8px;
      padding: 20px;
    }

    .chart-card h4 {
      margin: 0 0 16px 0;
      color: #2c3e50;
    }

    .grade-bar {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 12px;
    }

    .grade-label {
      width: 30px;
      font-weight: 600;
      color: #2c3e50;
    }

    .bar-container {
      flex: 1;
      height: 20px;
      background: #e9ecef;
      border-radius: 10px;
      overflow: hidden;
    }

    .bar {
      height: 100%;
      background: linear-gradient(90deg, #3498db, #2980b9);
      border-radius: 10px;
      transition: width 0.3s ease;
    }

    .grade-value {
      width: 40px;
      text-align: right;
      font-weight: 600;
      color: #2c3e50;
    }

    .performance-list {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    .performance-item {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    .subject-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .subject-name {
      font-weight: 600;
      color: #2c3e50;
    }

    .subject-average {
      font-weight: 600;
      color: #27ae60;
    }

    .progress-bar {
      height: 8px;
      background: #e9ecef;
      border-radius: 4px;
      overflow: hidden;
    }

    .progress {
      height: 100%;
      background: linear-gradient(90deg, #27ae60, #2ecc71);
      border-radius: 4px;
      transition: width 0.3s ease;
    }

    .attendance-stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
      margin-bottom: 30px;
    }

    .stat-card {
      background: #f8f9fa;
      border-radius: 8px;
      padding: 20px;
      display: flex;
      align-items: center;
      gap: 16px;
    }

    .stat-icon {
      font-size: 32px;
      background: white;
      padding: 12px;
      border-radius: 8px;
    }

    .stat-content h3 {
      margin: 0 0 4px 0;
      font-size: 24px;
      font-weight: 700;
      color: #2c3e50;
    }

    .stat-content p {
      margin: 0;
      color: #7f8c8d;
      font-size: 14px;
    }

    .attendance-table {
      overflow-x: auto;
    }

    .data-table {
      width: 100%;
      border-collapse: collapse;
      background: white;
      border-radius: 8px;
      overflow: hidden;
    }

    .data-table th {
      background: #f8f9fa;
      padding: 16px;
      text-align: left;
      font-weight: 600;
      color: #2c3e50;
      border-bottom: 1px solid #e1e5e9;
    }

    .data-table td {
      padding: 16px;
      border-bottom: 1px solid #f1f3f4;
    }

    .data-table tr:hover {
      background: #f8f9fa;
    }

    .status-badge {
      padding: 4px 12px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 600;
    }

    .status-badge.excellent {
      background: #d4edda;
      color: #155724;
    }

    .status-badge.good {
      background: #cce5ff;
      color: #004085;
    }

    .status-badge.poor {
      background: #f8d7da;
      color: #721c24;
    }

    .financial-summary {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 24px;
    }

    .summary-card {
      background: #f8f9fa;
      border-radius: 12px;
      padding: 24px;
      display: flex;
      align-items: center;
      gap: 16px;
    }

    .summary-card.income {
      border-left: 4px solid #27ae60;
    }

    .summary-card.expenses {
      border-left: 4px solid #e74c3c;
    }

    .summary-card.profit {
      border-left: 4px solid #3498db;
    }

    .summary-icon {
      font-size: 32px;
      background: white;
      padding: 12px;
      border-radius: 8px;
    }

    .summary-content h3 {
      margin: 0 0 4px 0;
      font-size: 24px;
      font-weight: 700;
      color: #2c3e50;
    }

    .summary-content p {
      margin: 0 0 4px 0;
      color: #7f8c8d;
      font-size: 14px;
    }

    .trend {
      font-size: 12px;
      font-weight: 600;
      padding: 2px 6px;
      border-radius: 4px;
    }

    .trend.positive {
      background: #d4edda;
      color: #155724;
    }

    .trend.negative {
      background: #f8d7da;
      color: #721c24;
    }

    .system-metrics {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 24px;
    }

    .metric-card {
      background: #f8f9fa;
      border-radius: 8px;
      padding: 20px;
    }

    .metric-card h4 {
      margin: 0 0 16px 0;
      color: #2c3e50;
    }

    .metric-item {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 12px;
    }

    .metric-label {
      width: 100px;
      font-size: 14px;
      color: #7f8c8d;
    }

    .metric-bar {
      flex: 1;
      height: 8px;
      background: #e9ecef;
      border-radius: 4px;
      overflow: hidden;
    }

    .metric-fill {
      height: 100%;
      background: linear-gradient(90deg, #3498db, #2980b9);
      border-radius: 4px;
      transition: width 0.3s ease;
    }

    .metric-value {
      width: 40px;
      text-align: right;
      font-weight: 600;
      color: #2c3e50;
      font-size: 14px;
    }

    .activity-stats {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    .activity-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 0;
      border-bottom: 1px solid #e9ecef;
    }

    .activity-label {
      color: #7f8c8d;
      font-size: 14px;
    }

    .activity-value {
      font-weight: 600;
      color: #2c3e50;
    }

    @media (max-width: 768px) {
      .header-content {
        flex-direction: column;
        gap: 20px;
        text-align: center;
      }

      .categories-grid {
        grid-template-columns: 1fr;
      }

      .charts-grid {
        grid-template-columns: 1fr;
      }

      .financial-summary {
        grid-template-columns: 1fr;
      }

      .system-metrics {
        grid-template-columns: 1fr;
      }
    }
  `]
})
export class AdminReportsComponent {
  selectedCategory = 'academic';
  academicFilter = 'all';
  attendanceFilter = 'week';
  financialFilter = 'month';
  systemFilter = 'realtime';

  gradeDistribution = [
    { grade: 'A+', count: 45, percentage: 90 },
    { grade: 'A', count: 78, percentage: 85 },
    { grade: 'B+', count: 92, percentage: 75 },
    { grade: 'B', count: 67, percentage: 60 },
    { grade: 'C+', count: 34, percentage: 40 },
    { grade: 'C', count: 23, percentage: 25 }
  ];

  subjectPerformance = [
    { name: 'Mathematics', average: 87 },
    { name: 'Science', average: 82 },
    { name: 'English', average: 79 },
    { name: 'History', average: 85 },
    { name: 'Art', average: 91 }
  ];

  attendanceStats = {
    studentRate: 94,
    teacherRate: 98,
    trend: '+2.3%'
  };

  classAttendance = [
    { class: 'Grade 5A', total: 28, present: 26, rate: 93 },
    { class: 'Grade 4B', total: 25, present: 24, rate: 96 },
    { class: 'Grade 3A', total: 22, present: 20, rate: 91 },
    { class: 'Grade 2B', total: 24, present: 23, rate: 96 },
    { class: 'Grade 1A', total: 20, present: 18, rate: 90 }
  ];

  financialData = {
    totalIncome: 125000,
    totalExpenses: 89000,
    netProfit: 36000,
    incomeGrowth: 8.5,
    expenseGrowth: 3.2,
    profitGrowth: 15.7
  };

  systemMetrics = {
    cpu: 45,
    memory: 67,
    disk: 78,
    activeUsers: 1247,
    dailyLogins: 892,
    peakHours: '9:00-11:00 AM'
  };

  constructor(private router: Router) {}

  goBack(): void {
    this.router.navigate(['/admin']);
  }

  setCategory(category: string): void {
    this.selectedCategory = category;
  }

  exportReport(): void {
    console.log('Exporting report for category:', this.selectedCategory);
    // Implement export functionality
  }

  getAttendanceStatus(rate: number): string {
    if (rate >= 95) return 'excellent';
    if (rate >= 90) return 'good';
    return 'poor';
  }

  getAttendanceStatusText(rate: number): string {
    if (rate >= 95) return 'Excellent';
    if (rate >= 90) return 'Good';
    return 'Needs Attention';
  }
}
