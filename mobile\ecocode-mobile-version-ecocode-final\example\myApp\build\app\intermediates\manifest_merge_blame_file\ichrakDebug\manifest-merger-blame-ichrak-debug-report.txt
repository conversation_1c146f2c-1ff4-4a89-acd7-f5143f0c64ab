1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.IchrakSchool"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:4:5-67
15-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:4:22-64
16    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
16-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:5:5-80
16-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:5:22-77
17    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
17-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:6:5-81
17-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:6:22-78
18    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
18-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:7:5-82
18-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:7:22-79
19    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
19-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:8:5-83
19-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:8:22-80
20    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
20-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:9:5-80
20-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:9:22-78
21    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
21-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:10:5-76
21-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:10:22-74
22    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
22-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:11:5-76
22-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:11:22-74
23    <uses-permission android:name="android.permission.VIBRATE" />
23-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:12:5-65
23-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:12:22-63
24    <uses-permission android:name="android.permission.WAKE_LOCK" />
24-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:13:5-68
24-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:13:22-65
25    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
25-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:14:5-82
25-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:14:22-79
26
27    <queries>
27-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:51:5-56:15
28        <intent>
28-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:52:9-55:18
29            <action android:name="android.intent.action.PROCESS_TEXT" />
29-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:53:13-72
29-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:53:21-70
30
31            <data android:mimeType="text/plain" />
31-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:42:17-89
32        </intent>
33        <intent>
33-->[:file_picker] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-12:18
34            <action android:name="android.intent.action.GET_CONTENT" />
34-->[:file_picker] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-72
34-->[:file_picker] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:9:21-69
35
36            <data android:mimeType="*/*" />
36-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:42:17-89
37        </intent>
38        <intent>
38-->[net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-3\e29af6bba5b83fbf9d8cabbd1c723f32\transformed\jetified-appauth-0.11.1\AndroidManifest.xml:25:9-31:18
39            <action android:name="android.intent.action.VIEW" />
39-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:39:17-68
39-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:39:25-66
40
41            <category android:name="android.intent.category.BROWSABLE" />
41-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:41:17-77
41-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:41:27-75
42
43            <data android:scheme="https" />
43-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:42:17-89
43-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:42:23-62
44        </intent>
45    </queries>
46
47    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
47-->[:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:9:5-79
47-->[:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:9:22-76
48
49    <permission
49-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\42462ff615a470fce269a489eb930f99\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
50        android:name="com.example.IchrakSchool.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
50-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\42462ff615a470fce269a489eb930f99\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
51        android:protectionLevel="signature" />
51-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\42462ff615a470fce269a489eb930f99\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
52
53    <uses-permission android:name="com.example.IchrakSchool.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
53-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\42462ff615a470fce269a489eb930f99\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
53-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\42462ff615a470fce269a489eb930f99\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
54
55    <application
55-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:15:5-49:19
56        android:name="androidx.multidex.MultiDexApplication"
56-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:17:9-61
57        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
57-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\42462ff615a470fce269a489eb930f99\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
58        android:debuggable="true"
59        android:enableOnBackInvokedCallback="true"
59-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:19:9-51
60        android:extractNativeLibs="true"
61        android:icon="@mipmap/demo"
61-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:18:9-36
62        android:label="@string/app_name"
62-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:16:9-41
63        android:requestLegacyExternalStorage="true" >
63-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:20:9-52
64        <activity
64-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:21:9-44:20
65            android:name="com.example.NovaSchool.MainActivity"
65-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:22:13-41
66            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
66-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:26:13-163
67            android:exported="true"
67-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:23:13-36
68            android:hardwareAccelerated="true"
68-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:27:13-47
69            android:launchMode="singleTop"
69-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:24:13-43
70            android:theme="@style/LaunchTheme"
70-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:25:13-47
71            android:windowSoftInputMode="adjustResize" >
71-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:28:13-55
72            <meta-data
72-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:30:13-33:19
73                android:name="io.flutter.embedding.android.NormalTheme"
73-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:31:17-72
74                android:resource="@style/NormalTheme" />
74-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:32:17-54
75
76            <intent-filter>
76-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:34:13-37:29
77                <action android:name="android.intent.action.MAIN" />
77-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:35:17-68
77-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:35:25-66
78
79                <category android:name="android.intent.category.LAUNCHER" />
79-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:36:17-76
79-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:36:27-74
80            </intent-filter>
81            <intent-filter>
81-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:38:13-43:29
82                <action android:name="android.intent.action.VIEW" />
82-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:39:17-68
82-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:39:25-66
83
84                <category android:name="android.intent.category.DEFAULT" />
84-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:40:17-75
84-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:40:27-73
85                <category android:name="android.intent.category.BROWSABLE" />
85-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:41:17-77
85-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:41:27-75
86
87                <data
87-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:42:17-89
88                    android:host="callback"
88-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:42:63-86
89                    android:scheme="com.example.NovaSchool" />
89-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:42:23-62
90            </intent-filter>
91        </activity>
92
93        <meta-data
93-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:46:9-48:33
94            android:name="flutterEmbedding"
94-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:47:13-44
95            android:value="2" />
95-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:48:13-30
96
97        <service
97-->[:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:14:9-17:72
98            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService"
98-->[:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:15:13-107
99            android:exported="false"
99-->[:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:16:13-37
100            android:permission="android.permission.BIND_JOB_SERVICE" />
100-->[:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:17:13-69
101        <service
101-->[:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:18:9-24:19
102            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService"
102-->[:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:19:13-97
103            android:exported="false" >
103-->[:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:20:13-37
104            <intent-filter>
104-->[:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:21:13-23:29
105                <action android:name="com.google.firebase.MESSAGING_EVENT" />
105-->[:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:22:17-78
105-->[:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:22:25-75
106            </intent-filter>
107        </service>
108
109        <receiver
109-->[:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:26:9-33:20
110            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver"
110-->[:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:27:13-98
111            android:exported="true"
111-->[:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:28:13-36
112            android:permission="com.google.android.c2dm.permission.SEND" >
112-->[:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:29:13-73
113            <intent-filter>
113-->[:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:30:13-32:29
114                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
114-->[:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:31:17-81
114-->[:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:31:25-78
115            </intent-filter>
116        </receiver>
117
118        <service
118-->[:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:35:9-39:19
119            android:name="com.google.firebase.components.ComponentDiscoveryService"
119-->[:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:35:18-89
120            android:directBootAware="true"
120-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9e6da811dcfb7c615f8acdd62216d271\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
121            android:exported="false" >
121-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac0dcf16ce6a281444fe134795f48771\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:56:13-37
122            <meta-data
122-->[:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:36:13-38:85
123                android:name="com.google.firebase.components:io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar"
123-->[:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:37:17-128
124                android:value="com.google.firebase.components.ComponentRegistrar" />
124-->[:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:38:17-82
125            <meta-data
125-->[:firebase_core] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-11:85
126                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
126-->[:firebase_core] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:10:17-124
127                android:value="com.google.firebase.components.ComponentRegistrar" />
127-->[:firebase_core] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:11:17-82
128            <meta-data
128-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac0dcf16ce6a281444fe134795f48771\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:57:13-59:85
129                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
129-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac0dcf16ce6a281444fe134795f48771\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:58:17-122
130                android:value="com.google.firebase.components.ComponentRegistrar" />
130-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac0dcf16ce6a281444fe134795f48771\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:59:17-82
131            <meta-data
131-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac0dcf16ce6a281444fe134795f48771\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:60:13-62:85
132                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
132-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac0dcf16ce6a281444fe134795f48771\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:61:17-119
133                android:value="com.google.firebase.components.ComponentRegistrar" />
133-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac0dcf16ce6a281444fe134795f48771\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:62:17-82
134            <meta-data
134-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\16b44a5a27378eb99bc553b95273bac9\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
135                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
135-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\16b44a5a27378eb99bc553b95273bac9\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
136                android:value="com.google.firebase.components.ComponentRegistrar" />
136-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\16b44a5a27378eb99bc553b95273bac9\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
137            <meta-data
137-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\16b44a5a27378eb99bc553b95273bac9\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
138                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
138-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\16b44a5a27378eb99bc553b95273bac9\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
139                android:value="com.google.firebase.components.ComponentRegistrar" />
139-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\16b44a5a27378eb99bc553b95273bac9\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
140            <meta-data
140-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ae41e01eb7ae5d989b223de0fe61f461\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
141                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
141-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ae41e01eb7ae5d989b223de0fe61f461\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
142                android:value="com.google.firebase.components.ComponentRegistrar" />
142-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ae41e01eb7ae5d989b223de0fe61f461\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
143            <meta-data
143-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9e6da811dcfb7c615f8acdd62216d271\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
144                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
144-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9e6da811dcfb7c615f8acdd62216d271\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
145                android:value="com.google.firebase.components.ComponentRegistrar" />
145-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9e6da811dcfb7c615f8acdd62216d271\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
146            <meta-data
146-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\176032b12d74ea1bfa0ece5bf0bd4f5c\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
147                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
147-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\176032b12d74ea1bfa0ece5bf0bd4f5c\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
148                android:value="com.google.firebase.components.ComponentRegistrar" />
148-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\176032b12d74ea1bfa0ece5bf0bd4f5c\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
149        </service>
150
151        <provider
151-->[:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:41:9-45:38
152            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider"
152-->[:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:42:13-102
153            android:authorities="com.example.IchrakSchool.flutterfirebasemessaginginitprovider"
153-->[:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:43:13-88
154            android:exported="false"
154-->[:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:44:13-37
155            android:initOrder="99" />
155-->[:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:45:13-35
156        <provider
156-->[:open_file_android] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:9:9-19:20
157            android:name="com.crazecoder.openfile.FileProvider"
157-->[:open_file_android] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:10:13-64
158            android:authorities="com.example.IchrakSchool.fileProvider.com.crazecoder.openfile"
158-->[:open_file_android] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-88
159            android:exported="false"
159-->[:open_file_android] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:12:13-37
160            android:grantUriPermissions="true"
160-->[:open_file_android] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-47
161            android:requestLegacyExternalStorage="true" >
161-->[:open_file_android] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:14:13-56
162            <meta-data
162-->[:open_file_android] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:16:13-18:53
163                android:name="android.support.FILE_PROVIDER_PATHS"
163-->[:open_file_android] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:17:17-67
164                android:resource="@xml/filepaths" />
164-->[:open_file_android] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:18:17-50
165        </provider>
166
167        <activity
167-->[:url_launcher_android] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-11:74
168            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
168-->[:url_launcher_android] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-74
169            android:exported="false"
169-->[:url_launcher_android] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:10:13-37
170            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
170-->[:url_launcher_android] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-71
171        <activity
171-->[net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-3\e29af6bba5b83fbf9d8cabbd1c723f32\transformed\jetified-appauth-0.11.1\AndroidManifest.xml:35:9-40:77
172            android:name="net.openid.appauth.AuthorizationManagementActivity"
172-->[net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-3\e29af6bba5b83fbf9d8cabbd1c723f32\transformed\jetified-appauth-0.11.1\AndroidManifest.xml:36:13-78
173            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation|keyboard|keyboardHidden"
173-->[net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-3\e29af6bba5b83fbf9d8cabbd1c723f32\transformed\jetified-appauth-0.11.1\AndroidManifest.xml:37:13-115
174            android:exported="false"
174-->[net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-3\e29af6bba5b83fbf9d8cabbd1c723f32\transformed\jetified-appauth-0.11.1\AndroidManifest.xml:38:13-37
175            android:launchMode="singleTask"
175-->[net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-3\e29af6bba5b83fbf9d8cabbd1c723f32\transformed\jetified-appauth-0.11.1\AndroidManifest.xml:39:13-44
176            android:theme="@style/Theme.AppCompat.Translucent.NoTitleBar" />
176-->[net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-3\e29af6bba5b83fbf9d8cabbd1c723f32\transformed\jetified-appauth-0.11.1\AndroidManifest.xml:40:13-74
177        <activity
177-->[net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-3\e29af6bba5b83fbf9d8cabbd1c723f32\transformed\jetified-appauth-0.11.1\AndroidManifest.xml:41:9-52:20
178            android:name="net.openid.appauth.RedirectUriReceiverActivity"
178-->[net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-3\e29af6bba5b83fbf9d8cabbd1c723f32\transformed\jetified-appauth-0.11.1\AndroidManifest.xml:42:13-74
179            android:exported="true" >
179-->[net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-3\e29af6bba5b83fbf9d8cabbd1c723f32\transformed\jetified-appauth-0.11.1\AndroidManifest.xml:43:13-36
180            <intent-filter>
180-->[net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-3\e29af6bba5b83fbf9d8cabbd1c723f32\transformed\jetified-appauth-0.11.1\AndroidManifest.xml:44:13-51:29
181                <action android:name="android.intent.action.VIEW" />
181-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:39:17-68
181-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:39:25-66
182
183                <category android:name="android.intent.category.DEFAULT" />
183-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:40:17-75
183-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:40:27-73
184                <category android:name="android.intent.category.BROWSABLE" />
184-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:41:17-77
184-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:41:27-75
185
186                <data android:scheme="com.example.NovaSchool" />
186-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:42:17-89
186-->C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:42:23-62
187            </intent-filter>
188        </activity>
189
190        <uses-library
190-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\dad65cb79b3eafd50888ad50598d546f\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
191            android:name="androidx.window.extensions"
191-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\dad65cb79b3eafd50888ad50598d546f\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
192            android:required="false" />
192-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\dad65cb79b3eafd50888ad50598d546f\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
193        <uses-library
193-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\dad65cb79b3eafd50888ad50598d546f\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
194            android:name="androidx.window.sidecar"
194-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\dad65cb79b3eafd50888ad50598d546f\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
195            android:required="false" />
195-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\dad65cb79b3eafd50888ad50598d546f\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
196
197        <provider
197-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\18bb7e320acb9ef5a96e99eb70fb4294\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
198            android:name="androidx.startup.InitializationProvider"
198-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\18bb7e320acb9ef5a96e99eb70fb4294\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
199            android:authorities="com.example.IchrakSchool.androidx-startup"
199-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\18bb7e320acb9ef5a96e99eb70fb4294\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
200            android:exported="false" >
200-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\18bb7e320acb9ef5a96e99eb70fb4294\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
201            <meta-data
201-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\18bb7e320acb9ef5a96e99eb70fb4294\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
202                android:name="androidx.emoji2.text.EmojiCompatInitializer"
202-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\18bb7e320acb9ef5a96e99eb70fb4294\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
203                android:value="androidx.startup" />
203-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\18bb7e320acb9ef5a96e99eb70fb4294\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
204            <meta-data
204-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\363db97abb4332fc93a163ff582cf337\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
205                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
205-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\363db97abb4332fc93a163ff582cf337\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
206                android:value="androidx.startup" />
206-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\363db97abb4332fc93a163ff582cf337\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
207            <meta-data
207-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f2c03ffff95df5ec6d0d921ca84310cb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
208                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
208-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f2c03ffff95df5ec6d0d921ca84310cb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
209                android:value="androidx.startup" />
209-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f2c03ffff95df5ec6d0d921ca84310cb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
210        </provider>
211
212        <receiver
212-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac0dcf16ce6a281444fe134795f48771\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:29:9-40:20
213            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
213-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac0dcf16ce6a281444fe134795f48771\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:30:13-78
214            android:exported="true"
214-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac0dcf16ce6a281444fe134795f48771\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:31:13-36
215            android:permission="com.google.android.c2dm.permission.SEND" >
215-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac0dcf16ce6a281444fe134795f48771\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:32:13-73
216            <intent-filter>
216-->[:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:30:13-32:29
217                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
217-->[:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:31:17-81
217-->[:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:31:25-78
218            </intent-filter>
219
220            <meta-data
220-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac0dcf16ce6a281444fe134795f48771\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:37:13-39:40
221                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
221-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac0dcf16ce6a281444fe134795f48771\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:38:17-92
222                android:value="true" />
222-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac0dcf16ce6a281444fe134795f48771\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:39:17-37
223        </receiver>
224        <!--
225             FirebaseMessagingService performs security checks at runtime,
226             but set to not exported to explicitly avoid allowing another app to call it.
227        -->
228        <service
228-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac0dcf16ce6a281444fe134795f48771\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:46:9-53:19
229            android:name="com.google.firebase.messaging.FirebaseMessagingService"
229-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac0dcf16ce6a281444fe134795f48771\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:47:13-82
230            android:directBootAware="true"
230-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac0dcf16ce6a281444fe134795f48771\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:48:13-43
231            android:exported="false" >
231-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac0dcf16ce6a281444fe134795f48771\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:49:13-37
232            <intent-filter android:priority="-500" >
232-->[:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:21:13-23:29
233                <action android:name="com.google.firebase.MESSAGING_EVENT" />
233-->[:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:22:17-78
233-->[:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:22:25-75
234            </intent-filter>
235        </service>
236
237        <provider
237-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9e6da811dcfb7c615f8acdd62216d271\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
238            android:name="com.google.firebase.provider.FirebaseInitProvider"
238-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9e6da811dcfb7c615f8acdd62216d271\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
239            android:authorities="com.example.IchrakSchool.firebaseinitprovider"
239-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9e6da811dcfb7c615f8acdd62216d271\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
240            android:directBootAware="true"
240-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9e6da811dcfb7c615f8acdd62216d271\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
241            android:exported="false"
241-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9e6da811dcfb7c615f8acdd62216d271\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
242            android:initOrder="100" />
242-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9e6da811dcfb7c615f8acdd62216d271\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
243
244        <activity
244-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8688e9838b51b6004ba54c59160ddf37\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
245            android:name="com.google.android.gms.common.api.GoogleApiActivity"
245-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8688e9838b51b6004ba54c59160ddf37\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:19-85
246            android:exported="false"
246-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8688e9838b51b6004ba54c59160ddf37\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:22:19-43
247            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
247-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8688e9838b51b6004ba54c59160ddf37\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:21:19-78
248
249        <meta-data
249-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\1f8ae2ae03c8f3ae6e8dc60fe5ef01da\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
250            android:name="com.google.android.gms.version"
250-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\1f8ae2ae03c8f3ae6e8dc60fe5ef01da\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
251            android:value="@integer/google_play_services_version" />
251-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\1f8ae2ae03c8f3ae6e8dc60fe5ef01da\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
252
253        <receiver
253-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f2c03ffff95df5ec6d0d921ca84310cb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
254            android:name="androidx.profileinstaller.ProfileInstallReceiver"
254-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f2c03ffff95df5ec6d0d921ca84310cb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
255            android:directBootAware="false"
255-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f2c03ffff95df5ec6d0d921ca84310cb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
256            android:enabled="true"
256-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f2c03ffff95df5ec6d0d921ca84310cb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
257            android:exported="true"
257-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f2c03ffff95df5ec6d0d921ca84310cb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
258            android:permission="android.permission.DUMP" >
258-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f2c03ffff95df5ec6d0d921ca84310cb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
259            <intent-filter>
259-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f2c03ffff95df5ec6d0d921ca84310cb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
260                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
260-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f2c03ffff95df5ec6d0d921ca84310cb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
260-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f2c03ffff95df5ec6d0d921ca84310cb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
261            </intent-filter>
262            <intent-filter>
262-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f2c03ffff95df5ec6d0d921ca84310cb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
263                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
263-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f2c03ffff95df5ec6d0d921ca84310cb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
263-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f2c03ffff95df5ec6d0d921ca84310cb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
264            </intent-filter>
265            <intent-filter>
265-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f2c03ffff95df5ec6d0d921ca84310cb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
266                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
266-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f2c03ffff95df5ec6d0d921ca84310cb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
266-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f2c03ffff95df5ec6d0d921ca84310cb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
267            </intent-filter>
268            <intent-filter>
268-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f2c03ffff95df5ec6d0d921ca84310cb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
269                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
269-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f2c03ffff95df5ec6d0d921ca84310cb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
269-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f2c03ffff95df5ec6d0d921ca84310cb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
270            </intent-filter>
271        </receiver>
272
273        <service
273-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\32400d416bc1421387762890a1a0fc16\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
274            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
274-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\32400d416bc1421387762890a1a0fc16\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
275            android:exported="false" >
275-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\32400d416bc1421387762890a1a0fc16\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
276            <meta-data
276-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\32400d416bc1421387762890a1a0fc16\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
277                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
277-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\32400d416bc1421387762890a1a0fc16\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
278                android:value="cct" />
278-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\32400d416bc1421387762890a1a0fc16\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
279        </service>
280        <service
280-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\2575da579be80ed57e233552a065f6f6\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
281            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
281-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\2575da579be80ed57e233552a065f6f6\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
282            android:exported="false"
282-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\2575da579be80ed57e233552a065f6f6\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
283            android:permission="android.permission.BIND_JOB_SERVICE" >
283-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\2575da579be80ed57e233552a065f6f6\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
284        </service>
285
286        <receiver
286-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\2575da579be80ed57e233552a065f6f6\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
287            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
287-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\2575da579be80ed57e233552a065f6f6\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
288            android:exported="false" />
288-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\2575da579be80ed57e233552a065f6f6\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
289    </application>
290
291</manifest>
