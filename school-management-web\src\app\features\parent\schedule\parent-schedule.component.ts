import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

interface ScheduleEvent {
  id: number;
  subject: string;
  teacher: string;
  time: string;
  duration: string;
  room: string;
  day: string;
  childName: string;
  type: 'class' | 'exam' | 'activity' | 'meeting';
}

@Component({
  selector: 'app-parent-schedule',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <div class="schedule-container">
      <!-- Header -->
      <div class="schedule-header">
        <div class="header-content">
          <h1>📅 Class Schedule</h1>
          <p>View your children's weekly class schedule and upcoming events</p>
        </div>
        <div class="header-actions">
          <select class="child-selector" [(ngModel)]="selectedChild" (change)="filterByChild()">
            <option value="">All Children</option>
            <option value="<PERSON>"><PERSON></option>
            <option value="<PERSON>"><PERSON></option>
          </select>
        </div>
      </div>

      <!-- Week Navigation -->
      <div class="week-navigation">
        <button class="nav-btn" (click)="previousWeek()">← Previous Week</button>
        <div class="current-week">
          <h3>{{getCurrentWeekRange()}}</h3>
        </div>
        <button class="nav-btn" (click)="nextWeek()">Next Week →</button>
      </div>

      <!-- Schedule Grid -->
      <div class="schedule-grid">
        <div class="time-column">
          <div class="time-header">Time</div>
          <div *ngFor="let time of timeSlots" class="time-slot">{{time}}</div>
        </div>

        <div *ngFor="let day of weekDays" class="day-column">
          <div class="day-header">
            <div class="day-name">{{day.name}}</div>
            <div class="day-date">{{day.date}}</div>
          </div>
          <div *ngFor="let time of timeSlots" class="schedule-cell">
            <div *ngFor="let event of getEventsForDayAndTime(day.name, time)"
                 class="schedule-event"
                 [class]="getEventClass(event)"
                 (click)="selectEvent(event)">
              <div class="event-subject">{{event.subject}}</div>
              <div class="event-teacher">{{event.teacher}}</div>
              <div class="event-room">📍 {{event.room}}</div>
              <div class="event-child">👤 {{event.childName}}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Upcoming Events -->
      <div class="upcoming-events">
        <h3>📋 Upcoming Events</h3>
        <div class="events-list">
          <div *ngFor="let event of upcomingEvents" class="event-card" [class]="getEventClass(event)">
            <div class="event-info">
              <div class="event-title">{{event.subject}}</div>
              <div class="event-details">
                <span class="event-time">🕐 {{event.day}} at {{event.time}}</span>
                <span class="event-location">📍 {{event.room}}</span>
                <span class="event-teacher">👨‍🏫 {{event.teacher}}</span>
                <span class="event-child">👤 {{event.childName}}</span>
              </div>
            </div>
            <div class="event-type">{{getEventTypeIcon(event.type)}}</div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .schedule-container {
      min-height: 100vh;
      background: #f5f7fa;
      padding: 20px;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .schedule-header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 30px;
      border-radius: 12px;
      margin-bottom: 30px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;
      gap: 20px;
    }

    .header-content h1 {
      margin: 0 0 8px 0;
      font-size: 28px;
      font-weight: 600;
    }

    .header-content p {
      margin: 0;
      opacity: 0.9;
      font-size: 16px;
    }

    .child-selector {
      background: rgba(255, 255, 255, 0.2);
      border: none;
      color: white;
      padding: 10px 16px;
      border-radius: 8px;
      font-size: 14px;
    }

    .week-navigation {
      background: white;
      padding: 20px;
      border-radius: 12px;
      margin-bottom: 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    }

    .nav-btn {
      background: #667eea;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 8px;
      cursor: pointer;
      transition: background 0.3s ease;
    }

    .nav-btn:hover {
      background: #5a67d8;
    }

    .current-week h3 {
      margin: 0;
      color: #333;
      font-size: 18px;
    }

    .schedule-grid {
      background: white;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
      margin-bottom: 30px;
      display: grid;
      grid-template-columns: 100px repeat(7, 1fr);
      gap: 1px;
      background: #e1e5e9;
    }

    .time-column, .day-column {
      background: white;
    }

    .time-header, .day-header {
      background: #667eea;
      color: white;
      padding: 15px 10px;
      text-align: center;
      font-weight: 600;
    }

    .day-name {
      font-size: 14px;
      margin-bottom: 4px;
    }

    .day-date {
      font-size: 12px;
      opacity: 0.9;
    }

    .time-slot {
      padding: 15px 10px;
      text-align: center;
      font-size: 12px;
      color: #666;
      border-bottom: 1px solid #f0f0f0;
    }

    .schedule-cell {
      padding: 5px;
      min-height: 60px;
      border-bottom: 1px solid #f0f0f0;
    }

    .schedule-event {
      background: #e3f2fd;
      border-left: 3px solid #2196f3;
      padding: 8px;
      border-radius: 4px;
      margin-bottom: 4px;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .schedule-event:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .schedule-event.exam {
      background: #ffebee;
      border-left-color: #f44336;
    }

    .schedule-event.activity {
      background: #f3e5f5;
      border-left-color: #9c27b0;
    }

    .schedule-event.meeting {
      background: #fff3e0;
      border-left-color: #ff9800;
    }

    .event-subject {
      font-weight: 600;
      font-size: 12px;
      margin-bottom: 2px;
      color: #333;
    }

    .event-teacher, .event-room, .event-child {
      font-size: 10px;
      color: #666;
      margin-bottom: 1px;
    }

    .upcoming-events {
      background: white;
      padding: 25px;
      border-radius: 12px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    }

    .upcoming-events h3 {
      margin: 0 0 20px 0;
      color: #333;
      font-size: 20px;
    }

    .events-list {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    .event-card {
      background: #f8f9fa;
      padding: 16px;
      border-radius: 8px;
      border-left: 4px solid #2196f3;
      display: flex;
      justify-content: space-between;
      align-items: center;
      transition: all 0.3s ease;
    }

    .event-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .event-card.exam {
      border-left-color: #f44336;
    }

    .event-card.activity {
      border-left-color: #9c27b0;
    }

    .event-card.meeting {
      border-left-color: #ff9800;
    }

    .event-title {
      font-weight: 600;
      color: #333;
      margin-bottom: 8px;
    }

    .event-details {
      display: flex;
      flex-wrap: wrap;
      gap: 12px;
      font-size: 12px;
      color: #666;
    }

    .event-type {
      font-size: 24px;
    }

    @media (max-width: 768px) {
      .schedule-grid {
        grid-template-columns: 80px repeat(7, 1fr);
        font-size: 10px;
      }

      .schedule-header {
        flex-direction: column;
        align-items: stretch;
      }

      .week-navigation {
        flex-direction: column;
        gap: 10px;
      }

      .event-details {
        flex-direction: column;
        gap: 4px;
      }
    }
  `]
})
export class ParentScheduleComponent implements OnInit {
  scheduleEvents: ScheduleEvent[] = [];
  filteredEvents: ScheduleEvent[] = [];
  upcomingEvents: ScheduleEvent[] = [];
  selectedChild = '';
  currentWeekStart = new Date();

  timeSlots = [
    '08:00', '09:00', '10:00', '11:00', '12:00',
    '13:00', '14:00', '15:00', '16:00', '17:00'
  ];

  weekDays = [
    { name: 'Monday', date: '' },
    { name: 'Tuesday', date: '' },
    { name: 'Wednesday', date: '' },
    { name: 'Thursday', date: '' },
    { name: 'Friday', date: '' },
    { name: 'Saturday', date: '' },
    { name: 'Sunday', date: '' }
  ];

  ngOnInit(): void {
    this.loadScheduleData();
    this.updateWeekDates();
    this.filteredEvents = this.scheduleEvents;
    this.loadUpcomingEvents();
  }

  loadScheduleData(): void {
    this.scheduleEvents = [
      {
        id: 1,
        subject: 'Mathematics',
        teacher: 'Dr. Sarah Wilson',
        time: '09:00',
        duration: '60 min',
        room: 'Room 101',
        day: 'Monday',
        childName: 'Sarah Johnson',
        type: 'class'
      },
      {
        id: 2,
        subject: 'English Literature',
        teacher: 'Ms. Emily Davis',
        time: '10:00',
        duration: '60 min',
        room: 'Room 205',
        day: 'Monday',
        childName: 'Sarah Johnson',
        type: 'class'
      },
      {
        id: 3,
        subject: 'Science',
        teacher: 'Mr. John Smith',
        time: '11:00',
        duration: '60 min',
        room: 'Lab 1',
        day: 'Monday',
        childName: 'Michael Johnson',
        type: 'class'
      },
      {
        id: 4,
        subject: 'Physical Education',
        teacher: 'Coach Mike Thompson',
        time: '14:00',
        duration: '60 min',
        room: 'Gymnasium',
        day: 'Tuesday',
        childName: 'Michael Johnson',
        type: 'class'
      },
      {
        id: 5,
        subject: 'Math Test',
        teacher: 'Dr. Sarah Wilson',
        time: '09:00',
        duration: '90 min',
        room: 'Room 101',
        day: 'Wednesday',
        childName: 'Sarah Johnson',
        type: 'exam'
      },
      {
        id: 6,
        subject: 'Art Class',
        teacher: 'Ms. Lisa Chen',
        time: '13:00',
        duration: '60 min',
        room: 'Art Studio',
        day: 'Thursday',
        childName: 'Michael Johnson',
        type: 'activity'
      },
      {
        id: 7,
        subject: 'Parent-Teacher Meeting',
        teacher: 'Dr. Sarah Wilson',
        time: '15:00',
        duration: '30 min',
        room: 'Room 101',
        day: 'Friday',
        childName: 'Sarah Johnson',
        type: 'meeting'
      }
    ];
  }

  loadUpcomingEvents(): void {
    const today = new Date();
    this.upcomingEvents = this.scheduleEvents.filter(event => {
      // For demo purposes, show all events as upcoming
      return true;
    }).slice(0, 5);
  }

  updateWeekDates(): void {
    const startOfWeek = new Date(this.currentWeekStart);
    startOfWeek.setDate(startOfWeek.getDate() - startOfWeek.getDay() + 1); // Monday

    this.weekDays.forEach((day, index) => {
      const date = new Date(startOfWeek);
      date.setDate(startOfWeek.getDate() + index);
      day.date = date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
    });
  }

  getCurrentWeekRange(): string {
    const startOfWeek = new Date(this.currentWeekStart);
    startOfWeek.setDate(startOfWeek.getDate() - startOfWeek.getDay() + 1);

    const endOfWeek = new Date(startOfWeek);
    endOfWeek.setDate(startOfWeek.getDate() + 6);

    return `${startOfWeek.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })} - ${endOfWeek.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' })}`;
  }

  previousWeek(): void {
    this.currentWeekStart.setDate(this.currentWeekStart.getDate() - 7);
    this.updateWeekDates();
  }

  nextWeek(): void {
    this.currentWeekStart.setDate(this.currentWeekStart.getDate() + 7);
    this.updateWeekDates();
  }

  filterByChild(): void {
    if (this.selectedChild) {
      this.filteredEvents = this.scheduleEvents.filter(event => event.childName === this.selectedChild);
    } else {
      this.filteredEvents = this.scheduleEvents;
    }
  }

  getEventsForDayAndTime(day: string, time: string): ScheduleEvent[] {
    return this.filteredEvents.filter(event => event.day === day && event.time === time);
  }

  getEventClass(event: ScheduleEvent): string {
    return event.type;
  }

  getEventTypeIcon(type: string): string {
    switch (type) {
      case 'class': return '📚';
      case 'exam': return '📝';
      case 'activity': return '🎨';
      case 'meeting': return '👥';
      default: return '📅';
    }
  }

  selectEvent(event: ScheduleEvent): void {
    // Handle event selection
    console.log('Selected event:', event);
  }
}
