export interface Student {
  id: number;
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  classId: number;
  className: string;
  parentId: number;
  avatar?: string;
  studentNumber: string;
  enrollmentDate: string;
  status: 'active' | 'inactive' | 'graduated';
}

export interface StudentInfoPerso {
  id: number;
  nom: string;
  prenom: string;
  dateNaissance: string;
  lieuNaissance: string;
  nationalite: string;
  adresse: string;
  telephone: string;
  email: string;
  nomPere: string;
  nomMere: string;
  telephonePere: string;
  telephoneMere: string;
  classe: ClasseInfo;
}

export interface ClasseInfo {
  id: number;
  nom: string;
  niveau: string;
  anneeScolare: string;
  enseignantPrincipal?: string;
}

export interface StudentBalance {
  eleveId: number;
  solde: number;
  devise: string;
  lastUpdate: string;
}

export interface PaymentHistory {
  id: number;
  eleveId: number;
  montant: number;
  datePaiement: string;
  typePaiement: string;
  description: string;
  statut: 'paid' | 'pending' | 'failed';
}
