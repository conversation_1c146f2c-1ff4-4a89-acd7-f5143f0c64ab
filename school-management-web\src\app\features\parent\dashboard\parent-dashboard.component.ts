import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';

@Component({
  selector: 'app-parent-dashboard',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div style="padding: 20px; background: #f0f8ff; min-height: 100vh;">
      <h1 style="color: #333; font-size: 24px; margin-bottom: 20px;">🎉 Parent Dashboard</h1>
      <div style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
        <h2 style="color: #667eea; margin-bottom: 16px;">Welcome to Parent Portal</h2>
        <p style="color: #666; margin-bottom: 20px;">This is the parent dashboard. You can monitor your children's progress here.</p>
        <button (click)="testNavigation()" style="background: #667eea; color: white; border: none; padding: 12px 24px; border-radius: 6px; cursor: pointer; font-size: 14px;">
          Test Alert
        </button>
        <div style="margin-top: 20px; padding: 16px; background: #e8f5e8; border-radius: 6px;">
          <h3 style="color: #2e7d32; margin: 0 0 8px 0;">✅ System Status</h3>
          <p style="color: #2e7d32; margin: 0;">Parent dashboard is loading correctly!</p>
        </div>
        
        <!-- Quick Navigation -->
        <div style="margin-top: 20px; display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px;">
          <button (click)="navigateToGrades()" style="background: #4caf50; color: white; border: none; padding: 16px; border-radius: 8px; cursor: pointer;">
            📊 View Grades
          </button>
          <button (click)="navigateToAttendance()" style="background: #2196f3; color: white; border: none; padding: 16px; border-radius: 8px; cursor: pointer;">
            📋 Attendance
          </button>
          <button (click)="navigateToMessages()" style="background: #ff9800; color: white; border: none; padding: 16px; border-radius: 8px; cursor: pointer;">
            💬 Messages
          </button>
          <button (click)="navigateToSchedule()" style="background: #9c27b0; color: white; border: none; padding: 16px; border-radius: 8px; cursor: pointer;">
            📅 Schedule
          </button>
        </div>
      </div>
    </div>
  `,
  styles: [`
    /* Simple styles for testing */
  `]
})
export class ParentDashboardComponent {
  constructor(private router: Router) {}

  testNavigation(): void {
    alert('Navigation is working! Dashboard component is loaded correctly.');
  }

  logout(): void {
    this.router.navigate(['/auth/login']);
  }

  navigateToGrades(): void {
    this.router.navigate(['/parent/grades']);
  }

  navigateToSchedule(): void {
    this.router.navigate(['/parent/schedule']);
  }

  navigateToMessages(): void {
    this.router.navigate(['/parent/messages']);
  }

  navigateToAttendance(): void {
    this.router.navigate(['/parent/attendance']);
  }
}
