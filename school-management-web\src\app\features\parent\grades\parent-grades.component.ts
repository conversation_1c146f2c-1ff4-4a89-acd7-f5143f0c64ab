import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

interface Grade {
  id: number;
  childName: string;
  subject: string;
  assignment: string;
  grade: string;
  percentage: number;
  date: Date;
  teacher: string;
  category: 'assignment' | 'quiz' | 'exam' | 'project';
  feedback: string;
  maxPoints: number;
  earnedPoints: number;
}

interface GradeSummary {
  childName: string;
  subjects: {
    subject: string;
    average: number;
    letterGrade: string;
    totalAssignments: number;
    trend: 'up' | 'down' | 'stable';
  }[];
  overallGPA: number;
}

@Component({
  selector: 'app-parent-grades',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <div class="grades-container">
      <!-- Header -->
      <div class="grades-header">
        <div class="header-content">
          <h1>📊 Academic Performance</h1>
          <p>Track your children's grades and academic progress</p>
        </div>
        <div class="header-actions">
          <select [(ngModel)]="selectedChild" (change)="filterGrades()" class="child-filter">
            <option value="">All Children</option>
            <option value="<PERSON>"><PERSON></option>
            <option value="Michael Johnson">Michael <PERSON></option>
          </select>
          <select [(ngModel)]="selectedSubject" (change)="filterGrades()" class="subject-filter">
            <option value="">All Subjects</option>
            <option value="Mathematics">Mathematics</option>
            <option value="English">English</option>
            <option value="Science">Science</option>
            <option value="Art">Art</option>
            <option value="PE">PE</option>
            <option value="Music">Music</option>
          </select>
          <button class="export-btn" (click)="exportGrades()">
            <span class="btn-icon">📥</span>
            Export Report
          </button>
        </div>
      </div>

      <!-- Grade Summary Cards -->
      <div class="summary-section">
        <div *ngFor="let summary of gradeSummaries" class="summary-card">
          <div class="summary-header">
            <h3>👤 {{summary.childName}}</h3>
            <div class="gpa-badge">
              <span class="gpa-label">GPA</span>
              <span class="gpa-value">{{summary.overallGPA.toFixed(2)}}</span>
            </div>
          </div>
          <div class="subjects-grid">
            <div *ngFor="let subject of summary.subjects" class="subject-summary">
              <div class="subject-info">
                <span class="subject-name">{{subject.subject}}</span>
                <span class="assignment-count">{{subject.totalAssignments}} assignments</span>
              </div>
              <div class="grade-info">
                <span class="grade-average">{{subject.average.toFixed(1)}}%</span>
                <span class="letter-grade" [class]="getGradeClass(subject.letterGrade)">{{subject.letterGrade}}</span>
                <span class="trend-indicator" [class]="subject.trend">
                  {{getTrendIcon(subject.trend)}}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Detailed Grades Table -->
      <div class="grades-table-section">
        <h2>📋 Detailed Grades</h2>
        <div class="table-container">
          <table class="grades-table">
            <thead>
              <tr>
                <th>Child</th>
                <th>Subject</th>
                <th>Assignment</th>
                <th>Category</th>
                <th>Grade</th>
                <th>Points</th>
                <th>Date</th>
                <th>Teacher</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let grade of filteredGrades" class="grade-row">
                <td class="child-cell">
                  <span class="child-name">{{grade.childName}}</span>
                </td>
                <td class="subject-cell">{{grade.subject}}</td>
                <td class="assignment-cell">{{grade.assignment}}</td>
                <td class="category-cell">
                  <span class="category-badge" [class]="grade.category">{{grade.category | titlecase}}</span>
                </td>
                <td class="grade-cell">
                  <span class="grade-value" [class]="getGradeClass(grade.grade)">{{grade.grade}}</span>
                  <span class="percentage">({{grade.percentage}}%)</span>
                </td>
                <td class="points-cell">{{grade.earnedPoints}}/{{grade.maxPoints}}</td>
                <td class="date-cell">{{grade.date | date:'MMM dd, yyyy'}}</td>
                <td class="teacher-cell">{{grade.teacher}}</td>
                <td class="actions-cell">
                  <button class="action-btn" (click)="viewFeedback(grade)" title="View Feedback">
                    💬
                  </button>
                  <button class="action-btn" (click)="contactTeacher(grade)" title="Contact Teacher">
                    📧
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- Empty State -->
      <div *ngIf="filteredGrades.length === 0" class="empty-state">
        <div class="empty-icon">📊</div>
        <h3>No grades found</h3>
        <p>Try adjusting your filters or check back later for new grades.</p>
      </div>
    </div>
  `,
  styles: [`
    .grades-container {
      min-height: 100vh;
      background: #f5f7fa;
      padding: 20px;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .grades-header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 30px;
      border-radius: 12px;
      margin-bottom: 30px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;
      gap: 20px;
    }

    .header-content h1 {
      margin: 0 0 8px 0;
      font-size: 28px;
      font-weight: 600;
    }

    .header-content p {
      margin: 0;
      opacity: 0.9;
      font-size: 16px;
    }

    .header-actions {
      display: flex;
      gap: 16px;
      align-items: center;
      flex-wrap: wrap;
    }

    .child-filter, .subject-filter {
      padding: 12px 16px;
      border: none;
      border-radius: 8px;
      background: rgba(255, 255, 255, 0.2);
      color: white;
      cursor: pointer;
      min-width: 150px;
    }

    .export-btn {
      background: rgba(255, 255, 255, 0.2);
      border: none;
      color: white;
      padding: 12px 16px;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .export-btn:hover {
      background: rgba(255, 255, 255, 0.3);
    }

    .summary-section {
      display: flex;
      flex-direction: column;
      gap: 24px;
      margin-bottom: 30px;
    }

    .summary-card {
      background: white;
      border-radius: 12px;
      padding: 24px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    }

    .summary-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      padding-bottom: 16px;
      border-bottom: 2px solid #f8f9fa;
    }

    .summary-header h3 {
      margin: 0;
      color: #333;
      font-size: 20px;
      font-weight: 600;
    }

    .gpa-badge {
      display: flex;
      align-items: center;
      gap: 8px;
      background: #667eea;
      color: white;
      padding: 8px 16px;
      border-radius: 20px;
    }

    .gpa-label {
      font-size: 12px;
      opacity: 0.9;
    }

    .gpa-value {
      font-size: 18px;
      font-weight: 600;
    }

    .subjects-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 16px;
    }

    .subject-summary {
      background: #f8f9fa;
      padding: 16px;
      border-radius: 8px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .subject-info {
      display: flex;
      flex-direction: column;
      gap: 4px;
    }

    .subject-name {
      font-weight: 600;
      color: #333;
      font-size: 16px;
    }

    .assignment-count {
      font-size: 12px;
      color: #666;
    }

    .grade-info {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .grade-average {
      font-size: 18px;
      font-weight: 600;
      color: #333;
    }

    .letter-grade {
      padding: 4px 8px;
      border-radius: 4px;
      font-weight: 600;
      font-size: 14px;
    }

    .letter-grade.A {
      background: #d4edda;
      color: #155724;
    }

    .letter-grade.B {
      background: #cce5ff;
      color: #004085;
    }

    .letter-grade.C {
      background: #fff3cd;
      color: #856404;
    }

    .letter-grade.D {
      background: #f8d7da;
      color: #721c24;
    }

    .trend-indicator {
      font-size: 16px;
    }

    .trend-indicator.up {
      color: #28a745;
    }

    .trend-indicator.down {
      color: #dc3545;
    }

    .trend-indicator.stable {
      color: #6c757d;
    }

    .grades-table-section {
      background: white;
      border-radius: 12px;
      padding: 24px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
      margin-bottom: 30px;
    }

    .grades-table-section h2 {
      margin: 0 0 20px 0;
      color: #333;
      font-size: 20px;
      font-weight: 600;
    }

    .table-container {
      overflow-x: auto;
    }

    .grades-table {
      width: 100%;
      border-collapse: collapse;
      font-size: 14px;
    }

    .grades-table th {
      background: #f8f9fa;
      padding: 12px;
      text-align: left;
      font-weight: 600;
      color: #333;
      border-bottom: 2px solid #dee2e6;
    }

    .grades-table td {
      padding: 12px;
      border-bottom: 1px solid #dee2e6;
      vertical-align: middle;
    }

    .grade-row:hover {
      background: #f8f9fa;
    }

    .child-name {
      font-weight: 500;
      color: #667eea;
    }

    .category-badge {
      padding: 2px 8px;
      border-radius: 12px;
      font-size: 11px;
      font-weight: 500;
      text-transform: uppercase;
    }

    .category-badge.assignment {
      background: #e3f2fd;
      color: #1976d2;
    }

    .category-badge.quiz {
      background: #fff3e0;
      color: #f57c00;
    }

    .category-badge.exam {
      background: #fce4ec;
      color: #c2185b;
    }

    .category-badge.project {
      background: #e8f5e8;
      color: #388e3c;
    }

    .grade-value {
      font-weight: 600;
      font-size: 16px;
    }

    .percentage {
      font-size: 12px;
      color: #666;
      margin-left: 4px;
    }

    .action-btn {
      background: none;
      border: none;
      cursor: pointer;
      padding: 4px;
      margin: 0 2px;
      border-radius: 4px;
      transition: background 0.3s ease;
    }

    .action-btn:hover {
      background: #f8f9fa;
    }

    .empty-state {
      text-align: center;
      padding: 60px 20px;
      color: #666;
    }

    .empty-icon {
      font-size: 64px;
      margin-bottom: 16px;
    }

    .empty-state h3 {
      margin: 0 0 8px 0;
      color: #333;
    }

    .empty-state p {
      margin: 0;
    }

    @media (max-width: 768px) {
      .grades-header {
        flex-direction: column;
        align-items: stretch;
      }

      .header-actions {
        justify-content: stretch;
      }

      .child-filter, .subject-filter {
        min-width: 100%;
      }

      .subjects-grid {
        grid-template-columns: 1fr;
      }

      .grades-table {
        font-size: 12px;
      }

      .grades-table th,
      .grades-table td {
        padding: 8px 4px;
      }
    }
  `]
})
export class ParentGradesComponent implements OnInit {
  selectedChild = '';
  selectedSubject = '';
  grades: Grade[] = [];
  filteredGrades: Grade[] = [];
  gradeSummaries: GradeSummary[] = [];

  ngOnInit(): void {
    this.loadGrades();
    this.calculateGradeSummaries();
    this.filteredGrades = this.grades;
  }

  loadGrades(): void {
    this.grades = [
      {
        id: 1,
        childName: 'Sarah Johnson',
        subject: 'Mathematics',
        assignment: 'Algebra Quiz #3',
        grade: 'A',
        percentage: 92,
        date: new Date('2024-01-15'),
        teacher: 'Dr. Sarah Wilson',
        category: 'quiz',
        feedback: 'Excellent work on complex equations. Keep up the great effort!',
        maxPoints: 100,
        earnedPoints: 92
      },
      {
        id: 2,
        childName: 'Sarah Johnson',
        subject: 'English',
        assignment: 'Essay: Character Analysis',
        grade: 'A-',
        percentage: 88,
        date: new Date('2024-01-12'),
        teacher: 'Ms. Emily Davis',
        category: 'assignment',
        feedback: 'Well-structured analysis with good supporting evidence.',
        maxPoints: 100,
        earnedPoints: 88
      },
      {
        id: 3,
        childName: 'Sarah Johnson',
        subject: 'Science',
        assignment: 'Chemistry Lab Report',
        grade: 'B+',
        percentage: 85,
        date: new Date('2024-01-10'),
        teacher: 'Mr. James Rodriguez',
        category: 'project',
        feedback: 'Good experimental procedure, could improve data analysis.',
        maxPoints: 100,
        earnedPoints: 85
      },
      {
        id: 4,
        childName: 'Michael Johnson',
        subject: 'Art',
        assignment: 'Watercolor Landscape',
        grade: 'A+',
        percentage: 98,
        date: new Date('2024-01-14'),
        teacher: 'Ms. Lisa Chen',
        category: 'project',
        feedback: 'Outstanding creativity and technique. Beautiful composition!',
        maxPoints: 100,
        earnedPoints: 98
      },
      {
        id: 5,
        childName: 'Michael Johnson',
        subject: 'PE',
        assignment: 'Fitness Assessment',
        grade: 'A',
        percentage: 95,
        date: new Date('2024-01-11'),
        teacher: 'Coach Mike Thompson',
        category: 'exam',
        feedback: 'Excellent improvement in all fitness categories.',
        maxPoints: 100,
        earnedPoints: 95
      },
      {
        id: 6,
        childName: 'Michael Johnson',
        subject: 'Music',
        assignment: 'Recorder Performance',
        grade: 'B+',
        percentage: 87,
        date: new Date('2024-01-09'),
        teacher: 'Mr. David Park',
        category: 'assignment',
        feedback: 'Good rhythm and pitch. Practice scales for improvement.',
        maxPoints: 100,
        earnedPoints: 87
      }
    ];
  }

  calculateGradeSummaries(): void {
    const childrenMap = new Map<string, Grade[]>();

    // Group grades by child
    this.grades.forEach(grade => {
      if (!childrenMap.has(grade.childName)) {
        childrenMap.set(grade.childName, []);
      }
      childrenMap.get(grade.childName)!.push(grade);
    });

    this.gradeSummaries = Array.from(childrenMap.entries()).map(([childName, grades]) => {
      const subjectsMap = new Map<string, Grade[]>();

      // Group by subject
      grades.forEach(grade => {
        if (!subjectsMap.has(grade.subject)) {
          subjectsMap.set(grade.subject, []);
        }
        subjectsMap.get(grade.subject)!.push(grade);
      });

      const subjects = Array.from(subjectsMap.entries()).map(([subject, subjectGrades]) => {
        const average = subjectGrades.reduce((sum, g) => sum + g.percentage, 0) / subjectGrades.length;
        return {
          subject,
          average,
          letterGrade: this.getLetterGrade(average),
          totalAssignments: subjectGrades.length,
          trend: 'stable' as 'up' | 'down' | 'stable' // Simplified for demo
        };
      });

      const overallAverage = subjects.reduce((sum, s) => sum + s.average, 0) / subjects.length;
      const overallGPA = this.calculateGPA(overallAverage);

      return {
        childName,
        subjects,
        overallGPA
      };
    });
  }

  filterGrades(): void {
    this.filteredGrades = this.grades.filter(grade => {
      const matchesChild = !this.selectedChild || grade.childName === this.selectedChild;
      const matchesSubject = !this.selectedSubject || grade.subject === this.selectedSubject;
      return matchesChild && matchesSubject;
    });
  }

  getLetterGrade(percentage: number): string {
    if (percentage >= 90) return 'A';
    if (percentage >= 80) return 'B';
    if (percentage >= 70) return 'C';
    if (percentage >= 60) return 'D';
    return 'F';
  }

  calculateGPA(percentage: number): number {
    if (percentage >= 90) return 4.0;
    if (percentage >= 80) return 3.0;
    if (percentage >= 70) return 2.0;
    if (percentage >= 60) return 1.0;
    return 0.0;
  }

  getGradeClass(grade: string): string {
    return grade.charAt(0); // Returns 'A', 'B', 'C', 'D', or 'F'
  }

  getTrendIcon(trend: string): string {
    switch (trend) {
      case 'up': return '📈';
      case 'down': return '📉';
      default: return '➡️';
    }
  }

  exportGrades(): void {
    const csvContent = this.generateCSV();
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'grades-report.csv';
    link.click();
    window.URL.revokeObjectURL(url);
  }

  generateCSV(): string {
    const headers = ['Child', 'Subject', 'Assignment', 'Category', 'Grade', 'Percentage', 'Points', 'Date', 'Teacher'];
    const rows = this.filteredGrades.map(grade => [
      grade.childName,
      grade.subject,
      grade.assignment,
      grade.category,
      grade.grade,
      grade.percentage.toString(),
      `${grade.earnedPoints}/${grade.maxPoints}`,
      grade.date.toLocaleDateString(),
      grade.teacher
    ]);

    return [headers, ...rows].map(row => row.join(',')).join('\n');
  }

  viewFeedback(grade: Grade): void {
    alert(`Feedback for ${grade.assignment}:\n\n${grade.feedback}`);
  }

  contactTeacher(grade: Grade): void {
    console.log('Contacting teacher:', grade.teacher);
    // Implement teacher contact functionality
  }
}
