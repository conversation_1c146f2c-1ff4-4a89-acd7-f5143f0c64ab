<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name" translatable="false">LoujaynSchool</string>
    <string name="gcm_defaultSenderId" translatable="false">817685344848</string>
    <string name="google_api_key" translatable="false">AIzaSyB9LXzPrFdmW59MW49bH40lg_y6L-v-gtQ</string>
    <string name="google_app_id" translatable="false">1:817685344848:android:11442e94f6c210ca82d755</string>
    <string name="google_crash_reporting_api_key" translatable="false">AIzaSyB9LXzPrFdmW59MW49bH40lg_y6L-v-gtQ</string>
    <string name="google_storage_bucket" translatable="false">ecocode-51a9c.firebasestorage.app</string>
    <string name="project_id" translatable="false">ecocode-51a9c</string>
    <style name="LaunchTheme" parent="@android:style/Theme.Light.NoTitleBar">
        
        <item name="android:windowBackground">@drawable/launch_background</item>
        <item name="android:forceDarkAllowed">false</item>
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowDrawsSystemBarBackgrounds">false</item>
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
    </style>
    <style name="NormalTheme" parent="@android:style/Theme.Light.NoTitleBar">
        <item name="android:windowBackground">?android:colorBackground</item>
    </style>
</resources>