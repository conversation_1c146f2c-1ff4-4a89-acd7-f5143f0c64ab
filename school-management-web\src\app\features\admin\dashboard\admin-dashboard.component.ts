import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';

@Component({
  selector: 'app-admin-dashboard',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="admin-dashboard">
      <!-- Header -->
      <header class="dashboard-header">
        <div class="header-content">
          <div class="welcome-section">
            <h1>🛡️ Admin Dashboard</h1>
            <p>System overview and management center</p>
          </div>
          <div class="header-actions">
            <button class="notification-btn">
              <span class="icon">🔔</span>
              <span class="badge">5</span>
            </button>
            <button class="profile-btn" (click)="logout()">
              <span class="icon">👤</span>
              Logout
            </button>
          </div>
        </div>
      </header>

      <!-- System Stats -->
      <section class="stats-section">
        <div class="stats-grid">
          <div class="stat-card users">
            <div class="stat-icon">👥</div>
            <div class="stat-content">
              <h3>{{systemStats.totalUsers}}</h3>
              <p>Total Users</p>
              <span class="trend positive">+{{systemStats.newUsersThisMonth}} this month</span>
            </div>
          </div>
          <div class="stat-card students">
            <div class="stat-icon">🎓</div>
            <div class="stat-content">
              <h3>{{systemStats.totalStudents}}</h3>
              <p>Students</p>
              <span class="trend positive">+{{systemStats.newStudents}} new</span>
            </div>
          </div>
          <div class="stat-card teachers">
            <div class="stat-icon">👩‍🏫</div>
            <div class="stat-content">
              <h3>{{systemStats.totalTeachers}}</h3>
              <p>Teachers</p>
              <span class="trend neutral">{{systemStats.activeTeachers}} active</span>
            </div>
          </div>
          <div class="stat-card classes">
            <div class="stat-icon">🏫</div>
            <div class="stat-content">
              <h3>{{systemStats.totalClasses}}</h3>
              <p>Classes</p>
              <span class="trend positive">{{systemStats.activeClasses}} active</span>
            </div>
          </div>
        </div>
      </section>

      <!-- Main Content -->
      <div class="main-content">
        <!-- Quick Actions -->
        <section class="quick-actions-section">
          <h2>⚡ Quick Actions</h2>
          <div class="actions-grid">
            <button class="action-card" (click)="navigateToUsers()">
              <div class="action-icon users-icon">👥</div>
              <div class="action-content">
                <h3>User Management</h3>
                <p>Manage students, teachers, and parents</p>
                <span class="action-badge">{{pendingUsers}} pending</span>
              </div>
            </button>

            <button class="action-card" (click)="navigateToAcademic()">
              <div class="action-icon academic-icon">📚</div>
              <div class="action-content">
                <h3>Academic Management</h3>
                <p>Classes, subjects, and schedules</p>
                <span class="action-badge">{{pendingAcademic}} updates</span>
              </div>
            </button>

            <button class="action-card" (click)="navigateToReports()">
              <div class="action-icon reports-icon">📊</div>
              <div class="action-content">
                <h3>Reports & Analytics</h3>
                <p>System performance and insights</p>
                <span class="action-badge">{{newReports}} new</span>
              </div>
            </button>

            <button class="action-card" (click)="navigateToSettings()">
              <div class="action-icon settings-icon">⚙️</div>
              <div class="action-content">
                <h3>System Settings</h3>
                <p>Configure system preferences</p>
                <span class="action-badge">{{systemAlerts}} alerts</span>
              </div>
            </button>
          </div>
        </section>

        <!-- Recent Activities -->
        <section class="activities-section">
          <h2>📈 Recent System Activities</h2>
          <div class="activities-list">
            <div class="activity-item" *ngFor="let activity of recentActivities">
              <div class="activity-icon" [ngClass]="activity.type">{{activity.icon}}</div>
              <div class="activity-content">
                <h4>{{activity.title}}</h4>
                <p>{{activity.description}}</p>
                <span class="activity-time">{{activity.time}}</span>
              </div>
              <div class="activity-status" [ngClass]="activity.status">
                {{activity.status}}
              </div>
            </div>
          </div>
        </section>
      </div>

      <!-- System Health -->
      <section class="system-health">
        <h2>🔧 System Health</h2>
        <div class="health-grid">
          <div class="health-card">
            <div class="health-header">
              <span class="health-title">Server Status</span>
              <span class="health-status online">Online</span>
            </div>
            <div class="health-details">
              <div class="health-metric">
                <span class="metric-label">Uptime:</span>
                <span class="metric-value">99.9%</span>
              </div>
              <div class="health-metric">
                <span class="metric-label">Response Time:</span>
                <span class="metric-value">45ms</span>
              </div>
            </div>
          </div>

          <div class="health-card">
            <div class="health-header">
              <span class="health-title">Database</span>
              <span class="health-status online">Connected</span>
            </div>
            <div class="health-details">
              <div class="health-metric">
                <span class="metric-label">Queries/sec:</span>
                <span class="metric-value">1,247</span>
              </div>
              <div class="health-metric">
                <span class="metric-label">Storage:</span>
                <span class="metric-value">78% used</span>
              </div>
            </div>
          </div>

          <div class="health-card">
            <div class="health-header">
              <span class="health-title">Backup System</span>
              <span class="health-status online">Active</span>
            </div>
            <div class="health-details">
              <div class="health-metric">
                <span class="metric-label">Last Backup:</span>
                <span class="metric-value">2 hours ago</span>
              </div>
              <div class="health-metric">
                <span class="metric-label">Success Rate:</span>
                <span class="metric-value">100%</span>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  `,
  styles: [`
    .admin-dashboard {
      min-height: 100vh;
      background: #f5f7fa;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .dashboard-header {
      background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
      color: white;
      padding: 30px;
      margin-bottom: 30px;
    }

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      max-width: 1400px;
      margin: 0 auto;
    }

    .welcome-section h1 {
      margin: 0 0 8px 0;
      font-size: 28px;
      font-weight: 600;
    }

    .welcome-section p {
      margin: 0;
      opacity: 0.9;
      font-size: 16px;
    }

    .header-actions {
      display: flex;
      gap: 16px;
      align-items: center;
    }

    .notification-btn, .profile-btn {
      background: rgba(255, 255, 255, 0.2);
      border: none;
      color: white;
      padding: 12px 16px;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;
    }

    .notification-btn:hover, .profile-btn:hover {
      background: rgba(255, 255, 255, 0.3);
    }

    .badge {
      position: absolute;
      top: -5px;
      right: -5px;
      background: #e74c3c;
      color: white;
      border-radius: 50%;
      width: 20px;
      height: 20px;
      font-size: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .stats-section {
      max-width: 1400px;
      margin: 0 auto 40px auto;
      padding: 0 30px;
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 24px;
    }

    .stat-card {
      background: white;
      padding: 28px;
      border-radius: 16px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
      display: flex;
      align-items: center;
      gap: 20px;
      transition: transform 0.3s ease;
      border-left: 4px solid;
    }

    .stat-card:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .stat-card.users { border-left-color: #3498db; }
    .stat-card.students { border-left-color: #2ecc71; }
    .stat-card.teachers { border-left-color: #e67e22; }
    .stat-card.classes { border-left-color: #9b59b6; }

    .stat-icon {
      font-size: 36px;
      background: #f8f9fa;
      padding: 16px;
      border-radius: 12px;
      min-width: 68px;
      text-align: center;
    }

    .stat-content h3 {
      margin: 0 0 4px 0;
      font-size: 28px;
      font-weight: 700;
      color: #2c3e50;
    }

    .stat-content p {
      margin: 0 0 8px 0;
      color: #7f8c8d;
      font-size: 16px;
      font-weight: 500;
    }

    .trend {
      font-size: 12px;
      font-weight: 600;
      padding: 4px 8px;
      border-radius: 12px;
    }

    .trend.positive {
      background: #d4edda;
      color: #155724;
    }

    .trend.neutral {
      background: #e2e3e5;
      color: #383d41;
    }

    .main-content {
      max-width: 1400px;
      margin: 0 auto;
      padding: 0 30px;
      display: grid;
      grid-template-columns: 2fr 1fr;
      gap: 40px;
    }

    .quick-actions-section h2, .activities-section h2 {
      margin: 0 0 24px 0;
      color: #2c3e50;
      font-size: 22px;
      font-weight: 600;
    }

    .actions-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 20px;
      margin-bottom: 40px;
    }

    .action-card {
      background: white;
      border: none;
      border-radius: 16px;
      padding: 24px;
      cursor: pointer;
      transition: all 0.3s ease;
      text-align: left;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
      position: relative;
      overflow: hidden;
    }

    .action-card:hover {
      transform: translateY(-4px);
      box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
    }

    .action-icon {
      font-size: 32px;
      margin-bottom: 16px;
      padding: 12px;
      border-radius: 12px;
      width: fit-content;
    }

    .users-icon { background: rgba(52, 152, 219, 0.1); }
    .academic-icon { background: rgba(46, 204, 113, 0.1); }
    .reports-icon { background: rgba(155, 89, 182, 0.1); }
    .settings-icon { background: rgba(230, 126, 34, 0.1); }

    .action-content h3 {
      margin: 0 0 8px 0;
      color: #2c3e50;
      font-size: 18px;
      font-weight: 600;
    }

    .action-content p {
      margin: 0 0 12px 0;
      color: #7f8c8d;
      font-size: 14px;
      line-height: 1.5;
    }

    .action-badge {
      background: #e74c3c;
      color: white;
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 600;
    }

    .activities-list {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    .activity-item {
      background: white;
      padding: 20px;
      border-radius: 12px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
      display: flex;
      gap: 16px;
      align-items: center;
    }

    .activity-icon {
      font-size: 20px;
      padding: 10px;
      border-radius: 8px;
      min-width: 40px;
      text-align: center;
    }

    .activity-icon.user { background: rgba(52, 152, 219, 0.1); }
    .activity-icon.system { background: rgba(46, 204, 113, 0.1); }
    .activity-icon.alert { background: rgba(231, 76, 60, 0.1); }

    .activity-content {
      flex: 1;
    }

    .activity-content h4 {
      margin: 0 0 4px 0;
      color: #2c3e50;
      font-size: 16px;
      font-weight: 600;
    }

    .activity-content p {
      margin: 0 0 4px 0;
      color: #7f8c8d;
      font-size: 14px;
    }

    .activity-time {
      color: #bdc3c7;
      font-size: 12px;
    }

    .activity-status {
      padding: 4px 12px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 600;
    }

    .activity-status.completed {
      background: #d4edda;
      color: #155724;
    }

    .activity-status.pending {
      background: #fff3cd;
      color: #856404;
    }

    .activity-status.failed {
      background: #f8d7da;
      color: #721c24;
    }

    .system-health {
      max-width: 1400px;
      margin: 40px auto 0 auto;
      padding: 0 30px;
    }

    .system-health h2 {
      margin: 0 0 24px 0;
      color: #2c3e50;
      font-size: 22px;
      font-weight: 600;
    }

    .health-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 24px;
    }

    .health-card {
      background: white;
      border-radius: 16px;
      padding: 24px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    }

    .health-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
    }

    .health-title {
      font-weight: 600;
      color: #2c3e50;
      font-size: 16px;
    }

    .health-status {
      padding: 4px 12px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 600;
    }

    .health-status.online {
      background: #d4edda;
      color: #155724;
    }

    .health-details {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    .health-metric {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .metric-label {
      color: #7f8c8d;
      font-size: 14px;
    }

    .metric-value {
      color: #2c3e50;
      font-weight: 600;
      font-size: 14px;
    }

    @media (max-width: 768px) {
      .header-content {
        flex-direction: column;
        gap: 20px;
        text-align: center;
      }

      .main-content {
        grid-template-columns: 1fr;
        gap: 30px;
      }

      .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      }

      .actions-grid {
        grid-template-columns: 1fr;
      }

      .health-grid {
        grid-template-columns: 1fr;
      }
    }
  `]
})
export class AdminDashboardComponent {
  systemStats = {
    totalUsers: 2315,
    totalStudents: 1250,
    totalTeachers: 85,
    totalClasses: 45,
    newUsersThisMonth: 47,
    newStudents: 23,
    activeTeachers: 82,
    activeClasses: 43
  };

  pendingUsers = 12;
  pendingAcademic = 5;
  newReports = 8;
  systemAlerts = 3;

  recentActivities = [
    {
      icon: '👤',
      title: 'New Teacher Registration',
      description: 'Sarah Johnson registered as Mathematics teacher',
      time: '2 hours ago',
      type: 'user',
      status: 'completed'
    },
    {
      icon: '🔄',
      title: 'System Backup Completed',
      description: 'Daily backup completed successfully',
      time: '4 hours ago',
      type: 'system',
      status: 'completed'
    },
    {
      icon: '⚠️',
      title: 'Server Alert',
      description: 'High memory usage detected on server 2',
      time: '6 hours ago',
      type: 'alert',
      status: 'pending'
    },
    {
      icon: '📊',
      title: 'Monthly Report Generated',
      description: 'Academic performance report for November',
      time: '1 day ago',
      type: 'system',
      status: 'completed'
    }
  ];

  constructor(private router: Router) {}

  logout(): void {
    this.router.navigate(['/auth/login']);
  }

  navigateToUsers(): void {
    this.router.navigate(['/admin/users']);
  }

  navigateToAcademic(): void {
    this.router.navigate(['/admin/academic']);
  }

  navigateToReports(): void {
    this.router.navigate(['/admin/reports']);
  }

  navigateToSettings(): void {
    this.router.navigate(['/admin/settings']);
  }
}
