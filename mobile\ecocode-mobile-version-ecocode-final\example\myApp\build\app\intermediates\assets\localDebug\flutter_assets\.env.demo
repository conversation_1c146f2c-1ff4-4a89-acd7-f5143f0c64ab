baseURL='https://demo.ecocode.ovh/api'
keycloakUri='https://auth.demo.ecocode.ovh/realms/Eco-Code'
tokenUrl='https://auth.demo.ecocode.ovh/realms/Eco-Code/protocol/openid-connect/token'
logoutUrl='https://auth.demo.ecocode.ovh/realms/Eco-Code/protocol/openid-connect/logout'
introspectUrl='https://auth.demo.ecocode.ovh/realms/Eco-Code/protocol/openid-connect/token/introspect'
forgetPasswordURL='https://demo.ecocode.ovh'
clientId='ishrakschool-back'
clientSecret='cttRhoDIkNiFzqJc9jh0w3OsZGKswQ6t'
ipAddress='demo.ecocode.ovh'
facebookUrl='https://www.facebook.com/people/Luminous-Horizon/61561398011455/'
nameFacebook='Eco-code'
instagramUrl='https://www.facebook.com/people/Luminous-Horizon/61561398011455/'
nameInstagram='Eco.Code'
adresse1='Immeuble Fourat, Sfax, Tunisia'
adresse2=''
tel1='20000000'
tel2='50000000'
tel3='44000000'
tel4='90000000'
mail='<EMAIL>'
youtubeUrl='https://www.youtube.com/channel/UCGy0KpZu_8IetwVpDMP_zQw'
schoolLogo='assets/logos/ecocode.png'
dashURL='https://demo.ecocode.ovh/profile-eleve-parent'
backendLoginUrl='https://demo.ecocode.ovh/api/users/login'
backendLogoutUrl='https://demo.ecocode.ovh/api/users/logout'
confirmed=true
confirmedParent=true
niveaux='7ème-Année,8ème-Année,9ème-Année'
APP_NAME='DefaultSchool'
appPath= 'https://demo.ecocode.ovh/api/Apk/download?fileName=EcoCode.apk&path=apk'
message= "Mise à jour de l'app"
CARNET_NAME= 'Bulletin'