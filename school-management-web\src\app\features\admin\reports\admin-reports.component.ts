import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';

@Component({
  selector: 'app-admin-reports',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="admin-reports">
      <!-- Header -->
      <header class="page-header">
        <div class="header-content">
          <div class="header-left">
            <button class="back-btn" (click)="goBack()">
              <span class="icon">←</span>
              Back to Dashboard
            </button>
            <h1>📊 Reports & Analytics</h1>
            <p>System performance insights and detailed reports</p>
          </div>
        </div>
      </header>

      <!-- Content -->
      <section class="content-section">
        <div class="content-card">
          <h2>Reports & Analytics System</h2>
          <p>This section will contain:</p>
          <ul>
            <li>Academic Performance Reports</li>
            <li>Attendance Analytics</li>
            <li>Financial Reports</li>
            <li>System Performance Metrics</li>
          </ul>
          <p>Currently under development...</p>
        </div>
      </section>
    </div>
  `,
  styles: [`
    .admin-reports {
      min-height: 100vh;
      background: #f5f7fa;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .page-header {
      background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
      color: white;
      padding: 30px;
      margin-bottom: 30px;
    }

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      max-width: 1400px;
      margin: 0 auto;
    }

    .back-btn {
      background: rgba(255, 255, 255, 0.2);
      border: none;
      color: white;
      padding: 8px 16px;
      border-radius: 6px;
      cursor: pointer;
      margin-bottom: 16px;
      transition: all 0.3s ease;
    }

    .back-btn:hover {
      background: rgba(255, 255, 255, 0.3);
    }

    .header-left h1 {
      margin: 0 0 8px 0;
      font-size: 28px;
      font-weight: 600;
    }

    .header-left p {
      margin: 0;
      opacity: 0.9;
      font-size: 16px;
    }

    .content-section {
      max-width: 1400px;
      margin: 0 auto;
      padding: 0 30px;
    }

    .content-card {
      background: white;
      border-radius: 12px;
      padding: 40px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
      text-align: center;
    }

    .content-card h2 {
      color: #2c3e50;
      margin-bottom: 20px;
    }

    .content-card p {
      color: #7f8c8d;
      margin-bottom: 20px;
    }

    .content-card ul {
      text-align: left;
      max-width: 300px;
      margin: 0 auto 20px auto;
      color: #2c3e50;
    }

    .content-card li {
      margin-bottom: 8px;
    }
  `]
})
export class AdminReportsComponent {
  constructor(private router: Router) {}

  goBack(): void {
    this.router.navigate(['/admin']);
  }
}
