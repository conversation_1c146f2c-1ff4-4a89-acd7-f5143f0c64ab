export interface Course {
  id: number;
  title: string;
  description: string;
  teacherId: number;
  teacherName: string;
  classId: number;
  className: string;
  subject: string;
  createdDate: string;
  files?: CourseFile[];
}

export interface CourseFile {
  id: number;
  fileName: string;
  fileUrl: string;
  fileType: string;
  uploadDate: string;
  size: number;
}

export interface Exercise {
  id: number;
  title: string;
  description: string;
  courseId: number;
  courseName: string;
  teacherId: number;
  teacherName: string;
  dueDate: string;
  createdDate: string;
  maxScore: number;
  files?: ExerciseFile[];
  submissions?: ExerciseSubmission[];
}

export interface ExerciseFile {
  id: number;
  fileName: string;
  fileUrl: string;
  fileType: string;
  uploadDate: string;
}

export interface ExerciseSubmission {
  id: number;
  studentId: number;
  studentName: string;
  submissionDate: string;
  score?: number;
  feedback?: string;
  files?: SubmissionFile[];
}

export interface SubmissionFile {
  id: number;
  fileName: string;
  fileUrl: string;
  uploadDate: string;
}

export interface Observation {
  id: number;
  studentId: number;
  studentName: string;
  teacherId: number;
  teacherName: string;
  subject: string;
  content: string;
  type: 'positive' | 'negative' | 'neutral';
  date: string;
  isPrivate: boolean;
}

export interface Discipline {
  id: number;
  studentId: number;
  studentName: string;
  teacherId: number;
  teacherName: string;
  incident: string;
  description: string;
  severity: 'low' | 'medium' | 'high';
  action: string;
  date: string;
  resolved: boolean;
}

export interface Schedule {
  id: number;
  classId: number;
  className: string;
  subject: string;
  teacherId: number;
  teacherName: string;
  dayOfWeek: number; // 0 = Sunday, 1 = Monday, etc.
  startTime: string;
  endTime: string;
  room: string;
}

export interface Grade {
  id: number;
  studentId: number;
  subject: string;
  teacherId: number;
  teacherName: string;
  grade: number;
  maxGrade: number;
  type: 'exam' | 'quiz' | 'homework' | 'project';
  date: string;
  trimester: number;
  comments?: string;
}

export interface ReportCard {
  studentId: number;
  studentName: string;
  className: string;
  trimester: number;
  academicYear: string;
  grades: Grade[];
  averageGrade: number;
  rank: number;
  totalStudents: number;
  teacherComments: string;
  principalComments: string;
  generatedDate: string;
}
