const express = require('express');
const path = require('path');
const fs = require('fs');

const app = express();
const PORT = 4200;

// Serve static files from src directory
app.use(express.static(path.join(__dirname, 'src')));

// Simple route to serve the main HTML
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'src', 'index.html'));
});

// Catch all routes and serve index.html (for Angular routing)
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'src', 'index.html'));
});

app.listen(PORT, () => {
  console.log(`🚀 School Management System running at http://localhost:${PORT}`);
  console.log(`📱 Frontend development server started`);
  console.log(`🎨 No backend connection - Direct navigation enabled`);
});
