import { Routes } from '@angular/router';

export const teacherRoutes: Routes = [
  {
    path: '',
    loadComponent: () => import('./layout/teacher-layout.component').then(m => m.TeacherLayoutComponent),
    children: [
      {
        path: '',
        redirectTo: 'dashboard',
        pathMatch: 'full'
      },
      {
        path: 'dashboard',
        loadComponent: () => import('./dashboard/teacher-dashboard.component').then(m => m.TeacherDashboardComponent)
      },
      {
        path: 'classes',
        loadComponent: () => import('./teacher-classes.component').then(m => m.TeacherClassesComponent)
      },
      {
        path: 'courses',
        loadComponent: () => import('./teacher-courses.component').then(m => m.TeacherCoursesComponent)
      },
      {
        path: 'exercises',
        loadComponent: () => import('./teacher-exercises.component').then(m => m.TeacherExercisesComponent)
      },
      {
        path: 'grades',
        loadComponent: () => import('./teacher-grades.component').then(m => m.TeacherGradesComponent)
      },
      {
        path: 'attendance',
        loadComponent: () => import('./teacher-attendance.component').then(m => m.TeacherAttendanceComponent)
      },
      {
        path: 'observations',
        loadComponent: () => import('./teacher-observations.component').then(m => m.TeacherObservationsComponent)
      },
      {
        path: 'schedule',
        loadComponent: () => import('./teacher-schedule.component').then(m => m.TeacherScheduleComponent)
      },
      {
        path: 'messages',
        loadComponent: () => import('./teacher-messages.component').then(m => m.TeacherMessagesComponent)
      },
      {
        path: 'profile',
        loadComponent: () => import('./teacher-profile.component').then(m => m.TeacherProfileComponent)
      }
    ]
  }
];
