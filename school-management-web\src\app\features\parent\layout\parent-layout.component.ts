import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterOutlet, RouterLink, RouterLinkActive, Router } from '@angular/router';

@Component({
  selector: 'app-parent-layout',
  standalone: true,
  imports: [CommonModule, RouterOutlet, RouterLink, RouterLinkActive],
  template: `
    <div class="parent-layout">
      <!-- Header -->
      <header class="header">
        <div class="header-content">
          <div class="header-left">
            <button class="menu-toggle" (click)="toggleSidebar()">
              <span class="hamburger"></span>
              <span class="hamburger"></span>
              <span class="hamburger"></span>
            </button>
            <div class="logo">
              <span class="logo-icon">👨‍👩‍👧‍👦</span>
              <span class="logo-text">ParentPortal</span>
            </div>
          </div>
          <div class="header-right">
            <div class="user-info">
              <div class="user-avatar">👨‍👩‍👧‍👦</div>
              <div class="user-details">
                <span class="user-name"><PERSON> & <PERSON></span>
                <span class="user-role">Parents</span>
              </div>
            </div>
            <button class="logout-btn" (click)="logout()">
              <span class="logout-icon">🚪</span>
              Logout
            </button>
          </div>
        </div>
      </header>

      <!-- Sidebar -->
      <aside class="sidebar" [class.open]="isSidebarOpen">
        <nav class="sidebar-nav">
          <ul class="nav-list">
            <li class="nav-item">
              <a routerLink="/parent/dashboard" routerLinkActive="active" class="nav-link" (click)="closeSidebar()">
                <span class="nav-icon">🏠</span>
                <span class="nav-label">Dashboard</span>
              </a>
            </li>
            <li class="nav-item">
              <a routerLink="/parent/courses" routerLinkActive="active" class="nav-link" (click)="closeSidebar()">
                <span class="nav-icon">📚</span>
                <span class="nav-label">Courses</span>
              </a>
            </li>
            <li class="nav-item">
              <a routerLink="/parent/grades" routerLinkActive="active" class="nav-link" (click)="closeSidebar()">
                <span class="nav-icon">📊</span>
                <span class="nav-label">Grades</span>
              </a>
            </li>
            <li class="nav-item">
              <a routerLink="/parent/attendance" routerLinkActive="active" class="nav-link" (click)="closeSidebar()">
                <span class="nav-icon">📋</span>
                <span class="nav-label">Attendance</span>
              </a>
            </li>
            <li class="nav-item">
              <a routerLink="/parent/schedule" routerLinkActive="active" class="nav-link" (click)="closeSidebar()">
                <span class="nav-icon">📅</span>
                <span class="nav-label">Schedule</span>
              </a>
            </li>
            <li class="nav-item">
              <a routerLink="/parent/exercises" routerLinkActive="active" class="nav-link" (click)="closeSidebar()">
                <span class="nav-icon">📝</span>
                <span class="nav-label">Exercises</span>
              </a>
            </li>
            <li class="nav-item">
              <a routerLink="/parent/observations" routerLinkActive="active" class="nav-link" (click)="closeSidebar()">
                <span class="nav-icon">👁️</span>
                <span class="nav-label">Observations</span>
              </a>
            </li>
            <li class="nav-item">
              <a routerLink="/parent/discipline" routerLinkActive="active" class="nav-link" (click)="closeSidebar()">
                <span class="nav-icon">⚠️</span>
                <span class="nav-label">Discipline</span>
              </a>
            </li>
            <li class="nav-item">
              <a routerLink="/parent/payments" routerLinkActive="active" class="nav-link" (click)="closeSidebar()">
                <span class="nav-icon">💳</span>
                <span class="nav-label">Payments</span>
              </a>
            </li>
            <li class="nav-item">
              <a routerLink="/parent/messages" routerLinkActive="active" class="nav-link" (click)="closeSidebar()">
                <span class="nav-icon">💬</span>
                <span class="nav-label">Messages</span>
              </a>
            </li>
            <li class="nav-item">
              <a routerLink="/parent/profile" routerLinkActive="active" class="nav-link" (click)="closeSidebar()">
                <span class="nav-icon">👤</span>
                <span class="nav-label">Profile</span>
              </a>
            </li>
          </ul>
        </nav>
      </aside>

      <!-- Main Content -->
      <main class="main-content" [class.sidebar-open]="isSidebarOpen">
        <div class="content-wrapper">
          <router-outlet></router-outlet>
        </div>
      </main>

      <!-- Sidebar Overlay -->
      <div class="sidebar-overlay" [class.active]="isSidebarOpen" (click)="closeSidebar()"></div>
    </div>
  `,
  styles: [`
    .parent-layout {
      display: flex;
      flex-direction: column;
      min-height: 100vh;
      background: #f5f7fa;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 0 20px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      z-index: 1000;
      height: 70px;
    }

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 100%;
      max-width: 1400px;
      margin: 0 auto;
    }

    .header-left {
      display: flex;
      align-items: center;
      gap: 20px;
    }

    .menu-toggle {
      background: none;
      border: none;
      cursor: pointer;
      padding: 8px;
      border-radius: 6px;
      transition: background 0.3s ease;
    }

    .menu-toggle:hover {
      background: rgba(255, 255, 255, 0.1);
    }

    .hamburger {
      display: block;
      width: 20px;
      height: 2px;
      background: white;
      margin: 4px 0;
      transition: 0.3s;
    }

    .logo {
      display: flex;
      align-items: center;
      gap: 10px;
      font-weight: 600;
      font-size: 18px;
    }

    .logo-icon {
      font-size: 24px;
    }

    .header-right {
      display: flex;
      align-items: center;
      gap: 20px;
    }

    .user-info {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .user-avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.2);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 20px;
    }

    .user-details {
      display: flex;
      flex-direction: column;
    }

    .user-name {
      font-weight: 600;
      font-size: 14px;
    }

    .user-role {
      font-size: 12px;
      opacity: 0.8;
    }

    .logout-btn {
      background: rgba(255, 255, 255, 0.2);
      border: none;
      color: white;
      padding: 8px 16px;
      border-radius: 6px;
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 14px;
      transition: background 0.3s ease;
    }

    .logout-btn:hover {
      background: rgba(255, 255, 255, 0.3);
    }

    .sidebar {
      position: fixed;
      top: 70px;
      left: -280px;
      width: 280px;
      height: calc(100vh - 70px);
      background: white;
      box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
      transition: left 0.3s ease;
      z-index: 999;
      overflow-y: auto;
    }

    .sidebar.open {
      left: 0;
    }

    .sidebar-nav {
      padding: 20px 0;
    }

    .nav-list {
      list-style: none;
      padding: 0;
      margin: 0;
    }

    .nav-item {
      margin-bottom: 4px;
    }

    .nav-link {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px 20px;
      color: #64748b;
      text-decoration: none;
      transition: all 0.3s ease;
      border-right: 3px solid transparent;
    }

    .nav-link:hover {
      background: #f8fafc;
      color: #667eea;
    }

    .nav-link.active {
      background: #f0f4ff;
      color: #667eea;
      border-right-color: #667eea;
    }

    .nav-icon {
      font-size: 18px;
      width: 20px;
      text-align: center;
    }

    .nav-label {
      font-weight: 500;
    }

    .main-content {
      margin-top: 70px;
      padding: 30px;
      transition: margin-left 0.3s ease;
    }

    .main-content.sidebar-open {
      margin-left: 280px;
    }

    .content-wrapper {
      max-width: 1400px;
      margin: 0 auto;
    }

    .sidebar-overlay {
      position: fixed;
      top: 70px;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
      z-index: 998;
      opacity: 0;
      visibility: hidden;
      transition: all 0.3s ease;
    }

    .sidebar-overlay.active {
      opacity: 1;
      visibility: visible;
    }

    @media (max-width: 768px) {
      .user-details {
        display: none;
      }

      .main-content.sidebar-open {
        margin-left: 0;
      }

      .sidebar {
        width: 260px;
        left: -260px;
      }

      .header-content {
        padding: 0 10px;
      }

      .logo-text {
        display: none;
      }
    }

    @media (min-width: 1024px) {
      .sidebar {
        left: 0;
      }

      .main-content {
        margin-left: 280px;
      }

      .menu-toggle {
        display: none;
      }

      .sidebar-overlay {
        display: none;
      }
    }
  `]
})
export class ParentLayoutComponent {
  isSidebarOpen = false;

  constructor(private router: Router) {}

  toggleSidebar(): void {
    this.isSidebarOpen = !this.isSidebarOpen;
  }

  closeSidebar(): void {
    this.isSidebarOpen = false;
  }

  logout(): void {
    this.router.navigate(['/auth/login']);
  }
}
