baseURL='http://***************:2023/api'
keycloakUri='http://***************:9003/realms/Eco-Code'
tokenUrl='http://***************:9003/realms/Eco-Code/protocol/openid-connect/token'
logoutUrl='http://***************:9003/realms/Eco-Code/protocol/openid-connect/logout'
introspectUrl='http://***************:9003/realms/Eco-Code/protocol/openid-connect/token/introspect'
forgetPasswordURL='http://nova.ecocode.ovh'
clientId='ishrakschool-back'
clientSecret='H5aeWUy55G8POK2Lxhb0wQ1lLACOblej'
ipAddress='***************'
facebookUrl='https://www.facebook.com/Ecole.NovaSchool/'
nameFacebook='Ecole.NovaSchool'
instagramUrl='https://www.instagram.com/explore/locations/1014724412/nova-school/'
nameInstagram='nova-school'
adresse1='Route Gremda Km 5,5 , Sfax, Tunisia'
adresse2=''
tel1='21595852'
tel2=''
tel3=''
tel4=''
mail=''
youtubeUrl=''
schoolLogo='assets/logos/nova.png'
dashURL='http://nova.ecocode.ovh/profile-eleve-parent'
backendLoginUrl='http://***************:2023/api/users/login'
backendLogoutUrl='http://***************:2023/api/users/logout'
confirmed=false
confirmedParent=true
niveaux='Préparatoire,1ère-Année,2ème-Année,3ème-Année,4ème-Année,5ème-Année,6ème-Année'
APP_NAME='NovaSchool'
CARNET_NAME= 'Carnet'