1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.dexterous.flutterlocalnotifications" >
4
5    <uses-sdk android:minSdkVersion="16" />
6
7    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
7-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_local_notifications-15.1.3\android\src\main\AndroidManifest.xml:3:5-80
7-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_local_notifications-15.1.3\android\src\main\AndroidManifest.xml:3:22-78
8    <uses-permission android:name="android.permission.VIBRATE" />
8-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_local_notifications-15.1.3\android\src\main\AndroidManifest.xml:4:5-66
8-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_local_notifications-15.1.3\android\src\main\AndroidManifest.xml:4:22-63
9    <uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT" />
9-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_local_notifications-15.1.3\android\src\main\AndroidManifest.xml:5:5-81
9-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_local_notifications-15.1.3\android\src\main\AndroidManifest.xml:5:22-78
10    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
10-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_local_notifications-15.1.3\android\src\main\AndroidManifest.xml:6:5-79
10-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_local_notifications-15.1.3\android\src\main\AndroidManifest.xml:6:22-76
11    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
11-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_local_notifications-15.1.3\android\src\main\AndroidManifest.xml:7:5-76
11-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_local_notifications-15.1.3\android\src\main\AndroidManifest.xml:7:22-74
12
13    <application>
13-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_local_notifications-15.1.3\android\src\main\AndroidManifest.xml:8:5-19:19
14        <receiver
14-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_local_notifications-15.1.3\android\src\main\AndroidManifest.xml:9:9-125
15            android:name="com.dexterous.flutterlocalnotifications.ActionBroadcastReceiver"
15-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_local_notifications-15.1.3\android\src\main\AndroidManifest.xml:9:44-122
16            android:exported="false" />
16-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_local_notifications-15.1.3\android\src\main\AndroidManifest.xml:9:19-43
17        <receiver
17-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_local_notifications-15.1.3\android\src\main\AndroidManifest.xml:10:9-131
18            android:name="com.dexterous.flutterlocalnotifications.ScheduledNotificationReceiver"
18-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_local_notifications-15.1.3\android\src\main\AndroidManifest.xml:10:44-128
19            android:exported="false" />
19-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_local_notifications-15.1.3\android\src\main\AndroidManifest.xml:10:19-43
20        <receiver
20-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_local_notifications-15.1.3\android\src\main\AndroidManifest.xml:11:9-18:20
21            android:name="com.dexterous.flutterlocalnotifications.ScheduledNotificationBootReceiver"
21-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_local_notifications-15.1.3\android\src\main\AndroidManifest.xml:11:44-132
22            android:exported="false" >
22-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_local_notifications-15.1.3\android\src\main\AndroidManifest.xml:11:19-43
23            <intent-filter>
23-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_local_notifications-15.1.3\android\src\main\AndroidManifest.xml:12:13-17:29
24                <action android:name="android.intent.action.BOOT_COMPLETED" />
24-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_local_notifications-15.1.3\android\src\main\AndroidManifest.xml:13:17-78
24-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_local_notifications-15.1.3\android\src\main\AndroidManifest.xml:13:25-76
25                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
25-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_local_notifications-15.1.3\android\src\main\AndroidManifest.xml:14:17-83
25-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_local_notifications-15.1.3\android\src\main\AndroidManifest.xml:14:25-81
26                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
26-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_local_notifications-15.1.3\android\src\main\AndroidManifest.xml:15:17-82
26-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_local_notifications-15.1.3\android\src\main\AndroidManifest.xml:15:25-79
27                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
27-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_local_notifications-15.1.3\android\src\main\AndroidManifest.xml:16:17-81
27-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_local_notifications-15.1.3\android\src\main\AndroidManifest.xml:16:25-79
28            </intent-filter>
29        </receiver>
30    </application>
31
32</manifest>
