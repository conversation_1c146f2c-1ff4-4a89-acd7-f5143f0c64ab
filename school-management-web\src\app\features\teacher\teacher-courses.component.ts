import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';

@Component({
  selector: 'app-teacher-courses',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <div class="teacher-courses">
      <!-- Header -->
      <header class="page-header">
        <div class="header-content">
          <div class="header-left">
            <h1>📚 Course Management</h1>
            <p>Create, organize, and manage your course content and materials</p>
          </div>
          <div class="header-actions">
            <button class="action-btn" (click)="showAddModal = true">
              <span class="icon">➕</span>
              Add Course
            </button>
            <button class="action-btn secondary" (click)="importCourse()">
              <span class="icon">📥</span>
              Import
            </button>
          </div>
        </div>
      </header>

      <!-- Course Categories -->
      <section class="categories-section">
        <div class="category-tabs">
          <button 
            class="category-btn" 
            [class.active]="activeCategory === category"
            (click)="setActiveCategory(category)"
            *ngFor="let category of categories">
            {{category}} ({{getCourseCount(category)}})
          </button>
        </div>
      </section>

      <!-- Courses Grid -->
      <section class="courses-section">
        <div class="courses-grid">
          <div class="course-card" *ngFor="let course of getFilteredCourses()">
            <div class="course-header">
              <div class="course-icon">{{course.icon}}</div>
              <div class="course-status" [ngClass]="course.status">{{course.status}}</div>
            </div>
            <div class="course-content">
              <h3>{{course.title}}</h3>
              <p>{{course.description}}</p>
              <div class="course-meta">
                <span class="meta-item">
                  <span class="meta-icon">📅</span>
                  {{course.duration}}
                </span>
                <span class="meta-item">
                  <span class="meta-icon">👥</span>
                  {{course.students}} students
                </span>
                <span class="meta-item">
                  <span class="meta-icon">📖</span>
                  {{course.lessons}} lessons
                </span>
              </div>
              <div class="progress-section">
                <div class="progress-label">Course Progress</div>
                <div class="progress-bar">
                  <div class="progress-fill" [style.width.%]="course.progress"></div>
                </div>
                <div class="progress-text">{{course.progress}}% Complete</div>
              </div>
            </div>
            <div class="course-actions">
              <button class="course-btn primary" (click)="viewCourse(course)">
                <span class="icon">👁️</span>
                View
              </button>
              <button class="course-btn secondary" (click)="editCourse(course)">
                <span class="icon">✏️</span>
                Edit
              </button>
              <button class="course-btn" (click)="manageLessons(course)">
                <span class="icon">📝</span>
                Lessons
              </button>
            </div>
          </div>
        </div>
      </section>

      <!-- Course Statistics -->
      <section class="stats-section">
        <h2>📊 Course Statistics</h2>
        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-icon">📚</div>
            <div class="stat-content">
              <div class="stat-number">{{totalCourses}}</div>
              <div class="stat-label">Total Courses</div>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">👥</div>
            <div class="stat-content">
              <div class="stat-number">{{totalStudents}}</div>
              <div class="stat-label">Enrolled Students</div>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">✅</div>
            <div class="stat-content">
              <div class="stat-number">{{completedCourses}}</div>
              <div class="stat-label">Completed</div>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">🚀</div>
            <div class="stat-content">
              <div class="stat-number">{{activeCourses}}</div>
              <div class="stat-label">Active Courses</div>
            </div>
          </div>
        </div>
      </section>

      <!-- Add Course Modal -->
      <div class="modal-overlay" *ngIf="showAddModal" (click)="showAddModal = false">
        <div class="modal-content" (click)="$event.stopPropagation()">
          <div class="modal-header">
            <h2>➕ Create New Course</h2>
            <button class="close-btn" (click)="showAddModal = false">✕</button>
          </div>
          <form class="add-form">
            <div class="form-group">
              <label>Course Title</label>
              <input type="text" [(ngModel)]="newCourse.title" name="courseTitle" placeholder="Enter course title">
            </div>
            <div class="form-row">
              <div class="form-group">
                <label>Category</label>
                <select [(ngModel)]="newCourse.category" name="courseCategory">
                  <option value="">Select Category</option>
                  <option value="Mathematics">Mathematics</option>
                  <option value="Science">Science</option>
                  <option value="Languages">Languages</option>
                  <option value="Arts">Arts</option>
                </select>
              </div>
              <div class="form-group">
                <label>Duration</label>
                <input type="text" [(ngModel)]="newCourse.duration" name="courseDuration" placeholder="e.g., 8 weeks">
              </div>
            </div>
            <div class="form-group">
              <label>Description</label>
              <textarea [(ngModel)]="newCourse.description" name="courseDescription" rows="4" placeholder="Enter course description"></textarea>
            </div>
            <div class="form-row">
              <div class="form-group">
                <label>Difficulty Level</label>
                <select [(ngModel)]="newCourse.level" name="courseLevel">
                  <option value="">Select Level</option>
                  <option value="Beginner">Beginner</option>
                  <option value="Intermediate">Intermediate</option>
                  <option value="Advanced">Advanced</option>
                </select>
              </div>
              <div class="form-group">
                <label>Target Class</label>
                <select [(ngModel)]="newCourse.targetClass" name="targetClass">
                  <option value="">Select Class</option>
                  <option value="Grade 1">Grade 1</option>
                  <option value="Grade 2">Grade 2</option>
                  <option value="Grade 3">Grade 3</option>
                  <option value="Grade 4">Grade 4</option>
                  <option value="Grade 5">Grade 5</option>
                </select>
              </div>
            </div>
            <div class="modal-actions">
              <button type="button" class="cancel-btn" (click)="showAddModal = false">Cancel</button>
              <button type="button" class="save-btn" (click)="addCourse()">
                <span class="icon">💾</span>
                Create Course
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .teacher-courses {
      padding: 0;
      background: #f5f7fa;
      min-height: 100vh;
    }

    .page-header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 30px;
      margin: -30px -30px 30px -30px;
    }

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      max-width: 1400px;
      margin: 0 auto;
    }

    .header-left h1 {
      margin: 0 0 8px 0;
      font-size: 28px;
      font-weight: 600;
    }

    .header-left p {
      margin: 0;
      opacity: 0.9;
      font-size: 16px;
    }

    .header-actions {
      display: flex;
      gap: 12px;
    }

    .action-btn {
      background: rgba(255, 255, 255, 0.2);
      border: none;
      color: white;
      padding: 12px 20px;
      border-radius: 8px;
      cursor: pointer;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 8px;
      transition: all 0.3s ease;
    }

    .action-btn:hover {
      background: rgba(255, 255, 255, 0.3);
    }

    .action-btn.secondary {
      background: transparent;
      border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .categories-section {
      margin-bottom: 30px;
    }

    .category-tabs {
      display: flex;
      gap: 8px;
      overflow-x: auto;
      padding: 0 4px;
    }

    .category-btn {
      background: white;
      border: none;
      padding: 12px 20px;
      border-radius: 8px;
      cursor: pointer;
      font-weight: 500;
      color: #64748b;
      transition: all 0.3s ease;
      white-space: nowrap;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .category-btn.active {
      background: #667eea;
      color: white;
    }

    .courses-section {
      margin-bottom: 40px;
    }

    .courses-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
      gap: 24px;
    }

    .course-card {
      background: white;
      border-radius: 16px;
      padding: 24px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
      transition: transform 0.3s ease;
    }

    .course-card:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .course-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
    }

    .course-icon {
      width: 50px;
      height: 50px;
      border-radius: 12px;
      background: #f0f4ff;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
    }

    .course-status {
      padding: 4px 12px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 600;
    }

    .course-status.active {
      background: #d4edda;
      color: #155724;
    }

    .course-status.draft {
      background: #fff3cd;
      color: #856404;
    }

    .course-status.completed {
      background: #cce5ff;
      color: #004085;
    }

    .course-content h3 {
      margin: 0 0 8px 0;
      color: #2d3748;
      font-size: 18px;
    }

    .course-content p {
      margin: 0 0 16px 0;
      color: #718096;
      font-size: 14px;
      line-height: 1.5;
    }

    .course-meta {
      display: flex;
      flex-wrap: wrap;
      gap: 12px;
      margin-bottom: 16px;
    }

    .meta-item {
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 12px;
      color: #718096;
    }

    .meta-icon {
      font-size: 14px;
    }

    .progress-section {
      margin-bottom: 20px;
    }

    .progress-label {
      font-size: 12px;
      color: #718096;
      margin-bottom: 8px;
    }

    .progress-bar {
      width: 100%;
      height: 6px;
      background: #e2e8f0;
      border-radius: 3px;
      overflow: hidden;
      margin-bottom: 4px;
    }

    .progress-fill {
      height: 100%;
      background: #667eea;
      transition: width 0.3s ease;
    }

    .progress-text {
      font-size: 12px;
      color: #667eea;
      font-weight: 600;
    }

    .course-actions {
      display: flex;
      gap: 8px;
    }

    .course-btn {
      flex: 1;
      padding: 8px 12px;
      border-radius: 6px;
      cursor: pointer;
      font-size: 12px;
      font-weight: 500;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 4px;
      transition: all 0.3s ease;
    }

    .course-btn.primary {
      background: #667eea;
      color: white;
      border: none;
    }

    .course-btn.secondary {
      background: white;
      color: #667eea;
      border: 1px solid #667eea;
    }

    .course-btn:not(.primary):not(.secondary) {
      background: #f8fafc;
      color: #64748b;
      border: 1px solid #e2e8f0;
    }

    .course-btn:hover {
      transform: translateY(-1px);
    }

    .stats-section {
      background: white;
      border-radius: 16px;
      padding: 24px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
      margin-bottom: 40px;
    }

    .stats-section h2 {
      margin: 0 0 24px 0;
      color: #2d3748;
      font-size: 20px;
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
    }

    .stat-card {
      background: #f8fafc;
      border-radius: 12px;
      padding: 20px;
      display: flex;
      align-items: center;
      gap: 16px;
    }

    .stat-icon {
      width: 50px;
      height: 50px;
      border-radius: 12px;
      background: #667eea;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
    }

    .stat-number {
      font-size: 24px;
      font-weight: 700;
      color: #2d3748;
      margin-bottom: 4px;
    }

    .stat-label {
      font-size: 14px;
      color: #718096;
    }

    /* Modal Styles */
    .modal-overlay {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1000;
    }

    .modal-content {
      background: white;
      border-radius: 12px;
      width: 90%;
      max-width: 600px;
      max-height: 90vh;
      overflow-y: auto;
    }

    .modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 24px;
      border-bottom: 1px solid #e2e8f0;
    }

    .modal-header h2 {
      margin: 0;
      color: #2d3748;
    }

    .close-btn {
      background: none;
      border: none;
      font-size: 20px;
      cursor: pointer;
      color: #718096;
    }

    .add-form {
      padding: 24px;
    }

    .form-row {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 16px;
      margin-bottom: 16px;
    }

    .form-group {
      display: flex;
      flex-direction: column;
      margin-bottom: 16px;
    }

    .form-group label {
      margin-bottom: 8px;
      font-weight: 500;
      color: #2d3748;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
      padding: 12px;
      border: 2px solid #e2e8f0;
      border-radius: 6px;
      font-size: 14px;
    }

    .form-group textarea {
      resize: vertical;
      min-height: 100px;
    }

    .modal-actions {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
      margin-top: 24px;
      padding-top: 24px;
      border-top: 1px solid #e2e8f0;
    }

    .cancel-btn {
      background: #6c757d;
      border: none;
      color: white;
      padding: 12px 24px;
      border-radius: 6px;
      cursor: pointer;
    }

    .save-btn {
      background: #667eea;
      border: none;
      color: white;
      padding: 12px 24px;
      border-radius: 6px;
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    @media (max-width: 768px) {
      .header-content {
        flex-direction: column;
        gap: 20px;
        text-align: center;
      }

      .courses-grid {
        grid-template-columns: 1fr;
      }

      .stats-grid {
        grid-template-columns: repeat(2, 1fr);
      }

      .form-row {
        grid-template-columns: 1fr;
      }

      .course-actions {
        flex-direction: column;
      }
    }
  `]
})
export class TeacherCoursesComponent {
  activeCategory = 'All';
  showAddModal = false;

  categories = ['All', 'Mathematics', 'Science', 'Languages', 'Arts'];

  newCourse = {
    title: '',
    category: '',
    duration: '',
    description: '',
    level: '',
    targetClass: ''
  };

  courses = [
    {
      id: 1,
      title: 'Advanced Algebra',
      description: 'Comprehensive algebra course covering quadratic equations, polynomials, and functions.',
      category: 'Mathematics',
      icon: '🔢',
      status: 'active',
      duration: '12 weeks',
      students: 28,
      lessons: 24,
      progress: 75,
      level: 'Advanced'
    },
    {
      id: 2,
      title: 'Basic Geometry',
      description: 'Introduction to geometric shapes, angles, and spatial reasoning.',
      category: 'Mathematics',
      icon: '📐',
      status: 'active',
      duration: '8 weeks',
      students: 32,
      lessons: 16,
      progress: 45,
      level: 'Beginner'
    },
    {
      id: 3,
      title: 'Science Fundamentals',
      description: 'Basic scientific principles and laboratory techniques.',
      category: 'Science',
      icon: '🔬',
      status: 'draft',
      duration: '10 weeks',
      students: 0,
      lessons: 20,
      progress: 20,
      level: 'Beginner'
    },
    {
      id: 4,
      title: 'English Literature',
      description: 'Reading comprehension and literary analysis for young learners.',
      category: 'Languages',
      icon: '📚',
      status: 'completed',
      duration: '6 weeks',
      students: 25,
      lessons: 12,
      progress: 100,
      level: 'Intermediate'
    }
  ];

  totalCourses = this.courses.length;
  totalStudents = this.courses.reduce((sum, course) => sum + course.students, 0);
  completedCourses = this.courses.filter(c => c.status === 'completed').length;
  activeCourses = this.courses.filter(c => c.status === 'active').length;

  constructor(private router: Router) {}

  setActiveCategory(category: string): void {
    this.activeCategory = category;
  }

  getCourseCount(category: string): number {
    if (category === 'All') return this.courses.length;
    return this.courses.filter(c => c.category === category).length;
  }

  getFilteredCourses() {
    if (this.activeCategory === 'All') return this.courses;
    return this.courses.filter(c => c.category === this.activeCategory);
  }

  viewCourse(course: any): void {
    console.log('View course:', course);
  }

  editCourse(course: any): void {
    console.log('Edit course:', course);
  }

  manageLessons(course: any): void {
    console.log('Manage lessons:', course);
  }

  addCourse(): void {
    if (this.newCourse.title && this.newCourse.category) {
      const newCourse = {
        id: this.courses.length + 1,
        ...this.newCourse,
        icon: '📖',
        status: 'draft',
        students: 0,
        lessons: 0,
        progress: 0
      };
      this.courses.push(newCourse);
      this.showAddModal = false;
      this.newCourse = { title: '', category: '', duration: '', description: '', level: '', targetClass: '' };
    }
  }

  importCourse(): void {
    console.log('Import course');
  }
}
