#Sun Jun 01 10:25:46 CET 2025
base.0=C\:\\Users\\benyo\\Desktop\\pfe\\mobile\\ecocode-mobile-version-ecocode-final\\example\\myApp\\build\\app\\intermediates\\dex\\demoDebug\\mergeExtDexDemoDebug\\classes.dex
base.1=C\:\\Users\\benyo\\Desktop\\pfe\\mobile\\ecocode-mobile-version-ecocode-final\\example\\myApp\\build\\app\\intermediates\\dex\\demoDebug\\mergeLibDexDemoDebug\\0\\classes.dex
base.2=C\:\\Users\\benyo\\Desktop\\pfe\\mobile\\ecocode-mobile-version-ecocode-final\\example\\myApp\\build\\app\\intermediates\\dex\\demoDebug\\mergeProjectDexDemoDebug\\0\\classes.dex
base.3=C\:\\Users\\benyo\\Desktop\\pfe\\mobile\\ecocode-mobile-version-ecocode-final\\example\\myApp\\build\\app\\intermediates\\dex\\demoDebug\\mergeProjectDexDemoDebug\\1\\classes.dex
base.4=C\:\\Users\\benyo\\Desktop\\pfe\\mobile\\ecocode-mobile-version-ecocode-final\\example\\myApp\\build\\app\\intermediates\\dex\\demoDebug\\mergeProjectDexDemoDebug\\3\\classes.dex
base.5=C\:\\Users\\benyo\\Desktop\\pfe\\mobile\\ecocode-mobile-version-ecocode-final\\example\\myApp\\build\\app\\intermediates\\desugar_lib_dex\\demoDebug\\classes1000.dex
base.6=C\:\\Users\\benyo\\Desktop\\pfe\\mobile\\ecocode-mobile-version-ecocode-final\\example\\myApp\\build\\app\\intermediates\\dex\\demoDebug\\mergeExtDexDemoDebug\\classes2.dex
path.0=classes.dex
path.1=0/classes.dex
path.2=0/classes.dex
path.3=1/classes.dex
path.4=3/classes.dex
path.5=classes1000.dex
path.6=classes2.dex
renamed.0=classes.dex
renamed.1=classes2.dex
renamed.2=classes3.dex
renamed.3=classes4.dex
renamed.4=classes5.dex
renamed.5=classes6.dex
renamed.6=classes7.dex
