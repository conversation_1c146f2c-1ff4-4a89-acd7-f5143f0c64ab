-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:2:1-57:12
MERGED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:2:1-57:12
INJECTED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\debug\AndroidManifest.xml:1:1-7:12
MERGED from [:device_info_plus] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\device_info_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:file_picker] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-15:12
MERGED from [:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-48:12
MERGED from [:firebase_core] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-15:12
MERGED from [:flutter_appauth] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\flutter_appauth\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:flutter_local_notifications] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-10:12
MERGED from [:flutter_plugin_android_lifecycle] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:open_file_android] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-22:12
MERGED from [:package_info_plus] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\package_info_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:path_provider_android] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\path_provider_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:permission_handler_android] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\permission_handler_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:shared_preferences_android] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\shared_preferences_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:url_launcher_android] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-14:12
MERGED from [net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-3\e29af6bba5b83fbf9d8cabbd1c723f32\transformed\jetified-appauth-0.11.1\AndroidManifest.xml:15:1-55:12
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\b47f2b5b543d4723cc676f4f1e95e1d1\transformed\preference-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\6b81f176e99a87272c49b1d24ec2636b\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\57c1c0bacecfc631670f89f9a212e268\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media:media:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\929ae1d3a00f8bfd8a9a1aa81922ea47\transformed\media-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\7be7d9406e844f92d0315bd988141b00\transformed\browser-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5b162dcac78a2ae956fb790147e5268a\transformed\recyclerview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\35d9bc2825917f4fa5a86b8685aceaef\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7f068f3e2ec828343cc799d40f9ce1e8\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\dad65cb79b3eafd50888ad50598d546f\transformed\jetified-window-1.2.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\dc9a77ed5dd0b6940a06943f0f7f17f3\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:17:1-21:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8ac65448400caaf51e69aa5731ae827d\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\3781b3f5f293231fa7900d0aab7193f7\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\18bb7e320acb9ef5a96e99eb70fb4294\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\90fc833d7144422c05e3a02e52e95951\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac0dcf16ce6a281444fe134795f48771\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:17:1-66:12
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\16b44a5a27378eb99bc553b95273bac9\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ae41e01eb7ae5d989b223de0fe61f461\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9e6da811dcfb7c615f8acdd62216d271\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\d562aebb24343d1e57f6f592d224a222\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8688e9838b51b6004ba54c59160ddf37\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:16:1-24:12
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\b34daed25f56462bad67b11d215f3292\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:2:1-13:12
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\b5a7752b3c21558c9095035a6f485012\transformed\jetified-firebase-installations-interop-17.1.1\AndroidManifest.xml:15:1-19:12
MERGED from [androidx.activity:activity-ktx:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\677de5ef1bdc5021054b23c4f82efbfc\transformed\jetified-activity-ktx-1.8.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\549f9b90956411ff250116223d81245e\transformed\jetified-activity-1.8.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\5fa136fdbede6ef6d6f99b3285df665c\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\55ab5e14a12edb40d604c3f01c30bfa1\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\84a1f0bcfe152ccdaf13c37306e5b0bf\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\48dfe54ee11691e593911c4000478cd3\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\43788c4f69b8d733c6886edcc684cbb5\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\9b4ce9a8dd0bfdf36dc12e61413d2220\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\602536ad6d4cf65cd359d6e3a305b521\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\272c9e61cab3e1b810d6805a5c1049e2\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\60765e497738b05773a285ac784eea4a\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\b706e9705966f9637699d6002c5f2d79\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\363db97abb4332fc93a163ff582cf337\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\1c916160872209709351e900651600df\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\1dba10a7577fa8f3baef16d4849e230d\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\8d129809cd8c466c0da9d5b65b255a5e\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\5fce665e74d156cb4614eabc8109e8d2\transformed\jetified-datastore-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\3e35dcaaccaf76afd6dde08b5fc6dc43\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\a81817003301d1a7d9398c1572b117f7\transformed\jetified-datastore-release\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\cdc318cb2477879731a20fd9682464f0\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:2:1-6:12
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a3165185f2d525d0fdc5467b35c687be\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\1f8ae2ae03c8f3ae6e8dc60fe5ef01da\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\4a1a08cc053a416e875e44f0df90f81c\transformed\fragment-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\6214f9b462fa5f1ec4b967dbc077ec23\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\067ce996d8d53be284974b2c2bb62db9\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\3eb6b6d199d47eea7c054efb88366fea\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\41121f6ec7a0d1786ef266124ab1a051\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d4beccfb936038cb8ed9ff0bd118a86\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\6a9804ab3caa4b86866a72723fd7c017\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\a467c1fd402840055b9892cad66e8764\transformed\transition-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4b3df5ffd582fc5d3d1345b770167516\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b34a0e6b1445bd902b4e8da9c77f9b3b\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\42462ff615a470fce269a489eb930f99\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\2afa94d1bcf8f854b341558bc43bcbb1\transformed\localbroadcastmanager-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f2c03ffff95df5ec6d0d921ca84310cb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\d7cf2d8404d700a18769a5d26d9a8c78\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\57c7a04d7dd3753416a7c36462bbb837\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\747fe02755efcbd0fb0f05fdf22f79d2\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7f6b68a543c2723e1da7a8e2cbf66386\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\761ecc1845ab25429b41b0bdd14c6124\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\54b1f48bf7938497eab3d09f3bb48e20\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8123fac3812cf931f1b1f4781b85647f\transformed\jetified-firebase-components-18.0.0\AndroidManifest.xml:15:1-20:12
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\176032b12d74ea1bfa0ece5bf0bd4f5c\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:15:1-31:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\32400d416bc1421387762890a1a0fc16\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:15:1-37:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e0e01d3a9c3b9abcfbd97fb470620c45\transformed\jetified-firebase-encoders-json-18.0.0\AndroidManifest.xml:15:1-23:12
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\2575da579be80ed57e233552a065f6f6\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c34b64293857da693d70f4ce53cbf342\transformed\jetified-transport-api-3.1.0\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f71465aebc0275adf0f55c66f598aacb\transformed\jetified-core-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\945b6820a33dfafd1f38d971d570ec9d\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\60d8239bd87c5d376d9dd5e247866b4b\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\166abcccb7d371e29d27ff7a9d39d011\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.multidex:multidex:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\9ea93decceb8e34f901d2477c6cfb905\transformed\multidex-2.0.1\AndroidManifest.xml:17:1-22:12
	package
		ADDED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:3:1-33
		INJECTED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\debug\AndroidManifest.xml
	android:versionCode
		INJECTED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:4:5-67
MERGED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:4:5-67
MERGED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:4:5-67
MERGED from [:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-67
MERGED from [:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-67
MERGED from [net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-3\e29af6bba5b83fbf9d8cabbd1c723f32\transformed\jetified-appauth-0.11.1\AndroidManifest.xml:22:5-67
MERGED from [net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-3\e29af6bba5b83fbf9d8cabbd1c723f32\transformed\jetified-appauth-0.11.1\AndroidManifest.xml:22:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\16b44a5a27378eb99bc553b95273bac9\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\16b44a5a27378eb99bc553b95273bac9\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\b34daed25f56462bad67b11d215f3292\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\b34daed25f56462bad67b11d215f3292\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\32400d416bc1421387762890a1a0fc16\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:25:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\32400d416bc1421387762890a1a0fc16\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:25:5-67
	android:name
		ADDED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:4:22-64
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:5:5-80
	android:name
		ADDED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:5:22-77
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:6:5-81
	android:name
		ADDED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:6:22-78
uses-permission#android.permission.MANAGE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:7:5-82
	android:name
		ADDED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:7:22-79
uses-permission#android.permission.REQUEST_INSTALL_PACKAGES
ADDED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:8:5-83
	android:name
		ADDED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:8:22-80
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:9:5-80
	android:name
		ADDED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:9:22-78
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:10:5-76
	android:name
		ADDED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:10:22-74
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:11:5-76
MERGED from [:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:11:5-77
MERGED from [:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:11:5-77
MERGED from [:flutter_local_notifications] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:8:5-77
MERGED from [:flutter_local_notifications] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:8:5-77
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac0dcf16ce6a281444fe134795f48771\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:23:5-77
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac0dcf16ce6a281444fe134795f48771\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:23:5-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:11:22-74
uses-permission#android.permission.VIBRATE
ADDED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:12:5-65
MERGED from [:flutter_local_notifications] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-66
MERGED from [:flutter_local_notifications] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-66
	android:name
		ADDED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:12:22-63
uses-permission#android.permission.WAKE_LOCK
ADDED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:13:5-68
MERGED from [:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:8:5-68
MERGED from [:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:8:5-68
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac0dcf16ce6a281444fe134795f48771\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:24:5-68
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac0dcf16ce6a281444fe134795f48771\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\b34daed25f56462bad67b11d215f3292\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\b34daed25f56462bad67b11d215f3292\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:9:5-68
	android:name
		ADDED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:13:22-65
uses-permission#com.google.android.c2dm.permission.RECEIVE
ADDED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:14:5-82
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac0dcf16ce6a281444fe134795f48771\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:26:5-82
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac0dcf16ce6a281444fe134795f48771\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:26:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\b34daed25f56462bad67b11d215f3292\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:11:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\b34daed25f56462bad67b11d215f3292\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:11:5-82
	android:name
		ADDED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:14:22-79
application
ADDED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:15:5-49:19
INJECTED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\debug\AndroidManifest.xml
MERGED from [:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:13:5-46:19
MERGED from [:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:13:5-46:19
MERGED from [:firebase_core] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_core] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-13:19
MERGED from [:open_file_android] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:8:5-20:19
MERGED from [:open_file_android] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:8:5-20:19
MERGED from [:url_launcher_android] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-12:19
MERGED from [:url_launcher_android] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-12:19
MERGED from [net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-3\e29af6bba5b83fbf9d8cabbd1c723f32\transformed\jetified-appauth-0.11.1\AndroidManifest.xml:34:5-53:19
MERGED from [net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-3\e29af6bba5b83fbf9d8cabbd1c723f32\transformed\jetified-appauth-0.11.1\AndroidManifest.xml:34:5-53:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\dad65cb79b3eafd50888ad50598d546f\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\dad65cb79b3eafd50888ad50598d546f\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\18bb7e320acb9ef5a96e99eb70fb4294\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\18bb7e320acb9ef5a96e99eb70fb4294\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac0dcf16ce6a281444fe134795f48771\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac0dcf16ce6a281444fe134795f48771\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\16b44a5a27378eb99bc553b95273bac9\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\16b44a5a27378eb99bc553b95273bac9\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ae41e01eb7ae5d989b223de0fe61f461\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ae41e01eb7ae5d989b223de0fe61f461\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9e6da811dcfb7c615f8acdd62216d271\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9e6da811dcfb7c615f8acdd62216d271\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\d562aebb24343d1e57f6f592d224a222\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\d562aebb24343d1e57f6f592d224a222\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8688e9838b51b6004ba54c59160ddf37\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8688e9838b51b6004ba54c59160ddf37\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\5fa136fdbede6ef6d6f99b3285df665c\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\5fa136fdbede6ef6d6f99b3285df665c\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\363db97abb4332fc93a163ff582cf337\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\363db97abb4332fc93a163ff582cf337\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\cdc318cb2477879731a20fd9682464f0\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\cdc318cb2477879731a20fd9682464f0\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:5:5-20
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a3165185f2d525d0fdc5467b35c687be\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a3165185f2d525d0fdc5467b35c687be\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\1f8ae2ae03c8f3ae6e8dc60fe5ef01da\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\1f8ae2ae03c8f3ae6e8dc60fe5ef01da\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:20:5-24:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\42462ff615a470fce269a489eb930f99\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\42462ff615a470fce269a489eb930f99\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f2c03ffff95df5ec6d0d921ca84310cb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f2c03ffff95df5ec6d0d921ca84310cb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\d7cf2d8404d700a18769a5d26d9a8c78\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\d7cf2d8404d700a18769a5d26d9a8c78\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\761ecc1845ab25429b41b0bdd14c6124\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\761ecc1845ab25429b41b0bdd14c6124\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\176032b12d74ea1bfa0ece5bf0bd4f5c\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\176032b12d74ea1bfa0ece5bf0bd4f5c\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\32400d416bc1421387762890a1a0fc16\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\32400d416bc1421387762890a1a0fc16\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\2575da579be80ed57e233552a065f6f6\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\2575da579be80ed57e233552a065f6f6\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:25:5-39:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\debug\AndroidManifest.xml
	android:requestLegacyExternalStorage
		ADDED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:20:9-52
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\42462ff615a470fce269a489eb930f99\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:label
		ADDED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:16:9-41
	android:icon
		ADDED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:18:9-36
	android:enableOnBackInvokedCallback
		ADDED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:19:9-51
	android:name
		ADDED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:17:9-61
activity#com.example.NovaSchool.MainActivity
ADDED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:21:9-44:20
	android:launchMode
		ADDED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:24:13-43
	android:hardwareAccelerated
		ADDED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:27:13-47
	android:windowSoftInputMode
		ADDED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:28:13-55
	android:exported
		ADDED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:23:13-36
	android:configChanges
		ADDED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:26:13-163
	android:theme
		ADDED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:25:13-47
	android:name
		ADDED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:22:13-41
meta-data#io.flutter.embedding.android.NormalTheme
ADDED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:30:13-33:19
	android:resource
		ADDED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:32:17-54
	android:name
		ADDED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:31:17-72
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:34:13-37:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:35:17-68
	android:name
		ADDED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:35:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:36:17-76
	android:name
		ADDED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:36:27-74
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:callback+data:scheme:com.example.NovaSchool
ADDED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:38:13-43:29
action#android.intent.action.VIEW
ADDED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:39:17-68
	android:name
		ADDED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:39:25-66
category#android.intent.category.DEFAULT
ADDED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:40:17-75
	android:name
		ADDED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:40:27-73
category#android.intent.category.BROWSABLE
ADDED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:41:17-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:41:27-75
data
ADDED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:42:17-89
	android:host
		ADDED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:42:63-86
	android:scheme
		ADDED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:42:23-62
meta-data#flutterEmbedding
ADDED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:46:9-48:33
	android:value
		ADDED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:48:13-30
	android:name
		ADDED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:47:13-44
queries
ADDED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:51:5-56:15
MERGED from [:file_picker] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-13:15
MERGED from [:file_picker] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-13:15
MERGED from [net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-3\e29af6bba5b83fbf9d8cabbd1c723f32\transformed\jetified-appauth-0.11.1\AndroidManifest.xml:24:5-32:15
MERGED from [net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-3\e29af6bba5b83fbf9d8cabbd1c723f32\transformed\jetified-appauth-0.11.1\AndroidManifest.xml:24:5-32:15
intent#action:name:android.intent.action.PROCESS_TEXT+data:mimeType:text/plain
ADDED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:52:9-55:18
action#android.intent.action.PROCESS_TEXT
ADDED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:53:13-72
	android:name
		ADDED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\main\AndroidManifest.xml:53:21-70
uses-sdk
INJECTED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\debug\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\debug\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\debug\AndroidManifest.xml
MERGED from [:device_info_plus] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\device_info_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:device_info_plus] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\device_info_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:file_picker] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:file_picker] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:firebase_core] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:firebase_core] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:flutter_appauth] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\flutter_appauth\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:flutter_appauth] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\flutter_appauth\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:flutter_local_notifications] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:flutter_local_notifications] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:flutter_plugin_android_lifecycle] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:flutter_plugin_android_lifecycle] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:open_file_android] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:6:5-44
MERGED from [:open_file_android] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:6:5-44
MERGED from [:package_info_plus] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\package_info_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:package_info_plus] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\package_info_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\path_provider_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\path_provider_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:permission_handler_android] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\permission_handler_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:permission_handler_android] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\permission_handler_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\shared_preferences_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\shared_preferences_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:url_launcher_android] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:url_launcher_android] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-3\e29af6bba5b83fbf9d8cabbd1c723f32\transformed\jetified-appauth-0.11.1\AndroidManifest.xml:18:5-20:41
MERGED from [net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-3\e29af6bba5b83fbf9d8cabbd1c723f32\transformed\jetified-appauth-0.11.1\AndroidManifest.xml:18:5-20:41
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\b47f2b5b543d4723cc676f4f1e95e1d1\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\b47f2b5b543d4723cc676f4f1e95e1d1\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\6b81f176e99a87272c49b1d24ec2636b\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\6b81f176e99a87272c49b1d24ec2636b\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\57c1c0bacecfc631670f89f9a212e268\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\57c1c0bacecfc631670f89f9a212e268\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media:media:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\929ae1d3a00f8bfd8a9a1aa81922ea47\transformed\media-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\929ae1d3a00f8bfd8a9a1aa81922ea47\transformed\media-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\7be7d9406e844f92d0315bd988141b00\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\7be7d9406e844f92d0315bd988141b00\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5b162dcac78a2ae956fb790147e5268a\transformed\recyclerview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5b162dcac78a2ae956fb790147e5268a\transformed\recyclerview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\35d9bc2825917f4fa5a86b8685aceaef\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\35d9bc2825917f4fa5a86b8685aceaef\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7f068f3e2ec828343cc799d40f9ce1e8\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7f068f3e2ec828343cc799d40f9ce1e8\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\dad65cb79b3eafd50888ad50598d546f\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\dad65cb79b3eafd50888ad50598d546f\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\dc9a77ed5dd0b6940a06943f0f7f17f3\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\dc9a77ed5dd0b6940a06943f0f7f17f3\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8ac65448400caaf51e69aa5731ae827d\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8ac65448400caaf51e69aa5731ae827d\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\3781b3f5f293231fa7900d0aab7193f7\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\3781b3f5f293231fa7900d0aab7193f7\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\18bb7e320acb9ef5a96e99eb70fb4294\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\18bb7e320acb9ef5a96e99eb70fb4294\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\90fc833d7144422c05e3a02e52e95951\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\90fc833d7144422c05e3a02e52e95951\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac0dcf16ce6a281444fe134795f48771\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac0dcf16ce6a281444fe134795f48771\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\16b44a5a27378eb99bc553b95273bac9\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\16b44a5a27378eb99bc553b95273bac9\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ae41e01eb7ae5d989b223de0fe61f461\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ae41e01eb7ae5d989b223de0fe61f461\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9e6da811dcfb7c615f8acdd62216d271\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9e6da811dcfb7c615f8acdd62216d271\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\d562aebb24343d1e57f6f592d224a222\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\d562aebb24343d1e57f6f592d224a222\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8688e9838b51b6004ba54c59160ddf37\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8688e9838b51b6004ba54c59160ddf37\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\b34daed25f56462bad67b11d215f3292\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\b34daed25f56462bad67b11d215f3292\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\b5a7752b3c21558c9095035a6f485012\transformed\jetified-firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\b5a7752b3c21558c9095035a6f485012\transformed\jetified-firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [androidx.activity:activity-ktx:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\677de5ef1bdc5021054b23c4f82efbfc\transformed\jetified-activity-ktx-1.8.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\677de5ef1bdc5021054b23c4f82efbfc\transformed\jetified-activity-ktx-1.8.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\549f9b90956411ff250116223d81245e\transformed\jetified-activity-1.8.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\549f9b90956411ff250116223d81245e\transformed\jetified-activity-1.8.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\5fa136fdbede6ef6d6f99b3285df665c\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\5fa136fdbede6ef6d6f99b3285df665c\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\55ab5e14a12edb40d604c3f01c30bfa1\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\55ab5e14a12edb40d604c3f01c30bfa1\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\84a1f0bcfe152ccdaf13c37306e5b0bf\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\84a1f0bcfe152ccdaf13c37306e5b0bf\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\48dfe54ee11691e593911c4000478cd3\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\48dfe54ee11691e593911c4000478cd3\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\43788c4f69b8d733c6886edcc684cbb5\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\43788c4f69b8d733c6886edcc684cbb5\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\9b4ce9a8dd0bfdf36dc12e61413d2220\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\9b4ce9a8dd0bfdf36dc12e61413d2220\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\602536ad6d4cf65cd359d6e3a305b521\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\602536ad6d4cf65cd359d6e3a305b521\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\272c9e61cab3e1b810d6805a5c1049e2\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\272c9e61cab3e1b810d6805a5c1049e2\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\60765e497738b05773a285ac784eea4a\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\60765e497738b05773a285ac784eea4a\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\b706e9705966f9637699d6002c5f2d79\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\b706e9705966f9637699d6002c5f2d79\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\363db97abb4332fc93a163ff582cf337\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\363db97abb4332fc93a163ff582cf337\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\1c916160872209709351e900651600df\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\1c916160872209709351e900651600df\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\1dba10a7577fa8f3baef16d4849e230d\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\1dba10a7577fa8f3baef16d4849e230d\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\8d129809cd8c466c0da9d5b65b255a5e\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\8d129809cd8c466c0da9d5b65b255a5e\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\5fce665e74d156cb4614eabc8109e8d2\transformed\jetified-datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\5fce665e74d156cb4614eabc8109e8d2\transformed\jetified-datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\3e35dcaaccaf76afd6dde08b5fc6dc43\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\3e35dcaaccaf76afd6dde08b5fc6dc43\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\a81817003301d1a7d9398c1572b117f7\transformed\jetified-datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\a81817003301d1a7d9398c1572b117f7\transformed\jetified-datastore-release\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\cdc318cb2477879731a20fd9682464f0\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\cdc318cb2477879731a20fd9682464f0\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:4:5-43
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a3165185f2d525d0fdc5467b35c687be\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a3165185f2d525d0fdc5467b35c687be\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\1f8ae2ae03c8f3ae6e8dc60fe5ef01da\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\1f8ae2ae03c8f3ae6e8dc60fe5ef01da\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\4a1a08cc053a416e875e44f0df90f81c\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\4a1a08cc053a416e875e44f0df90f81c\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\6214f9b462fa5f1ec4b967dbc077ec23\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\6214f9b462fa5f1ec4b967dbc077ec23\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\067ce996d8d53be284974b2c2bb62db9\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\067ce996d8d53be284974b2c2bb62db9\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\3eb6b6d199d47eea7c054efb88366fea\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\3eb6b6d199d47eea7c054efb88366fea\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\41121f6ec7a0d1786ef266124ab1a051\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\41121f6ec7a0d1786ef266124ab1a051\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d4beccfb936038cb8ed9ff0bd118a86\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d4beccfb936038cb8ed9ff0bd118a86\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\6a9804ab3caa4b86866a72723fd7c017\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\6a9804ab3caa4b86866a72723fd7c017\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\a467c1fd402840055b9892cad66e8764\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\a467c1fd402840055b9892cad66e8764\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4b3df5ffd582fc5d3d1345b770167516\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4b3df5ffd582fc5d3d1345b770167516\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b34a0e6b1445bd902b4e8da9c77f9b3b\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b34a0e6b1445bd902b4e8da9c77f9b3b\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\42462ff615a470fce269a489eb930f99\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\42462ff615a470fce269a489eb930f99\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\2afa94d1bcf8f854b341558bc43bcbb1\transformed\localbroadcastmanager-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\2afa94d1bcf8f854b341558bc43bcbb1\transformed\localbroadcastmanager-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f2c03ffff95df5ec6d0d921ca84310cb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f2c03ffff95df5ec6d0d921ca84310cb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\d7cf2d8404d700a18769a5d26d9a8c78\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\d7cf2d8404d700a18769a5d26d9a8c78\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\57c7a04d7dd3753416a7c36462bbb837\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\57c7a04d7dd3753416a7c36462bbb837\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\747fe02755efcbd0fb0f05fdf22f79d2\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\747fe02755efcbd0fb0f05fdf22f79d2\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7f6b68a543c2723e1da7a8e2cbf66386\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7f6b68a543c2723e1da7a8e2cbf66386\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\761ecc1845ab25429b41b0bdd14c6124\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\761ecc1845ab25429b41b0bdd14c6124\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\54b1f48bf7938497eab3d09f3bb48e20\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\54b1f48bf7938497eab3d09f3bb48e20\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8123fac3812cf931f1b1f4781b85647f\transformed\jetified-firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8123fac3812cf931f1b1f4781b85647f\transformed\jetified-firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\176032b12d74ea1bfa0ece5bf0bd4f5c\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\176032b12d74ea1bfa0ece5bf0bd4f5c\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\32400d416bc1421387762890a1a0fc16\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\32400d416bc1421387762890a1a0fc16\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e0e01d3a9c3b9abcfbd97fb470620c45\transformed\jetified-firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e0e01d3a9c3b9abcfbd97fb470620c45\transformed\jetified-firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\2575da579be80ed57e233552a065f6f6\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\2575da579be80ed57e233552a065f6f6\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c34b64293857da693d70f4ce53cbf342\transformed\jetified-transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c34b64293857da693d70f4ce53cbf342\transformed\jetified-transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f71465aebc0275adf0f55c66f598aacb\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f71465aebc0275adf0f55c66f598aacb\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\945b6820a33dfafd1f38d971d570ec9d\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\945b6820a33dfafd1f38d971d570ec9d\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\60d8239bd87c5d376d9dd5e247866b4b\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\60d8239bd87c5d376d9dd5e247866b4b\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\166abcccb7d371e29d27ff7a9d39d011\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\166abcccb7d371e29d27ff7a9d39d011\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.multidex:multidex:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\9ea93decceb8e34f901d2477c6cfb905\transformed\multidex-2.0.1\AndroidManifest.xml:20:5-43
MERGED from [androidx.multidex:multidex:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\9ea93decceb8e34f901d2477c6cfb905\transformed\multidex-2.0.1\AndroidManifest.xml:20:5-43
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\android\app\src\debug\AndroidManifest.xml
intent#action:name:android.intent.action.GET_CONTENT+data:mimeType:*/*
ADDED from [:file_picker] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-12:18
action#android.intent.action.GET_CONTENT
ADDED from [:file_picker] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-72
	android:name
		ADDED from [:file_picker] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:9:21-69
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:9:5-79
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac0dcf16ce6a281444fe134795f48771\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac0dcf16ce6a281444fe134795f48771\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\16b44a5a27378eb99bc553b95273bac9\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\16b44a5a27378eb99bc553b95273bac9\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\b34daed25f56462bad67b11d215f3292\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\b34daed25f56462bad67b11d215f3292\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\32400d416bc1421387762890a1a0fc16\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\32400d416bc1421387762890a1a0fc16\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\2575da579be80ed57e233552a065f6f6\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\2575da579be80ed57e233552a065f6f6\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:22:5-79
	android:name
		ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:9:22-76
service#io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService
ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:14:9-17:72
	android:exported
		ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:16:13-37
	android:permission
		ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:17:13-69
	android:name
		ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:15:13-107
service#io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService
ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:18:9-24:19
	android:exported
		ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:20:13-37
	android:name
		ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:19:13-97
intent-filter#action:name:com.google.firebase.MESSAGING_EVENT
ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:21:13-23:29
action#com.google.firebase.MESSAGING_EVENT
ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:22:17-78
	android:name
		ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:22:25-75
receiver#io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver
ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:26:9-33:20
	android:exported
		ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:28:13-36
	android:permission
		ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:29:13-73
	android:name
		ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:27:13-98
intent-filter#action:name:com.google.android.c2dm.intent.RECEIVE
ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:30:13-32:29
action#com.google.android.c2dm.intent.RECEIVE
ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:31:17-81
	android:name
		ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:31:25-78
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:35:9-39:19
MERGED from [:firebase_core] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_core] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-12:19
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac0dcf16ce6a281444fe134795f48771\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac0dcf16ce6a281444fe134795f48771\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\16b44a5a27378eb99bc553b95273bac9\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\16b44a5a27378eb99bc553b95273bac9\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ae41e01eb7ae5d989b223de0fe61f461\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ae41e01eb7ae5d989b223de0fe61f461\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9e6da811dcfb7c615f8acdd62216d271\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9e6da811dcfb7c615f8acdd62216d271\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\176032b12d74ea1bfa0ece5bf0bd4f5c\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:22:9-28:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\176032b12d74ea1bfa0ece5bf0bd4f5c\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:22:9-28:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac0dcf16ce6a281444fe134795f48771\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:56:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9e6da811dcfb7c615f8acdd62216d271\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9e6da811dcfb7c615f8acdd62216d271\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:35:18-89
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar
ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:36:13-38:85
	android:value
		ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:38:17-82
	android:name
		ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:37:17-128
provider#io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider
ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:41:9-45:38
	android:authorities
		ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:43:13-88
	android:exported
		ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:44:13-37
	android:initOrder
		ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:45:13-35
	android:name
		ADDED from [:firebase_messaging] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:42:13-102
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar
ADDED from [:firebase_core] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-11:85
	android:value
		ADDED from [:firebase_core] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:11:17-82
	android:name
		ADDED from [:firebase_core] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:10:17-124
provider#com.crazecoder.openfile.FileProvider
ADDED from [:open_file_android] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:9:9-19:20
	android:requestLegacyExternalStorage
		ADDED from [:open_file_android] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:14:13-56
	android:grantUriPermissions
		ADDED from [:open_file_android] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-47
	android:authorities
		ADDED from [:open_file_android] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-88
	android:exported
		ADDED from [:open_file_android] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:12:13-37
	tools:replace
		ADDED from [:open_file_android] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:15:13-48
	android:name
		ADDED from [:open_file_android] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:10:13-64
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [:open_file_android] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:16:13-18:53
	android:resource
		ADDED from [:open_file_android] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:18:17-50
	android:name
		ADDED from [:open_file_android] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:17:17-67
activity#io.flutter.plugins.urllauncher.WebViewActivity
ADDED from [:url_launcher_android] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-11:74
	android:exported
		ADDED from [:url_launcher_android] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:10:13-37
	android:theme
		ADDED from [:url_launcher_android] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-71
	android:name
		ADDED from [:url_launcher_android] C:\Users\<USER>\Desktop\pfe\mobile\ecocode-mobile-version-ecocode-final\example\myApp\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-74
intent#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+data:scheme:https
ADDED from [net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-3\e29af6bba5b83fbf9d8cabbd1c723f32\transformed\jetified-appauth-0.11.1\AndroidManifest.xml:25:9-31:18
activity#net.openid.appauth.AuthorizationManagementActivity
ADDED from [net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-3\e29af6bba5b83fbf9d8cabbd1c723f32\transformed\jetified-appauth-0.11.1\AndroidManifest.xml:35:9-40:77
	android:launchMode
		ADDED from [net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-3\e29af6bba5b83fbf9d8cabbd1c723f32\transformed\jetified-appauth-0.11.1\AndroidManifest.xml:39:13-44
	android:exported
		ADDED from [net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-3\e29af6bba5b83fbf9d8cabbd1c723f32\transformed\jetified-appauth-0.11.1\AndroidManifest.xml:38:13-37
	android:configChanges
		ADDED from [net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-3\e29af6bba5b83fbf9d8cabbd1c723f32\transformed\jetified-appauth-0.11.1\AndroidManifest.xml:37:13-115
	android:theme
		ADDED from [net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-3\e29af6bba5b83fbf9d8cabbd1c723f32\transformed\jetified-appauth-0.11.1\AndroidManifest.xml:40:13-74
	android:name
		ADDED from [net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-3\e29af6bba5b83fbf9d8cabbd1c723f32\transformed\jetified-appauth-0.11.1\AndroidManifest.xml:36:13-78
activity#net.openid.appauth.RedirectUriReceiverActivity
ADDED from [net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-3\e29af6bba5b83fbf9d8cabbd1c723f32\transformed\jetified-appauth-0.11.1\AndroidManifest.xml:41:9-52:20
	android:exported
		ADDED from [net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-3\e29af6bba5b83fbf9d8cabbd1c723f32\transformed\jetified-appauth-0.11.1\AndroidManifest.xml:43:13-36
	android:name
		ADDED from [net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-3\e29af6bba5b83fbf9d8cabbd1c723f32\transformed\jetified-appauth-0.11.1\AndroidManifest.xml:42:13-74
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:scheme:${appAuthRedirectScheme}
ADDED from [net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-3\e29af6bba5b83fbf9d8cabbd1c723f32\transformed\jetified-appauth-0.11.1\AndroidManifest.xml:44:13-51:29
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:scheme:com.example.NovaSchool
ADDED from [net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-3\e29af6bba5b83fbf9d8cabbd1c723f32\transformed\jetified-appauth-0.11.1\AndroidManifest.xml:44:13-51:29
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\dad65cb79b3eafd50888ad50598d546f\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\dad65cb79b3eafd50888ad50598d546f\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\dad65cb79b3eafd50888ad50598d546f\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\dad65cb79b3eafd50888ad50598d546f\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\dad65cb79b3eafd50888ad50598d546f\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\dad65cb79b3eafd50888ad50598d546f\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\18bb7e320acb9ef5a96e99eb70fb4294\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\363db97abb4332fc93a163ff582cf337\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\363db97abb4332fc93a163ff582cf337\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f2c03ffff95df5ec6d0d921ca84310cb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f2c03ffff95df5ec6d0d921ca84310cb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\d7cf2d8404d700a18769a5d26d9a8c78\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\d7cf2d8404d700a18769a5d26d9a8c78\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\18bb7e320acb9ef5a96e99eb70fb4294\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\18bb7e320acb9ef5a96e99eb70fb4294\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\18bb7e320acb9ef5a96e99eb70fb4294\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\18bb7e320acb9ef5a96e99eb70fb4294\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\18bb7e320acb9ef5a96e99eb70fb4294\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\18bb7e320acb9ef5a96e99eb70fb4294\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\18bb7e320acb9ef5a96e99eb70fb4294\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
receiver#com.google.firebase.iid.FirebaseInstanceIdReceiver
ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac0dcf16ce6a281444fe134795f48771\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:29:9-40:20
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac0dcf16ce6a281444fe134795f48771\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:31:13-36
	android:permission
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac0dcf16ce6a281444fe134795f48771\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:32:13-73
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac0dcf16ce6a281444fe134795f48771\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:30:13-78
meta-data#com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED
ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac0dcf16ce6a281444fe134795f48771\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:37:13-39:40
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac0dcf16ce6a281444fe134795f48771\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:39:17-37
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac0dcf16ce6a281444fe134795f48771\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:38:17-92
service#com.google.firebase.messaging.FirebaseMessagingService
ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac0dcf16ce6a281444fe134795f48771\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:46:9-53:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac0dcf16ce6a281444fe134795f48771\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:49:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac0dcf16ce6a281444fe134795f48771\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:48:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac0dcf16ce6a281444fe134795f48771\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:47:13-82
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac0dcf16ce6a281444fe134795f48771\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:57:13-59:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac0dcf16ce6a281444fe134795f48771\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:59:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac0dcf16ce6a281444fe134795f48771\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:58:17-122
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac0dcf16ce6a281444fe134795f48771\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:60:13-62:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac0dcf16ce6a281444fe134795f48771\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:62:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac0dcf16ce6a281444fe134795f48771\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:61:17-119
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\16b44a5a27378eb99bc553b95273bac9\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\16b44a5a27378eb99bc553b95273bac9\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\16b44a5a27378eb99bc553b95273bac9\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\16b44a5a27378eb99bc553b95273bac9\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\16b44a5a27378eb99bc553b95273bac9\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\16b44a5a27378eb99bc553b95273bac9\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ae41e01eb7ae5d989b223de0fe61f461\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ae41e01eb7ae5d989b223de0fe61f461\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ae41e01eb7ae5d989b223de0fe61f461\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9e6da811dcfb7c615f8acdd62216d271\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9e6da811dcfb7c615f8acdd62216d271\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9e6da811dcfb7c615f8acdd62216d271\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9e6da811dcfb7c615f8acdd62216d271\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9e6da811dcfb7c615f8acdd62216d271\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9e6da811dcfb7c615f8acdd62216d271\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9e6da811dcfb7c615f8acdd62216d271\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9e6da811dcfb7c615f8acdd62216d271\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9e6da811dcfb7c615f8acdd62216d271\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8688e9838b51b6004ba54c59160ddf37\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8688e9838b51b6004ba54c59160ddf37\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8688e9838b51b6004ba54c59160ddf37\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8688e9838b51b6004ba54c59160ddf37\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:19-85
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\363db97abb4332fc93a163ff582cf337\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\363db97abb4332fc93a163ff582cf337\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\363db97abb4332fc93a163ff582cf337\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\1f8ae2ae03c8f3ae6e8dc60fe5ef01da\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\1f8ae2ae03c8f3ae6e8dc60fe5ef01da\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\1f8ae2ae03c8f3ae6e8dc60fe5ef01da\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\42462ff615a470fce269a489eb930f99\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\42462ff615a470fce269a489eb930f99\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\42462ff615a470fce269a489eb930f99\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.example.LoujaynSchool.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\42462ff615a470fce269a489eb930f99\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\42462ff615a470fce269a489eb930f99\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\42462ff615a470fce269a489eb930f99\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\42462ff615a470fce269a489eb930f99\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\42462ff615a470fce269a489eb930f99\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.example.LoujaynSchool.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\42462ff615a470fce269a489eb930f99\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\42462ff615a470fce269a489eb930f99\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f2c03ffff95df5ec6d0d921ca84310cb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f2c03ffff95df5ec6d0d921ca84310cb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f2c03ffff95df5ec6d0d921ca84310cb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f2c03ffff95df5ec6d0d921ca84310cb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f2c03ffff95df5ec6d0d921ca84310cb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f2c03ffff95df5ec6d0d921ca84310cb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f2c03ffff95df5ec6d0d921ca84310cb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f2c03ffff95df5ec6d0d921ca84310cb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f2c03ffff95df5ec6d0d921ca84310cb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f2c03ffff95df5ec6d0d921ca84310cb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f2c03ffff95df5ec6d0d921ca84310cb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f2c03ffff95df5ec6d0d921ca84310cb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f2c03ffff95df5ec6d0d921ca84310cb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f2c03ffff95df5ec6d0d921ca84310cb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f2c03ffff95df5ec6d0d921ca84310cb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f2c03ffff95df5ec6d0d921ca84310cb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f2c03ffff95df5ec6d0d921ca84310cb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f2c03ffff95df5ec6d0d921ca84310cb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f2c03ffff95df5ec6d0d921ca84310cb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f2c03ffff95df5ec6d0d921ca84310cb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f2c03ffff95df5ec6d0d921ca84310cb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
meta-data#com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar
ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\176032b12d74ea1bfa0ece5bf0bd4f5c\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
	android:value
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\176032b12d74ea1bfa0ece5bf0bd4f5c\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
	android:name
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\176032b12d74ea1bfa0ece5bf0bd4f5c\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\32400d416bc1421387762890a1a0fc16\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\2575da579be80ed57e233552a065f6f6\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\2575da579be80ed57e233552a065f6f6\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\32400d416bc1421387762890a1a0fc16\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\32400d416bc1421387762890a1a0fc16\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\32400d416bc1421387762890a1a0fc16\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\32400d416bc1421387762890a1a0fc16\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\32400d416bc1421387762890a1a0fc16\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\2575da579be80ed57e233552a065f6f6\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\2575da579be80ed57e233552a065f6f6\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\2575da579be80ed57e233552a065f6f6\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\2575da579be80ed57e233552a065f6f6\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\2575da579be80ed57e233552a065f6f6\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\2575da579be80ed57e233552a065f6f6\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\2575da579be80ed57e233552a065f6f6\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
