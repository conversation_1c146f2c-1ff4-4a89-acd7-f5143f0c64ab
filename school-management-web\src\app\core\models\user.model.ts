export interface User {
  id: string;
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  roles: string[];
  profile: UserProfile;
}

export interface UserProfile {
  type: 'parent' | 'teacher' | 'admin';
  avatar?: string;
  phone?: string;
  address?: string;
}

export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}

export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  accessToken: string;
  refreshToken: string;
  user: User;
}

export interface DecodedToken {
  sub: string;
  name: string;
  family_name: string;
  email: string;
  resource_access: {
    [clientId: string]: {
      roles: string[];
    };
  };
  exp: number;
  iat: number;
}
