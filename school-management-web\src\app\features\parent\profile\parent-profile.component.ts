import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

interface ParentProfile {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address: string;
  emergencyContact: string;
  emergencyPhone: string;
  children: Child[];
}

interface Child {
  id: number;
  firstName: string;
  lastName: string;
  grade: string;
  class: string;
  dateOfBirth: Date;
}

@Component({
  selector: 'app-parent-profile',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <div class="profile-container">
      <!-- Header -->
      <div class="profile-header">
        <div class="header-content">
          <h1>👤 My Profile</h1>
          <p>Manage your profile and account settings</p>
        </div>
      </div>

      <!-- Profile Form -->
      <div class="profile-form">
        <div class="form-section">
          <h3>Personal Information</h3>
          <div class="form-grid">
            <div class="form-group">
              <label>First Name</label>
              <input type="text" [(ngModel)]="profile.firstName" class="form-control">
            </div>
            <div class="form-group">
              <label>Last Name</label>
              <input type="text" [(ngModel)]="profile.lastName" class="form-control">
            </div>
            <div class="form-group">
              <label>Email</label>
              <input type="email" [(ngModel)]="profile.email" class="form-control">
            </div>
            <div class="form-group">
              <label>Phone</label>
              <input type="tel" [(ngModel)]="profile.phone" class="form-control">
            </div>
            <div class="form-group full-width">
              <label>Address</label>
              <textarea [(ngModel)]="profile.address" class="form-control" rows="3"></textarea>
            </div>
          </div>
        </div>

        <div class="form-section">
          <h3>Emergency Contact</h3>
          <div class="form-grid">
            <div class="form-group">
              <label>Emergency Contact Name</label>
              <input type="text" [(ngModel)]="profile.emergencyContact" class="form-control">
            </div>
            <div class="form-group">
              <label>Emergency Phone</label>
              <input type="tel" [(ngModel)]="profile.emergencyPhone" class="form-control">
            </div>
          </div>
        </div>

        <div class="form-section">
          <h3>My Children</h3>
          <div class="children-list">
            <div *ngFor="let child of profile.children" class="child-card">
              <div class="child-info">
                <div class="child-name">{{child.firstName}} {{child.lastName}}</div>
                <div class="child-details">
                  <span>📚 Grade {{child.grade}}</span>
                  <span>🏫 Class {{child.class}}</span>
                  <span>🎂 {{child.dateOfBirth | date:'MMM dd, yyyy'}}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="form-actions">
          <button class="btn primary" (click)="saveProfile()">Save Changes</button>
          <button class="btn secondary" (click)="resetForm()">Reset</button>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .profile-container {
      min-height: 100vh;
      background: #f5f7fa;
      padding: 20px;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .profile-header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 30px;
      border-radius: 12px;
      margin-bottom: 30px;
    }

    .header-content h1 {
      margin: 0 0 8px 0;
      font-size: 28px;
      font-weight: 600;
    }

    .header-content p {
      margin: 0;
      opacity: 0.9;
      font-size: 16px;
    }

    .profile-form {
      background: white;
      padding: 30px;
      border-radius: 12px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    }

    .form-section {
      margin-bottom: 30px;
    }

    .form-section h3 {
      margin: 0 0 20px 0;
      color: #333;
      font-size: 20px;
      border-bottom: 2px solid #667eea;
      padding-bottom: 10px;
    }

    .form-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 20px;
    }

    .form-group {
      display: flex;
      flex-direction: column;
    }

    .form-group.full-width {
      grid-column: 1 / -1;
    }

    .form-group label {
      margin-bottom: 8px;
      font-weight: 500;
      color: #333;
    }

    .form-control {
      padding: 12px;
      border: 2px solid #e1e5e9;
      border-radius: 8px;
      font-size: 14px;
      transition: border-color 0.3s ease;
    }

    .form-control:focus {
      outline: none;
      border-color: #667eea;
    }
  `]
})
export class ParentProfileComponent implements OnInit {
  profile: ParentProfile = {
    id: 1,
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    address: '',
    emergencyContact: '',
    emergencyPhone: '',
    children: []
  };

  ngOnInit(): void {
    this.loadProfile();
  }

  loadProfile(): void {
    this.profile = {
      id: 1,
      firstName: 'John',
      lastName: 'Johnson',
      email: '<EMAIL>',
      phone: '+****************',
      address: '123 Main Street, Anytown, ST 12345',
      emergencyContact: 'Jane Johnson',
      emergencyPhone: '+****************',
      children: [
        {
          id: 1,
          firstName: 'Sarah',
          lastName: 'Johnson',
          grade: '10',
          class: '10A',
          dateOfBirth: new Date('2008-05-15')
        },
        {
          id: 2,
          firstName: 'Michael',
          lastName: 'Johnson',
          grade: '8',
          class: '8B',
          dateOfBirth: new Date('2010-09-22')
        }
      ]
    };
  }

  saveProfile(): void {
    console.log('Saving profile:', this.profile);
    // Implement save logic
    alert('Profile saved successfully!');
  }

  resetForm(): void {
    this.loadProfile();
  }
}
