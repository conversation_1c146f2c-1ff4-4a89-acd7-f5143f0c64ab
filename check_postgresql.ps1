# PostgreSQL Detection and Startup Script
Write-Host "🔍 Checking PostgreSQL Installation..." -ForegroundColor Yellow

# Check for PostgreSQL in common installation paths
$postgresqlPaths = @(
    "C:\Program Files\PostgreSQL",
    "C:\Program Files (x86)\PostgreSQL",
    "C:\PostgreSQL"
)

$found = $false
foreach ($path in $postgresqlPaths) {
    if (Test-Path $path) {
        Write-Host "✅ Found PostgreSQL at: $path" -ForegroundColor Green
        $found = $true
        
        # List versions
        $versions = Get-ChildItem $path -Directory
        foreach ($version in $versions) {
            Write-Host "   📁 Version: $($version.Name)" -ForegroundColor Cyan
            
            # Check for pg_ctl
            $pgCtlPath = Join-Path $version.FullName "bin\pg_ctl.exe"
            if (Test-Path $pgCtlPath) {
                Write-Host "   🔧 pg_ctl found at: $pgCtlPath" -ForegroundColor Green
            }
        }
    }
}

if (-not $found) {
    Write-Host "❌ PostgreSQL not found in common locations" -ForegroundColor Red
    Write-Host "📥 Please install PostgreSQL from: https://www.postgresql.org/download/windows/" -ForegroundColor Yellow
    exit
}

# Check PostgreSQL services
Write-Host "`n🔍 Checking PostgreSQL Services..." -ForegroundColor Yellow
$services = Get-Service | Where-Object {$_.Name -like "*postgres*"}

if ($services) {
    foreach ($service in $services) {
        Write-Host "   🔧 Service: $($service.Name) - Status: $($service.Status)" -ForegroundColor Cyan
        
        if ($service.Status -eq "Stopped") {
            Write-Host "   🚀 Attempting to start $($service.Name)..." -ForegroundColor Yellow
            try {
                Start-Service $service.Name
                Write-Host "   ✅ Service started successfully!" -ForegroundColor Green
            } catch {
                Write-Host "   ❌ Failed to start service: $($_.Exception.Message)" -ForegroundColor Red
            }
        } elseif ($service.Status -eq "Running") {
            Write-Host "   ✅ Service is already running!" -ForegroundColor Green
        }
    }
} else {
    Write-Host "❌ No PostgreSQL services found" -ForegroundColor Red
}

# Check if port 5432 is listening
Write-Host "`n🔍 Checking Port 5432..." -ForegroundColor Yellow
$port = netstat -an | Select-String ":5432"
if ($port) {
    Write-Host "✅ Port 5432 is listening:" -ForegroundColor Green
    $port | ForEach-Object { Write-Host "   $($_.Line)" -ForegroundColor Cyan }
} else {
    Write-Host "❌ Port 5432 is not listening" -ForegroundColor Red
}

# Test connection
Write-Host "`n🔍 Testing PostgreSQL Connection..." -ForegroundColor Yellow
try {
    # Try to connect using psql if available
    $psqlPath = Get-Command psql -ErrorAction SilentlyContinue
    if ($psqlPath) {
        Write-Host "✅ psql command found at: $($psqlPath.Source)" -ForegroundColor Green
        Write-Host "💡 You can test connection with: psql -h localhost -U postgres" -ForegroundColor Cyan
    } else {
        Write-Host "❌ psql command not found in PATH" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Error checking psql: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n📋 Summary:" -ForegroundColor Yellow
Write-Host "1. If PostgreSQL is not installed, download from: https://www.postgresql.org/download/windows/" -ForegroundColor White
Write-Host "2. If installed but not running, check Windows Services" -ForegroundColor White
Write-Host "3. Default connection: localhost:5432, user: postgres" -ForegroundColor White
Write-Host "4. Make sure to remember the password you set during installation" -ForegroundColor White
