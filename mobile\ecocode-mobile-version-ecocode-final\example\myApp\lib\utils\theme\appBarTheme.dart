import 'package:flutter/material.dart';
import '../constants/colors.dart';

class AppBarTheme {
  AppBarTheme._();

  static const lightAppBarTheme = AppBarTheme(
    elevation: 0,
    centerTitle: true,
    scrolledUnderElevation: 0,
    backgroundColor: AppColors.surface,
    surfaceTintColor: Colors.transparent,
    shadowColor: AppColors.shadow,
    foregroundColor: AppColors.onSurface,
    iconTheme: IconThemeData(
      color: AppColors.onSurface,
      size: 24,
    ),
    actionsIconTheme: IconThemeData(
      color: AppColors.onSurface,
      size: 24,
    ),
    titleTextStyle: TextStyle(
      fontSize: 20.0,
      fontWeight: FontWeight.w600,
      color: AppColors.onSurface,
      letterSpacing: 0.15,
    ),
    toolbarTextStyle: TextStyle(
      color: AppColors.onSurface,
    ),
  );

  static const darkAppBarTheme = AppBarTheme(
    elevation: 0,
    centerTitle: true,
    scrolledUnderElevation: 0,
    backgroundColor: Color(0xFF10131C),
    surfaceTintColor: Colors.transparent,
    shadowColor: Color(0xFF000000),
    foregroundColor: Color(0xFFE6E1E5),
    iconTheme: IconThemeData(
      color: Color(0xFFE6E1E5),
      size: 24,
    ),
    actionsIconTheme: IconThemeData(
      color: Color(0xFFE6E1E5),
      size: 24,
    ),
    titleTextStyle: TextStyle(
      fontSize: 20.0,
      fontWeight: FontWeight.w600,
      color: Color(0xFFE6E1E5),
      letterSpacing: 0.15,
    ),
    toolbarTextStyle: TextStyle(
      color: Color(0xFFE6E1E5),
    ),
  );
}