import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

interface AttendanceRecord {
  id: number;
  childName: string;
  date: Date;
  status: 'present' | 'absent' | 'late' | 'excused';
  subject: string;
  teacher: string;
  notes?: string;
  timeIn?: string;
  timeOut?: string;
}

interface AttendanceSummary {
  childName: string;
  totalDays: number;
  presentDays: number;
  absentDays: number;
  lateDays: number;
  excusedDays: number;
  attendanceRate: number;
}

@Component({
  selector: 'app-parent-attendance',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <div class="attendance-container">
      <!-- Header -->
      <div class="attendance-header">
        <div class="header-content">
          <h1>📋 Attendance Tracking</h1>
          <p>Monitor your children's attendance and punctuality</p>
        </div>
        <div class="header-actions">
          <select [(ngModel)]="selectedChild" (change)="filterRecords()" class="child-filter">
            <option value="">All Children</option>
            <option value="<PERSON>"><PERSON></option>
            <option value="<PERSON>"><PERSON></option>
          </select>
          <input type="month" [(ngModel)]="selectedMonth" (change)="filterRecords()" class="month-filter">
          <button class="export-btn" (click)="exportAttendance()">
            <span class="btn-icon">📥</span>
            Export Report
          </button>
        </div>
      </div>

      <!-- Summary Cards -->
      <div class="summary-section">
        <div *ngFor="let summary of attendanceSummaries" class="summary-card">
          <div class="summary-header">
            <h3>👤 {{summary.childName}}</h3>
            <div class="attendance-rate" [class]="getRateClass(summary.attendanceRate)">
              {{summary.attendanceRate.toFixed(1)}}%
            </div>
          </div>
          <div class="summary-stats">
            <div class="stat-item">
              <span class="stat-label">Present</span>
              <span class="stat-value present">{{summary.presentDays}}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Absent</span>
              <span class="stat-value absent">{{summary.absentDays}}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Late</span>
              <span class="stat-value late">{{summary.lateDays}}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Excused</span>
              <span class="stat-value excused">{{summary.excusedDays}}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Attendance Calendar View -->
      <div class="calendar-section">
        <h2>📅 Monthly Calendar View</h2>
        <div class="calendar-container">
          <div class="calendar-header">
            <button class="nav-btn" (click)="previousMonth()">‹</button>
            <h3>{{getCurrentMonthYear()}}</h3>
            <button class="nav-btn" (click)="nextMonth()">›</button>
          </div>
          <div class="calendar-grid">
            <div class="day-header" *ngFor="let day of weekDays">{{day}}</div>
            <div *ngFor="let day of calendarDays"
                 class="calendar-day"
                 [class]="getCalendarDayClass(day)">
              <span class="day-number">{{day.date}}</span>
              <div *ngIf="day.attendance" class="attendance-indicator" [class]="day.attendance.status">
                {{getStatusIcon(day.attendance.status)}}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Detailed Records Table -->
      <div class="records-section">
        <h2>📊 Detailed Attendance Records</h2>
        <div class="table-container">
          <table class="attendance-table">
            <thead>
              <tr>
                <th>Date</th>
                <th>Child</th>
                <th>Subject</th>
                <th>Status</th>
                <th>Time In</th>
                <th>Time Out</th>
                <th>Teacher</th>
                <th>Notes</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let record of filteredRecords" class="record-row">
                <td class="date-cell">{{record.date | date:'MMM dd, yyyy'}}</td>
                <td class="child-cell">{{record.childName}}</td>
                <td class="subject-cell">{{record.subject}}</td>
                <td class="status-cell">
                  <span class="status-badge" [class]="record.status">
                    {{getStatusIcon(record.status)}} {{record.status | titlecase}}
                  </span>
                </td>
                <td class="time-cell">{{record.timeIn || '-'}}</td>
                <td class="time-cell">{{record.timeOut || '-'}}</td>
                <td class="teacher-cell">{{record.teacher}}</td>
                <td class="notes-cell">{{record.notes || '-'}}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- Empty State -->
      <div *ngIf="filteredRecords.length === 0" class="empty-state">
        <div class="empty-icon">📋</div>
        <h3>No attendance records found</h3>
        <p>Try adjusting your filters or check back later for new records.</p>
      </div>
    </div>
  `,
  styles: [`
    .attendance-container {
      min-height: 100vh;
      background: #f5f7fa;
      padding: 20px;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .attendance-header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 30px;
      border-radius: 12px;
      margin-bottom: 30px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;
      gap: 20px;
    }

    .header-content h1 {
      margin: 0 0 8px 0;
      font-size: 28px;
      font-weight: 600;
    }

    .header-content p {
      margin: 0;
      opacity: 0.9;
      font-size: 16px;
    }

    .header-actions {
      display: flex;
      gap: 16px;
      align-items: center;
      flex-wrap: wrap;
    }

    .child-filter, .month-filter {
      padding: 12px 16px;
      border: none;
      border-radius: 8px;
      background: rgba(255, 255, 255, 0.2);
      color: white;
      cursor: pointer;
    }

    .export-btn {
      background: rgba(255, 255, 255, 0.2);
      border: none;
      color: white;
      padding: 12px 16px;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .export-btn:hover {
      background: rgba(255, 255, 255, 0.3);
    }

    .summary-section {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
      gap: 24px;
      margin-bottom: 30px;
    }

    .summary-card {
      background: white;
      border-radius: 12px;
      padding: 24px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    }

    .summary-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
    }

    .summary-header h3 {
      margin: 0;
      color: #333;
      font-size: 18px;
      font-weight: 600;
    }

    .attendance-rate {
      padding: 8px 16px;
      border-radius: 20px;
      font-weight: 600;
      font-size: 16px;
    }

    .attendance-rate.excellent {
      background: #d4edda;
      color: #155724;
    }

    .attendance-rate.good {
      background: #cce5ff;
      color: #004085;
    }

    .attendance-rate.warning {
      background: #fff3cd;
      color: #856404;
    }

    .attendance-rate.poor {
      background: #f8d7da;
      color: #721c24;
    }

    .summary-stats {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 16px;
    }

    .stat-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px;
      background: #f8f9fa;
      border-radius: 8px;
    }

    .stat-label {
      font-size: 14px;
      color: #666;
    }

    .stat-value {
      font-weight: 600;
      font-size: 16px;
    }

    .stat-value.present {
      color: #28a745;
    }

    .stat-value.absent {
      color: #dc3545;
    }

    .stat-value.late {
      color: #ffc107;
    }

    .stat-value.excused {
      color: #6c757d;
    }

    .calendar-section {
      background: white;
      border-radius: 12px;
      padding: 24px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
      margin-bottom: 30px;
    }

    .calendar-section h2 {
      margin: 0 0 20px 0;
      color: #333;
      font-size: 20px;
      font-weight: 600;
    }

    .calendar-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
    }

    .nav-btn {
      background: #667eea;
      color: white;
      border: none;
      padding: 8px 12px;
      border-radius: 6px;
      cursor: pointer;
      font-size: 18px;
    }

    .nav-btn:hover {
      background: #5a6fd8;
    }

    .calendar-grid {
      display: grid;
      grid-template-columns: repeat(7, 1fr);
      gap: 1px;
      background: #dee2e6;
      border-radius: 8px;
      overflow: hidden;
    }

    .day-header {
      background: #f8f9fa;
      padding: 12px;
      text-align: center;
      font-weight: 600;
      color: #333;
    }

    .calendar-day {
      background: white;
      padding: 8px;
      min-height: 60px;
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .calendar-day.other-month {
      background: #f8f9fa;
      color: #6c757d;
    }

    .day-number {
      font-size: 14px;
      font-weight: 500;
    }

    .attendance-indicator {
      margin-top: 4px;
      font-size: 16px;
    }

    .records-section {
      background: white;
      border-radius: 12px;
      padding: 24px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
      margin-bottom: 30px;
    }

    .records-section h2 {
      margin: 0 0 20px 0;
      color: #333;
      font-size: 20px;
      font-weight: 600;
    }

    .table-container {
      overflow-x: auto;
    }

    .attendance-table {
      width: 100%;
      border-collapse: collapse;
      font-size: 14px;
    }

    .attendance-table th {
      background: #f8f9fa;
      padding: 12px;
      text-align: left;
      font-weight: 600;
      color: #333;
      border-bottom: 2px solid #dee2e6;
    }

    .attendance-table td {
      padding: 12px;
      border-bottom: 1px solid #dee2e6;
      vertical-align: middle;
    }

    .record-row:hover {
      background: #f8f9fa;
    }

    .status-badge {
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 500;
      display: inline-flex;
      align-items: center;
      gap: 4px;
    }

    .status-badge.present {
      background: #d4edda;
      color: #155724;
    }

    .status-badge.absent {
      background: #f8d7da;
      color: #721c24;
    }

    .status-badge.late {
      background: #fff3cd;
      color: #856404;
    }

    .status-badge.excused {
      background: #e2e3e5;
      color: #383d41;
    }

    .empty-state {
      text-align: center;
      padding: 60px 20px;
      color: #666;
    }

    .empty-icon {
      font-size: 64px;
      margin-bottom: 16px;
    }

    .empty-state h3 {
      margin: 0 0 8px 0;
      color: #333;
    }

    .empty-state p {
      margin: 0;
    }

    @media (max-width: 768px) {
      .attendance-header {
        flex-direction: column;
        align-items: stretch;
      }

      .header-actions {
        justify-content: stretch;
      }

      .child-filter, .month-filter {
        min-width: 100%;
      }

      .summary-section {
        grid-template-columns: 1fr;
      }

      .calendar-grid {
        font-size: 12px;
      }

      .calendar-day {
        min-height: 40px;
        padding: 4px;
      }

      .attendance-table {
        font-size: 12px;
      }

      .attendance-table th,
      .attendance-table td {
        padding: 8px 4px;
      }
    }
  `]
})
export class ParentAttendanceComponent implements OnInit {
  selectedChild = '';
  selectedMonth = '';
  currentDate = new Date();

  attendanceRecords: AttendanceRecord[] = [];
  filteredRecords: AttendanceRecord[] = [];
  attendanceSummaries: AttendanceSummary[] = [];

  weekDays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
  calendarDays: any[] = [];

  ngOnInit(): void {
    this.selectedMonth = this.formatMonth(this.currentDate);
    this.loadAttendanceRecords();
    this.calculateSummaries();
    this.generateCalendar();
    this.filteredRecords = this.attendanceRecords;
  }

  loadAttendanceRecords(): void {
    this.attendanceRecords = [
      {
        id: 1,
        childName: 'Sarah Johnson',
        date: new Date('2024-01-15'),
        status: 'present',
        subject: 'Mathematics',
        teacher: 'Dr. Sarah Wilson',
        timeIn: '8:00 AM',
        timeOut: '3:00 PM'
      },
      {
        id: 2,
        childName: 'Sarah Johnson',
        date: new Date('2024-01-16'),
        status: 'late',
        subject: 'English',
        teacher: 'Ms. Emily Davis',
        timeIn: '8:15 AM',
        timeOut: '3:00 PM',
        notes: 'Traffic delay'
      },
      {
        id: 3,
        childName: 'Sarah Johnson',
        date: new Date('2024-01-17'),
        status: 'absent',
        subject: 'Science',
        teacher: 'Mr. James Rodriguez',
        notes: 'Sick leave'
      },
      {
        id: 4,
        childName: 'Michael Johnson',
        date: new Date('2024-01-15'),
        status: 'present',
        subject: 'Art',
        teacher: 'Ms. Lisa Chen',
        timeIn: '8:00 AM',
        timeOut: '3:00 PM'
      },
      {
        id: 5,
        childName: 'Michael Johnson',
        date: new Date('2024-01-16'),
        status: 'present',
        subject: 'PE',
        teacher: 'Coach Mike Thompson',
        timeIn: '8:00 AM',
        timeOut: '3:00 PM'
      },
      {
        id: 6,
        childName: 'Michael Johnson',
        date: new Date('2024-01-17'),
        status: 'excused',
        subject: 'Music',
        teacher: 'Mr. David Park',
        notes: 'Doctor appointment'
      }
    ];
  }

  calculateSummaries(): void {
    const childrenMap = new Map<string, AttendanceRecord[]>();

    this.attendanceRecords.forEach(record => {
      if (!childrenMap.has(record.childName)) {
        childrenMap.set(record.childName, []);
      }
      childrenMap.get(record.childName)!.push(record);
    });

    this.attendanceSummaries = Array.from(childrenMap.entries()).map(([childName, records]) => {
      const totalDays = records.length;
      const presentDays = records.filter(r => r.status === 'present').length;
      const absentDays = records.filter(r => r.status === 'absent').length;
      const lateDays = records.filter(r => r.status === 'late').length;
      const excusedDays = records.filter(r => r.status === 'excused').length;
      const attendanceRate = totalDays > 0 ? ((presentDays + lateDays) / totalDays) * 100 : 0;

      return {
        childName,
        totalDays,
        presentDays,
        absentDays,
        lateDays,
        excusedDays,
        attendanceRate
      };
    });
  }

  filterRecords(): void {
    this.filteredRecords = this.attendanceRecords.filter(record => {
      const matchesChild = !this.selectedChild || record.childName === this.selectedChild;

      let matchesMonth = true;
      if (this.selectedMonth) {
        const recordMonth = this.formatMonth(record.date);
        matchesMonth = recordMonth === this.selectedMonth;
      }

      return matchesChild && matchesMonth;
    });
  }

  formatMonth(date: Date): string {
    return date.toISOString().substring(0, 7); // YYYY-MM format
  }

  getRateClass(rate: number): string {
    if (rate >= 95) return 'excellent';
    if (rate >= 85) return 'good';
    if (rate >= 75) return 'warning';
    return 'poor';
  }

  getStatusIcon(status: string): string {
    switch (status) {
      case 'present': return '✅';
      case 'absent': return '❌';
      case 'late': return '⏰';
      case 'excused': return '📋';
      default: return '❓';
    }
  }

  getCurrentMonthYear(): string {
    return this.currentDate.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
  }

  previousMonth(): void {
    this.currentDate.setMonth(this.currentDate.getMonth() - 1);
    this.generateCalendar();
  }

  nextMonth(): void {
    this.currentDate.setMonth(this.currentDate.getMonth() + 1);
    this.generateCalendar();
  }

  generateCalendar(): void {
    const year = this.currentDate.getFullYear();
    const month = this.currentDate.getMonth();

    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - firstDay.getDay());

    this.calendarDays = [];
    const currentDate = new Date(startDate);

    for (let i = 0; i < 42; i++) { // 6 weeks * 7 days
      const dayRecord = this.attendanceRecords.find(record =>
        record.date.toDateString() === currentDate.toDateString()
      );

      this.calendarDays.push({
        date: currentDate.getDate(),
        fullDate: new Date(currentDate),
        isCurrentMonth: currentDate.getMonth() === month,
        attendance: dayRecord
      });

      currentDate.setDate(currentDate.getDate() + 1);
    }
  }

  getCalendarDayClass(day: any): string {
    return day.isCurrentMonth ? '' : 'other-month';
  }

  exportAttendance(): void {
    const csvContent = this.generateAttendanceCSV();
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'attendance-report.csv';
    link.click();
    window.URL.revokeObjectURL(url);
  }

  generateAttendanceCSV(): string {
    const headers = ['Date', 'Child', 'Subject', 'Status', 'Time In', 'Time Out', 'Teacher', 'Notes'];
    const rows = this.filteredRecords.map(record => [
      record.date.toLocaleDateString(),
      record.childName,
      record.subject,
      record.status,
      record.timeIn || '',
      record.timeOut || '',
      record.teacher,
      record.notes || ''
    ]);

    return [headers, ...rows].map(row => row.join(',')).join('\n');
  }
}
