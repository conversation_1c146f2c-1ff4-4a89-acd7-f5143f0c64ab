import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

interface Message {
  id: number;
  sender: string;
  recipient: string;
  subject: string;
  content: string;
  date: Date;
  isRead: boolean;
  type: 'received' | 'sent';
  priority: 'low' | 'normal' | 'high';
  childName?: string;
}

@Component({
  selector: 'app-parent-messages',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <div class="messages-container">
      <!-- Header -->
      <div class="messages-header">
        <div class="header-content">
          <h1>💬 Messages</h1>
          <p>Communicate with teachers and school staff</p>
        </div>
        <div class="header-actions">
          <button class="compose-btn" (click)="showComposeModal = true">
            <span class="btn-icon">✏️</span>
            Compose Message
          </button>
        </div>
      </div>

      <!-- Message Filters -->
      <div class="filters-section">
        <div class="filter-tabs">
          <button class="filter-tab" [class.active]="activeFilter === 'all'" (click)="setFilter('all')">
            All Messages ({{getAllMessagesCount()}})
          </button>
          <button class="filter-tab" [class.active]="activeFilter === 'received'" (click)="setFilter('received')">
            Received ({{getReceivedCount()}})
          </button>
          <button class="filter-tab" [class.active]="activeFilter === 'sent'" (click)="setFilter('sent')">
            Sent ({{getSentCount()}})
          </button>
          <button class="filter-tab" [class.active]="activeFilter === 'unread'" (click)="setFilter('unread')">
            Unread ({{getUnreadCount()}})
          </button>
        </div>
        <div class="search-box">
          <input type="text" placeholder="Search messages..." [(ngModel)]="searchTerm" (input)="filterMessages()" class="search-input">
          <span class="search-icon">🔍</span>
        </div>
      </div>

      <!-- Messages List -->
      <div class="messages-list">
        <div *ngFor="let message of filteredMessages" class="message-item" [class]="getMessageClass(message)" (click)="selectMessage(message)">
          <div class="message-header">
            <div class="sender-info">
              <span class="sender-name">{{message.type === 'received' ? message.sender : 'To: ' + message.recipient}}</span>
              <span class="message-date">{{message.date | date:'MMM dd, yyyy HH:mm'}}</span>
            </div>
            <div class="message-indicators">
              <span *ngIf="message.priority === 'high'" class="priority-indicator high">🔴</span>
              <span *ngIf="!message.isRead && message.type === 'received'" class="unread-indicator">●</span>
            </div>
          </div>
          <div class="message-subject">{{message.subject}}</div>
          <div class="message-preview">{{getMessagePreview(message.content)}}</div>
          <div *ngIf="message.childName" class="child-tag">👤 {{message.childName}}</div>
        </div>
      </div>

      <!-- Empty State -->
      <div *ngIf="filteredMessages.length === 0" class="empty-state">
        <div class="empty-icon">💬</div>
        <h3>No messages found</h3>
        <p>Try adjusting your filters or compose a new message.</p>
      </div>
    </div>
  `,
  styles: [`
    .messages-container {
      min-height: 100vh;
      background: #f5f7fa;
      padding: 20px;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .messages-header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 30px;
      border-radius: 12px;
      margin-bottom: 30px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;
      gap: 20px;
    }

    .header-content h1 {
      margin: 0 0 8px 0;
      font-size: 28px;
      font-weight: 600;
    }

    .header-content p {
      margin: 0;
      opacity: 0.9;
      font-size: 16px;
    }

    .compose-btn {
      background: rgba(255, 255, 255, 0.2);
      border: none;
      color: white;
      padding: 12px 20px;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 500;
    }

    .compose-btn:hover {
      background: rgba(255, 255, 255, 0.3);
    }

    .filters-section {
      background: white;
      padding: 20px;
      border-radius: 12px;
      margin-bottom: 20px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;
      gap: 20px;
    }

    .filter-tabs {
      display: flex;
      gap: 8px;
    }

    .filter-tab {
      padding: 8px 16px;
      border: 2px solid #e1e5e9;
      background: white;
      border-radius: 20px;
      cursor: pointer;
      transition: all 0.3s ease;
      font-size: 14px;
      font-weight: 500;
    }

    .filter-tab.active {
      background: #667eea;
      color: white;
      border-color: #667eea;
    }

    .filter-tab:hover:not(.active) {
      border-color: #667eea;
      color: #667eea;
    }

    .search-box {
      position: relative;
    }

    .search-input {
      padding: 10px 40px 10px 16px;
      border: 2px solid #e1e5e9;
      border-radius: 8px;
      min-width: 250px;
    }

    .search-icon {
      position: absolute;
      right: 12px;
      top: 50%;
      transform: translateY(-50%);
      color: #666;
    }

    .messages-list {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    .message-item {
      background: white;
      padding: 20px;
      border-radius: 12px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
      cursor: pointer;
      transition: all 0.3s ease;
      border-left: 4px solid #e1e5e9;
    }

    .message-item:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .message-item.unread {
      border-left-color: #667eea;
      background: #f8f9ff;
    }

    .message-item.high-priority {
      border-left-color: #dc3545;
    }

    .message-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
    }

    .sender-name {
      font-weight: 600;
      color: #333;
      font-size: 16px;
    }

    .message-date {
      color: #666;
      font-size: 12px;
    }

    .message-indicators {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .unread-indicator {
      color: #667eea;
      font-size: 12px;
    }

    .priority-indicator.high {
      font-size: 12px;
    }

    .message-subject {
      font-weight: 500;
      color: #333;
      margin-bottom: 4px;
      font-size: 14px;
    }

    .message-preview {
      color: #666;
      font-size: 14px;
      line-height: 1.4;
      margin-bottom: 8px;
    }

    .child-tag {
      display: inline-block;
      background: #e3f2fd;
      color: #1976d2;
      padding: 2px 8px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 500;
    }

    .empty-state {
      text-align: center;
      padding: 60px 20px;
      color: #666;
    }

    .empty-icon {
      font-size: 64px;
      margin-bottom: 16px;
    }

    .empty-state h3 {
      margin: 0 0 8px 0;
      color: #333;
    }

    .empty-state p {
      margin: 0;
    }

    @media (max-width: 768px) {
      .messages-header {
        flex-direction: column;
        align-items: stretch;
      }

      .filters-section {
        flex-direction: column;
        align-items: stretch;
      }

      .filter-tabs {
        flex-wrap: wrap;
      }

      .search-input {
        min-width: 100%;
      }
    }
  `]
})
export class ParentMessagesComponent implements OnInit {
  messages: Message[] = [];
  filteredMessages: Message[] = [];
  selectedMessage: Message | null = null;
  showComposeModal = false;
  activeFilter = 'all';
  searchTerm = '';

  newMessage = {
    recipient: '',
    subject: '',
    content: '',
    priority: 'normal' as 'low' | 'normal' | 'high',
    childName: ''
  };

  ngOnInit(): void {
    this.loadMessages();
    this.filteredMessages = this.messages;
  }

  loadMessages(): void {
    this.messages = [
      {
        id: 1,
        sender: 'Dr. Sarah Wilson',
        recipient: 'Parent',
        subject: 'Sarah\'s Math Progress',
        content: 'I wanted to update you on Sarah\'s excellent progress in mathematics. She has shown remarkable improvement in algebra and is consistently scoring above 90% on her assignments.',
        date: new Date('2024-01-15T10:30:00'),
        isRead: false,
        type: 'received',
        priority: 'normal',
        childName: 'Sarah Johnson'
      },
      {
        id: 2,
        sender: 'Ms. Emily Davis',
        recipient: 'Parent',
        subject: 'Reading Assignment Reminder',
        content: 'This is a reminder that Sarah\'s book report on "Charlotte\'s Web" is due next Friday. Please ensure she has completed the reading and is prepared to present.',
        date: new Date('2024-01-14T14:15:00'),
        isRead: true,
        type: 'received',
        priority: 'normal',
        childName: 'Sarah Johnson'
      },
      {
        id: 3,
        sender: 'Principal Johnson',
        recipient: 'Parent',
        subject: 'School Event Invitation',
        content: 'You are cordially invited to our annual Science Fair on January 25th. Both Sarah and Michael have projects that will be displayed. We hope to see you there!',
        date: new Date('2024-01-13T09:00:00'),
        isRead: true,
        type: 'received',
        priority: 'high'
      },
      {
        id: 4,
        sender: 'Parent',
        recipient: 'Coach Mike Thompson',
        subject: 'Michael\'s PE Participation',
        content: 'Thank you for your patience with Michael during PE classes. We\'ve been working on his confidence in team sports at home.',
        date: new Date('2024-01-12T16:45:00'),
        isRead: true,
        type: 'sent',
        priority: 'normal',
        childName: 'Michael Johnson'
      },
      {
        id: 5,
        sender: 'Ms. Lisa Chen',
        recipient: 'Parent',
        subject: 'Art Supplies Needed',
        content: 'For our upcoming art project, Michael will need watercolor paints and brushes. Please send these items by next Tuesday.',
        date: new Date('2024-01-11T11:20:00'),
        isRead: false,
        type: 'received',
        priority: 'normal',
        childName: 'Michael Johnson'
      }
    ];
  }

  setFilter(filter: string): void {
    this.activeFilter = filter;
    this.filterMessages();
  }

  filterMessages(): void {
    let filtered = this.messages;

    // Apply filter
    switch (this.activeFilter) {
      case 'received':
        filtered = filtered.filter(m => m.type === 'received');
        break;
      case 'sent':
        filtered = filtered.filter(m => m.type === 'sent');
        break;
      case 'unread':
        filtered = filtered.filter(m => !m.isRead && m.type === 'received');
        break;
    }

    // Apply search
    if (this.searchTerm) {
      const term = this.searchTerm.toLowerCase();
      filtered = filtered.filter(m =>
        m.subject.toLowerCase().includes(term) ||
        m.content.toLowerCase().includes(term) ||
        m.sender.toLowerCase().includes(term) ||
        (m.childName && m.childName.toLowerCase().includes(term))
      );
    }

    this.filteredMessages = filtered;
  }

  getAllMessagesCount(): number {
    return this.messages.length;
  }

  getReceivedCount(): number {
    return this.messages.filter(m => m.type === 'received').length;
  }

  getSentCount(): number {
    return this.messages.filter(m => m.type === 'sent').length;
  }

  getUnreadCount(): number {
    return this.messages.filter(m => !m.isRead && m.type === 'received').length;
  }

  getMessageClass(message: Message): string {
    let classes = '';
    if (!message.isRead && message.type === 'received') {
      classes += 'unread ';
    }
    if (message.priority === 'high') {
      classes += 'high-priority ';
    }
    return classes.trim();
  }

  getMessagePreview(content: string): string {
    return content.length > 100 ? content.substring(0, 100) + '...' : content;
  }

  selectMessage(message: Message): void {
    this.selectedMessage = message;
    if (!message.isRead && message.type === 'received') {
      message.isRead = true;
      this.filterMessages(); // Refresh to update unread count
    }
  }

  closeMessage(): void {
    this.selectedMessage = null;
  }

  closeCompose(): void {
    this.showComposeModal = false;
    this.resetNewMessage();
  }

  resetNewMessage(): void {
    this.newMessage = {
      recipient: '',
      subject: '',
      content: '',
      priority: 'normal',
      childName: ''
    };
  }

  sendMessage(): void {
    if (!this.newMessage.recipient || !this.newMessage.subject || !this.newMessage.content) {
      alert('Please fill in all required fields.');
      return;
    }

    const message: Message = {
      id: this.messages.length + 1,
      sender: 'Parent',
      recipient: this.newMessage.recipient,
      subject: this.newMessage.subject,
      content: this.newMessage.content,
      date: new Date(),
      isRead: true,
      type: 'sent',
      priority: this.newMessage.priority,
      childName: this.newMessage.childName
    };

    this.messages.unshift(message);
    this.filterMessages();
    this.closeCompose();
    alert('Message sent successfully!');
  }

  replyToMessage(): void {
    if (this.selectedMessage) {
      this.newMessage = {
        recipient: this.selectedMessage.sender,
        subject: 'Re: ' + this.selectedMessage.subject,
        content: '',
        priority: 'normal',
        childName: this.selectedMessage.childName || ''
      };
      this.showComposeModal = true;
      this.closeMessage();
    }
  }

  forwardMessage(): void {
    if (this.selectedMessage) {
      this.newMessage = {
        recipient: '',
        subject: 'Fwd: ' + this.selectedMessage.subject,
        content: '\n\n--- Forwarded Message ---\n' + this.selectedMessage.content,
        priority: 'normal',
        childName: this.selectedMessage.childName || ''
      };
      this.showComposeModal = true;
      this.closeMessage();
    }
  }

  deleteMessage(): void {
    if (this.selectedMessage && confirm('Are you sure you want to delete this message?')) {
      const index = this.messages.findIndex(m => m.id === this.selectedMessage!.id);
      if (index > -1) {
        this.messages.splice(index, 1);
        this.filterMessages();
        this.closeMessage();
      }
    }
  }
}
