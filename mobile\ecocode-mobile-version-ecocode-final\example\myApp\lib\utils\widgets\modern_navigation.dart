import 'package:flutter/material.dart';
import '../constants/colors.dart';
import '../constants/size.dart';
import '../device/devices_utility.dart';

/// Modern app bar with gradient and custom styling
class ModernAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String? title;
  final Widget? titleWidget;
  final List<Widget>? actions;
  final Widget? leading;
  final bool automaticallyImplyLeading;
  final Color? backgroundColor;
  final List<Color>? gradientColors;
  final double? elevation;
  final bool centerTitle;
  final PreferredSizeWidget? bottom;

  const ModernAppBar({
    Key? key,
    this.title,
    this.titleWidget,
    this.actions,
    this.leading,
    this.automaticallyImplyLeading = true,
    this.backgroundColor,
    this.gradientColors,
    this.elevation,
    this.centerTitle = true,
    this.bottom,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: gradientColors != null
            ? LinearGradient(
                colors: gradientColors!,
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              )
            : null,
        color: backgroundColor ?? AppColors.surface,
        boxShadow: elevation != null && elevation! > 0
            ? [
                BoxShadow(
                  color: AppColors.shadow.withOpacity(0.1),
                  blurRadius: elevation! * 2,
                  offset: Offset(0, elevation!),
                ),
              ]
            : null,
      ),
      child: AppBar(
        title: titleWidget ?? (title != null ? Text(title!) : null),
        actions: actions,
        leading: leading,
        automaticallyImplyLeading: automaticallyImplyLeading,
        backgroundColor: Colors.transparent,
        elevation: 0,
        centerTitle: centerTitle,
        bottom: bottom,
        iconTheme: IconThemeData(
          color: gradientColors != null ? AppColors.onPrimary : AppColors.onSurface,
        ),
        titleTextStyle: Theme.of(context).textTheme.titleLarge?.copyWith(
          color: gradientColors != null ? AppColors.onPrimary : AppColors.onSurface,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(
        kToolbarHeight + (bottom?.preferredSize.height ?? 0),
      );
}

/// Modern bottom navigation bar with custom styling
class ModernBottomNavigationBar extends StatelessWidget {
  final int currentIndex;
  final ValueChanged<int> onTap;
  final List<ModernBottomNavigationItem> items;
  final Color? backgroundColor;
  final Color? selectedItemColor;
  final Color? unselectedItemColor;

  const ModernBottomNavigationBar({
    Key? key,
    required this.currentIndex,
    required this.onTap,
    required this.items,
    this.backgroundColor,
    this.selectedItemColor,
    this.unselectedItemColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: backgroundColor ?? AppColors.surface,
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow.withOpacity(0.1),
            blurRadius: 20,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: SafeArea(
        child: Padding(
          padding: EdgeInsets.symmetric(
            horizontal: AppSize.defaultSpace(context),
            vertical: AppSize.spaceBetweenItems(context),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: items.asMap().entries.map((entry) {
              final index = entry.key;
              final item = entry.value;
              final isSelected = index == currentIndex;

              return Expanded(
                child: GestureDetector(
                  onTap: () => onTap(index),
                  child: AnimatedContainer(
                    duration: const Duration(milliseconds: 200),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    decoration: BoxDecoration(
                      color: isSelected
                          ? (selectedItemColor ?? AppColors.primary).withOpacity(0.1)
                          : Colors.transparent,
                      borderRadius: BorderRadius.circular(AppSize.radiusLg),
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        AnimatedContainer(
                          duration: const Duration(milliseconds: 200),
                          child: Icon(
                            isSelected ? item.selectedIcon ?? item.icon : item.icon,
                            color: isSelected
                                ? selectedItemColor ?? AppColors.primary
                                : unselectedItemColor ?? AppColors.onSurfaceVariant,
                            size: 24,
                          ),
                        ),
                        const SizedBox(height: 4),
                        AnimatedDefaultTextStyle(
                          duration: const Duration(milliseconds: 200),
                          style: Theme.of(context).textTheme.bodySmall!.copyWith(
                            color: isSelected
                                ? selectedItemColor ?? AppColors.primary
                                : unselectedItemColor ?? AppColors.onSurfaceVariant,
                            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                          ),
                          child: Text(
                            item.label,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
        ),
      ),
    );
  }
}

/// Modern navigation rail for tablets and desktop
class ModernNavigationRail extends StatelessWidget {
  final int selectedIndex;
  final ValueChanged<int> onDestinationSelected;
  final List<ModernNavigationRailDestination> destinations;
  final Widget? leading;
  final Widget? trailing;
  final bool extended;
  final Color? backgroundColor;
  final Color? selectedIconColor;
  final Color? unselectedIconColor;

  const ModernNavigationRail({
    Key? key,
    required this.selectedIndex,
    required this.onDestinationSelected,
    required this.destinations,
    this.leading,
    this.trailing,
    this.extended = false,
    this.backgroundColor,
    this.selectedIconColor,
    this.unselectedIconColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: extended ? 256 : 80,
      decoration: BoxDecoration(
        color: backgroundColor ?? AppColors.surface,
        border: Border(
          right: BorderSide(
            color: AppColors.outline.withOpacity(0.2),
            width: 1,
          ),
        ),
      ),
      child: Column(
        children: [
          if (leading != null) ...[
            Padding(
              padding: const EdgeInsets.all(16),
              child: leading!,
            ),
          ],
          Expanded(
            child: ListView.builder(
              itemCount: destinations.length,
              itemBuilder: (context, index) {
                final destination = destinations[index];
                final isSelected = index == selectedIndex;

                return Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 4,
                  ),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      borderRadius: BorderRadius.circular(AppSize.radiusMd),
                      onTap: () => onDestinationSelected(index),
                      child: AnimatedContainer(
                        duration: const Duration(milliseconds: 200),
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 12,
                        ),
                        decoration: BoxDecoration(
                          color: isSelected
                              ? (selectedIconColor ?? AppColors.primary).withOpacity(0.1)
                              : Colors.transparent,
                          borderRadius: BorderRadius.circular(AppSize.radiusMd),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              isSelected 
                                  ? destination.selectedIcon ?? destination.icon 
                                  : destination.icon,
                              color: isSelected
                                  ? selectedIconColor ?? AppColors.primary
                                  : unselectedIconColor ?? AppColors.onSurfaceVariant,
                              size: 24,
                            ),
                            if (extended) ...[
                              const SizedBox(width: 12),
                              Expanded(
                                child: Text(
                                  destination.label,
                                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    color: isSelected
                                        ? selectedIconColor ?? AppColors.primary
                                        : unselectedIconColor ?? AppColors.onSurfaceVariant,
                                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                                  ),
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
          if (trailing != null) ...[
            Padding(
              padding: const EdgeInsets.all(16),
              child: trailing!,
            ),
          ],
        ],
      ),
    );
  }
}

/// Modern drawer with custom styling
class ModernDrawer extends StatelessWidget {
  final Widget? header;
  final List<ModernDrawerItem> items;
  final Color? backgroundColor;

  const ModernDrawer({
    Key? key,
    this.header,
    required this.items,
    this.backgroundColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Drawer(
      backgroundColor: backgroundColor ?? AppColors.surface,
      child: Column(
        children: [
          if (header != null) header!,
          Expanded(
            child: ListView.builder(
              padding: EdgeInsets.all(AppSize.spaceBetweenItems(context)),
              itemCount: items.length,
              itemBuilder: (context, index) {
                final item = items[index];
                
                if (item.isDivider) {
                  return Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    child: Divider(
                      color: AppColors.outline.withOpacity(0.2),
                    ),
                  );
                }

                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 2),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      borderRadius: BorderRadius.circular(AppSize.radiusMd),
                      onTap: item.onTap,
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 12,
                        ),
                        child: Row(
                          children: [
                            if (item.icon != null) ...[
                              Icon(
                                item.icon,
                                color: AppColors.onSurfaceVariant,
                                size: 24,
                              ),
                              const SizedBox(width: 16),
                            ],
                            Expanded(
                              child: Text(
                                item.title,
                                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                                  color: AppColors.onSurface,
                                ),
                              ),
                            ),
                            if (item.trailing != null) item.trailing!,
                          ],
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}

// Data classes for navigation components
class ModernBottomNavigationItem {
  final IconData icon;
  final IconData? selectedIcon;
  final String label;

  const ModernBottomNavigationItem({
    required this.icon,
    this.selectedIcon,
    required this.label,
  });
}

class ModernNavigationRailDestination {
  final IconData icon;
  final IconData? selectedIcon;
  final String label;

  const ModernNavigationRailDestination({
    required this.icon,
    this.selectedIcon,
    required this.label,
  });
}

class ModernDrawerItem {
  final String title;
  final IconData? icon;
  final VoidCallback? onTap;
  final Widget? trailing;
  final bool isDivider;

  const ModernDrawerItem({
    required this.title,
    this.icon,
    this.onTap,
    this.trailing,
    this.isDivider = false,
  });

  const ModernDrawerItem.divider()
      : title = '',
        icon = null,
        onTap = null,
        trailing = null,
        isDivider = true;
}
