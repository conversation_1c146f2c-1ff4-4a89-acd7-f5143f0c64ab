import 'package:NovaSchool/commun/parent_app/P_appDrawer.dart';
import 'package:NovaSchool/commun/parent_app/app_PnavBar.dart';
import 'package:NovaSchool/services/auth_service.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:NovaSchool/features/chat/test.dart';
import '../../../../commun/app_background.dart';
import '../../../../commun/parent_app/app_PheadBar.dart';
import '../../../chatbot/chatbot_screen.dart';
import 'pedagogicalElement/home_PpedagogicalElement.dart';
import 'home_Pwelcome.dart';
import '../../../utils/widgets/modern_navigation.dart';
import '../../../utils/widgets/modern_cards.dart';
import '../../../utils/widgets/responsive_layout.dart';
import '../../../utils/constants/colors.dart';
import '../../../utils/constants/size.dart';
import '../../../utils/device/devices_utility.dart';

class PHomeScreen extends StatelessWidget {
  const PHomeScreen({Key? key}) : super(key: key);

  // 🎨 Palette de couleurs modernes et raffinées
  static const Color primaryColor = Color(0xFF4099FF); // Bleu profond raffiné
  static const Color secondaryColor = Color(0xFFF1F1F1); // Blanc cassé élégant
  static const Color backgroundColor = Color(0xFFF2F2F2); // Gris clair doux
  static const Color textColor = Color(0xFF2D3A45); // Texte plus foncé

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: true,
      child: ResponsiveLayout(
        mobile: _buildMobileLayout(context),
        tablet: _buildTabletLayout(context),
        desktop: _buildDesktopLayout(context),
      ),
    );
  }

  Widget _buildMobileLayout(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      drawer: AppDrawer(),
      appBar: ModernAppBar(
        title: 'Accueil Parent',
        gradientColors: AppColors.primaryGradient,
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications_outlined),
            onPressed: () {
              // Navigate to notifications
            },
          ),
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: () async {
              await AuthService.logoutKeycloak();
            },
          ),
        ],
      ),
      body: _buildBody(context),
    );
  }

  Widget _buildTabletLayout(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: ModernAppBar(
        title: 'Accueil Parent',
        gradientColors: AppColors.primaryGradient,
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications_outlined),
            onPressed: () {
              // Navigate to notifications
            },
          ),
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: () async {
              await AuthService.logoutKeycloak();
            },
          ),
        ],
      ),
      body: Row(
        children: [
          // Navigation rail for tablet
          Container(
            width: 80,
            decoration: BoxDecoration(
              color: AppColors.surface,
              border: Border(
                right: BorderSide(
                  color: AppColors.outline.withOpacity(0.2),
                  width: 1,
                ),
              ),
            ),
            child: Column(
              children: [
                const SizedBox(height: 20),
                IconButton(
                  icon: const Icon(Icons.home),
                  onPressed: () {},
                  color: AppColors.primary,
                ),
                IconButton(
                  icon: const Icon(Icons.school),
                  onPressed: () {},
                  color: AppColors.onSurfaceVariant,
                ),
                IconButton(
                  icon: const Icon(Icons.assignment),
                  onPressed: () {},
                  color: AppColors.onSurfaceVariant,
                ),
                IconButton(
                  icon: const Icon(Icons.chat),
                  onPressed: () {},
                  color: AppColors.onSurfaceVariant,
                ),
              ],
            ),
          ),
          Expanded(child: _buildBody(context)),
        ],
      ),
    );
  }

  Widget _buildDesktopLayout(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: ModernAppBar(
        title: 'Accueil Parent - EcoCode School',
        gradientColors: AppColors.primaryGradient,
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              // Open search
            },
          ),
          IconButton(
            icon: const Icon(Icons.notifications_outlined),
            onPressed: () {
              // Navigate to notifications
            },
          ),
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: () async {
              await AuthService.logoutKeycloak();
            },
          ),
        ],
      ),
      body: Row(
        children: [
          // Extended navigation rail for desktop
          Container(
            width: 256,
            decoration: BoxDecoration(
              color: AppColors.surface,
              border: Border(
                right: BorderSide(
                  color: AppColors.outline.withOpacity(0.2),
                  width: 1,
                ),
              ),
            ),
            child: Column(
              children: [
                const SizedBox(height: 20),
                _buildNavItem(context, Icons.home, 'Accueil', true),
                _buildNavItem(context, Icons.school, 'Pédagogie', false),
                _buildNavItem(context, Icons.assignment, 'Devoirs', false),
                _buildNavItem(context, Icons.grade, 'Notes', false),
                _buildNavItem(context, Icons.schedule, 'Emploi du temps', false),
                _buildNavItem(context, Icons.chat, 'Messages', false),
                _buildNavItem(context, Icons.payment, 'Paiements', false),
                _buildNavItem(context, Icons.settings, 'Paramètres', false),
              ],
            ),
          ),
          Expanded(child: _buildBody(context)),
        ],
      ),
    );
  }

  Widget _buildNavItem(BuildContext context, IconData icon, String label, bool isSelected) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(AppSize.radiusMd),
          onTap: () {},
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: isSelected
                  ? AppColors.primary.withOpacity(0.1)
                  : Colors.transparent,
              borderRadius: BorderRadius.circular(AppSize.radiusMd),
            ),
            child: Row(
              children: [
                Icon(
                  icon,
                  color: isSelected ? AppColors.primary : AppColors.onSurfaceVariant,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    label,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: isSelected ? AppColors.primary : AppColors.onSurfaceVariant,
                      fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildBody(BuildContext context) {
    return ResponsiveContainer(
      child: SingleChildScrollView(
        padding: EdgeInsets.all(AppSize.defaultSpace(context)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildWelcomeSection(context),
            SizedBox(height: AppSize.spaceBetweenSections(context)),
            _buildQuickStatsSection(context),
            SizedBox(height: AppSize.spaceBetweenSections(context)),
            const PHomePedagogicalElement(),
          ],
        ),
      ),
    );
  }

  Widget _buildWelcomeSection(BuildContext context) {
    return ModernGlassCard(
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Bienvenue !',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.onSurface,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Suivez la scolarité de votre enfant en temps réel',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: AppColors.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: AppColors.primaryGradient,
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(AppSize.radiusLg),
            ),
            child: const Icon(
              Icons.school,
              color: AppColors.onPrimary,
              size: 32,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickStatsSection(BuildContext context) {
    return ResponsiveGridView(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      children: [
        ModernDashboardCard(
          title: 'Notes récentes',
          value: '16.5',
          icon: Icons.grade,
          iconColor: AppColors.success,
          subtitle: 'Moyenne générale',
        ),
        ModernDashboardCard(
          title: 'Absences',
          value: '2',
          icon: Icons.event_busy,
          iconColor: AppColors.warning,
          subtitle: 'Ce mois-ci',
        ),
        ModernDashboardCard(
          title: 'Devoirs',
          value: '5',
          icon: Icons.assignment,
          iconColor: AppColors.primary,
          subtitle: 'À rendre',
        ),
        ModernDashboardCard(
          title: 'Messages',
          value: '3',
          icon: Icons.message,
          iconColor: AppColors.secondary,
          subtitle: 'Non lus',
        ),
      ],
    );
  }
}
/****

    class PHomeScreen extends StatelessWidget {
    const PHomeScreen({Key? key}) : super(key: key);

    // 🎨 Palette de couleurs modernes et raffinées
    static const Color primaryColor = Color(0xFF4099FF); // Bleu profond raffiné
    static const Color secondaryColor = Color(0xFFF1F1F1); // Blanc cassé élégant
    static const Color backgroundColor = Color(0xFFF2F2F2); // Gris clair doux
    static const Color textColor = Color(0xFF2D3A45); // Texte plus foncé

    @override
    Widget build(BuildContext context) {
    return WillPopScope(
    onWillPop: () async {
    // Handle back press to exit app
    return true; // Let the system handle back navigation (exit app)
    },
    child: Scaffold(

    body: AppBackground(
    child: Column(
    children: [
    PrimaryHeaderContainer(
    child: Column(
    children: [
    PAppHeadBar(),
    PHomeWelcome(),
    SizedBox(height: 70),
    ],
    ),
    ),
    Expanded(
    child: SingleChildScrollView(
    child: Column(
    children: [
    PHomePedagogicalElement(),
    ],
    ),
    ),
    ),
    ],
    ),
    ),
    ),
    );
    }
    }

 */
/*

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async => true,

      child: Scaffold(
        backgroundColor: backgroundColor,
        /* drawer:  Drawer(
        backgroundColor: primaryColor, // Remplace par la couleur souhaitée
        child: AppDrawer(),
      ),*/

        appBar: PreferredSize(
          preferredSize: Size.fromHeight(kToolbarHeight),
          child: Container(
            //
            width: double.infinity,

            ///
            decoration: BoxDecoration(
              gradient: LinearGradient(colors: [Colors.blue, Colors.cyan]),
            ),
            child: AppBar(
              backgroundColor: Colors.transparent,
              // Pour afficher le gradient
              elevation: 0,
              centerTitle: true,
              // ✅ Assure le centrage du titre
              title: Image.asset(
                'assets/logos/ecocode.png', // Remplace avec ton chemin
                height: 50, // Ajuste la taille
                color: Colors
                    .white, // Applique du blanc au logo (si PNG avec transparence)
              ),
              actions: [
                IconButton(
                  icon: Icon(Icons.notifications, color: Colors.black),
                  // ✅ Ajout de l'icône de chat
                  onPressed: () {
                    //Navigator.push(
                    //context,MaterialPageRoute(builder: (context) => PChatScreen()),);
                  },
                ),
              ],
            ),
          ),
        ),

        body: SafeArea(
          child: SingleChildScrollView(
            physics: BouncingScrollPhysics(), // Effet de défilement fluide
            child: Column(
              children: [
                // Utilisation de `const` pour éviter des reconstructions inutiles
                PHomePedagogicalElement(),
                //SizedBox(height: 0),
                ParentAppNavBar(),
              ],
            ),
          ),
        ),

      ),

    );
  }
}*/
